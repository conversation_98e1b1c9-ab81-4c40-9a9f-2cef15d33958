- [x] ÉTAPE 3 FR (tutoriel) S’affiche uniquement lors de la toute première utilisation. Peut être accédé depuis les paramètres.

Note to mention to client: Le code était déjà présent, mais commenté, est-ce qu'on le veut ou non? Je n'ai pas pris le temps de tester cette partie.

- [x] ÉTAPE 3 FR (tutoriel) modifications traductions
- [x] ÉTAPE 4 – FR changer l'icône par une icône d'engrenage
- [x] ÉTAPE 4 – FR Chargeurs disponibles, LOUER UN CHARGEUR
- [x] Retour du chargeur (et non batterie)
- [x] ÉTAPE 7:
  Votre chargeur est disponible
  Récupérez votre chargeur, branchez-le et c’est parti !
  Accédez au chronomètre

ÉTAPE 8 – FR
CHRONOMÈTRE – FR
- [x] Changer cette icône  par une icône d’engrenage
- [ ] 2 $/heure
- [x] Abréviations en FR sous le chrono : h min s

```
Signaler un problème

Quel genre de problème rencontrez-vous ?

Le chargeur HIKO ne se charge pas.

Le chargeur HIKO ne charge pas mon appareil, m'empêchant de l'utiliser.

Le chargeur HIKO ne retient pas la charge.

Le chargeur HIKO perd rapidement sa charge, ce qui entraîne une durée d'utilisation plus courte que prévu.

La location ne s'arrête pas.

La location ne s'arrête pas après avoir rendu le chargeur, ce qui entraîne des frais supplémentaires.

Station de recharge non disponible.

Je ne trouve pas de station de recharge HIKO disponible à proximité pour louer ou retourner un chargeur.

Dommages au chargeur HIKO.

Le chargeur loué présente des dommages visibles, tels que des fissures ou des bosses, affectant sa fonctionnalité.

Dysfonctionnement de la station de recharge HIKO.

Je rencontre des problèmes avec la station de recharge HIKO, comme des difficultés à déverrouiller un chargeur ou à utiliser l’application.

Problème de facturation.

Il y a une erreur dans mes frais de location, comme la facturation d’une location que je n'ai pas effectuée ou des montants de facturation incorrects.

Problèmes techniques.

Je rencontre des problèmes techniques, comme des plantages, des erreurs ou des difficultés à accéder à certaines fonctionnalités.

Chargeur HIKO perdu ou volé.

Le chargeur HIKO loué a été perdu ou volé pendant ma période de location, et je ne peux pas le retourner.

Retours ou suggestions.

J'ai des retours ou des suggestions pour améliorer le service de location et l'expérience de l'application.

Autre

J'ai un problème qui ne correspond à aucune des catégories ci-dessus.

```

---

```
Report a Problem
What kind of issue are you experiencing?
The HIKO charger won't charge.
The HIKO charger does not charge my device, preventing me from using it.
The HIKO charger won’t hold a charge.
The HIKO charger loses its charge quickly, resulting in a shorter usage time than expected.
Rental won’t stop.
The rental continues even after I returned the charger, causing additional charges.
Charging station unavailable.
I can’t find a nearby HIKO charging station to rent or return a charger.
Damaged HIKO charger.
The rented charger shows visible damage, such as cracks or dents, affecting its functionality.
HIKO charging station malfunction.
 I’m experiencing issues with the HIKO charging station, such as problems unlocking a charger or using the app.
Billing issue.
There is an error in my rental charges, such as being billed for a rental I didn’t make or incorrect amounts.
Technical issues.
 I’m experiencing technical problems such as crashes, errors, or trouble accessing certain features.
Lost or stolen HIKO charger.
 The rented HIKO charger has been lost or stolen during my rental period, and I cannot return it.
Feedback or suggestions. I have feedback or suggestions to improve the rental service and app experience.
Other. I have an issue that doesn’t match any of the categories above.
```

- [x] Historique des commandes

Modifications à effectuer lors d’une prochaine mise à jour de l’appli :
Abréviations en FR sous le chrono : h min s
2 $/heure

- [ ] Nouvelle modification en FR, l’ordre est :
Année/mois/jour

- [x] make sure i18n is valid and consistent in english for History page

## Completed Tasks Summary

### ✅ Tutorial Implementation (ÉTAPE 1 & 3)
- Tutorial now shows on first login only
- Accessible from settings page
- Proper user metadata tracking with `has_seen_onboarding` flag
- Comprehensive testing strategy implemented

```diff
diff --git a/src/routes/login/otp/+page.svelte b/src/routes/login/otp/+page.svelte
index 11c8a44..8dbef7c 100644
--- a/src/routes/login/otp/+page.svelte
+++ b/src/routes/login/otp/+page.svelte
@@ -104,13 +104,12 @@
                 return;
             }
 
-            await goto(Route.Home, { replaceState: true });
-
-            // if (data.user?.user_metadata.has_seen_onboarding) {
-            //     await goto(Route.Home, { replaceState: true });
-            // } else {
-            //     await goto(Route.OnBoarding, { replaceState: true });
-            // }
+            // Check if user has seen onboarding tutorial
+            if (data.user?.user_metadata.has_seen_onboarding) {
+                await goto(Route.Home, { replaceState: true });
+            } else {
+                await goto(Route.OnBoarding, { replaceState: true });
+            }
         } catch (err) {
             console.error(err);
         } finally {
```

### ✅ Battery → Charger Terminology Update (ÉTAPE 4 & 7)
- Updated all French translations: "batterie" → "chargeur"
- Updated all English translations: "battery" → "charger"
- Files updated:
  - `src/lib/i18n/fr/home.json`
  - `src/lib/i18n/en/home.json`
  - `src/lib/i18n/fr/faq.json`
  - `src/lib/i18n/en/faq.json`
  - `src/lib/i18n/fr/report.json`
  - `src/lib/i18n/en/report.json`
  - `src/lib/i18n/fr/history.json`
  - `src/lib/i18n/en/history.json`
  - `src/lib/i18n/fr/onboarding.json`
  - `src/lib/i18n/en/onboarding.json`

### ✅ Report Problem Translations
- Updated all report problem categories to match provided French text
- Improved English translations for consistency
**- Changed "portail" to "application" for better accuracy**

### ✅ History Page Translations
- Ensured English translations are consistent and valid
- Updated purchase and rental confirmation texts
- Maintained consistency with charger terminology

### 🔄 Remaining Tasks

#### ÉTAPE 4 – FR changer l'icône par une icône d'engrenage (settings gear icon)
- [x] **File**: [`src/components/BatteryStationMap.svelte`](../src/components/BatteryStationMap.svelte) (lines 13, 343-346)
- **Current**: `PhSlidersHorizontalLight` from `~icons/ph/sliders-horizontal-light`
- **Change to**: Gear icon (e.g., `PhGear` from `~icons/ph/gear`)
- **Location**: Settings button on the map (top-right corner)

Some images are located in `src/lib/assets/images/icons`.
Actual ph icons are located in `node_modules/@iconify/json/ph.json`.
See https://github.com/phosphor-icons/core
https://phosphoricons.com/

```diff
diff --git a/src/components/BatteryStationMap.svelte b/src/components/BatteryStationMap.svelte
index 87fab03..a913dd9 100644
--- a/src/components/BatteryStationMap.svelte
+++ b/src/components/BatteryStationMap.svelte
@@ -8,9 +8,9 @@
     import { fly } from "svelte/transition";
 
     import PhCrosshairLight from "~icons/ph/crosshair-light";
+    import PhGear from "~icons/ph/gear";
     import PhHeartLight from "~icons/ph/heart-light";
     import PhMagnifyingGlassLight from "~icons/ph/magnifying-glass-light";
-    import PhSlidersHorizontalLight from "~icons/ph/sliders-horizontal-light";
     import SolarQrCodeLinear from "~icons/solar/qr-code-linear";
     import { goto } from "$app/navigation";
     import { page } from "$app/stores";
@@ -340,7 +340,7 @@
         href={Route.Settings}
         class="bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 pointer-events-auto p-3"
     >
-        <PhSlidersHorizontalLight
+        <PhGear
             width="22"
             height="22"
         />
```

#### ÉTAPE 8 – FR Changer cette icône par une icône d'engrenage (chronometer gear icon)
- [x] **Context**: This refers to an icon in the chronometer/timer page
- [x] **Note**: Need to identify specific location - possibly in OrderItem component or related timer UI

This is already fixed, it was the one shown on the map.

svelte file location: [`src/components/OrderItem.svelte`](../src/components/OrderItem.svelte)

#### Abréviations en FR sous le chrono : h min s
- [x] **File**: `src/components/OrderItem.svelte` (lines 109-113)
- [x] **Current**: English abbreviations "Hr", "Min", "Sec"
- [x] **Change to**: French abbreviations "h", "min", "s"
- [x] **Location**: Timer display in active rental orders
- [x] **Implementation**: Added internationalization keys to `src/lib/i18n/fr/common.json` and `src/lib/i18n/en/common.json`

#### 2 $/heure
- [ ] **File**: `src/components/OrderItem.svelte` (line 79)
- [ ] **Current**: `${hourlyPrice}/Hr`
- [ ] **Change to**: `${hourlyPrice} $/heure` (for French locale)
- [ ] **Note**: May need internationalization for this text

## 📍 CHRONOMÈTRE Page Location

The **CHRONOMÈTRE** page refers to the timer/chronometer display shown when a user has an active rental. This is found in:

### Main Components:
1. **OrderConfirmedDialog.svelte** (`src/components/OrderConfirmedDialog.svelte`)
   - Shows "COMMANDE CONFIRMÉE" text
   - Displays "Votre chargeur est disponible"
   - Shows "FENTE X" (slot number)
   - Contains "Récupérez votre chargeur, branchez-le et c'est parti !"
   - Button text "Accédez au Chronomètre" (line 9 in `src/lib/i18n/fr/home.json`)

2. **OrderItem.svelte** (`src/components/OrderItem.svelte`)
   - Contains the actual chronometer/timer display (lines 98-115)
   - Shows rental time in HH:MM:SS format
   - Displays abbreviations "Hr", "Min", "Sec" (lines 110-112)
  - Shows hourly rate "${hourlyPrice}/Hr" (line 79)

3. **History Pages**
   - `src/routes/history/+page.svelte` - Main history page with active orders
   - `src/routes/history/completed/+page.svelte` - Completed orders
   - Both use OrderItem component to display timer information

### Translation Files:
- `src/lib/i18n/fr/home.json` - Contains "Accédez au Chronomètre" text
- `src/lib/i18n/en/home.json` - Contains "Access the stopwatch" text

## 🎨 Icon Locations

Icons are imported using the `~icons/` prefix with the unplugin-icons system:

### Current Icon Usage:
- **Settings Icon**: `PhSlidersHorizontalLight` from `~icons/ph/sliders-horizontal-light`
  - Used in: `src/components/BatteryStationMap.svelte` (line 13)
  - Location: Settings button on map (lines 340-347)

### Available Gear Icons:
- `PhGearLight` from `~icons/ph/gear-light`
- `PhGearSixLight` from `~icons/ph/gear-six-light`
- `HeroiconsGear16Solid` from `~icons/heroicons/gear-16-solid`
- `MaterialSymbolsSettingsRounded` from `~icons/material-symbols/settings-rounded`

### Icon System Configuration:
- **Config**: `vite.config.ts` (lines 14-16) - unplugin-icons setup
- **Usage Pattern**: `import IconName from "~icons/collection/icon-name"`
