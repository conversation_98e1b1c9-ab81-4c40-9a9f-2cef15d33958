{"permissions": {"allow": ["Bash(find:*)", "WebSearch", "WebSearch", "WebSearch", "<PERSON><PERSON>(mkdir -p analysis)", "Bash(npm run test:*)", "<PERSON><PERSON>(node test-runner.js)", "Bash(npx vitest run:*)", "Bash(npx vitest run:*)", "Bash(rm:*)", "Bash(npm test)", "Bash(npm run lint)", "Bash(npx eslint:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(act:*)", "Bash(npm install:*)", "Bash(grep:*)", "Bash(npm uninstall:*)", "Bash(npx wrangler pages dev:*)", "<PERSON><PERSON>(npx wrangler:*)", "WebFetch(domain:community.cloudflare.com)", "WebFetch(domain:github.com)"], "deny": [], "ask": []}}