import{S as oc,i as lc,b as Wt,f as nr,t as Vt,g as rr,c as Ie,a as ui,m as xe,d as Ce,_ as Z}from"../chunks/index.RK-K-o1D.js";import{p as _a,n as ya}from"../chunks/stores.sj_Ool2X.js";import{e as cc}from"../chunks/public.1B-Uau7n.js";import{s as uc,l as dc,p as Yt,m as pc,i as le,d as Kt,R as hc,o as mc,e as fc,c as gc,a as _c,b as Tr,S as Ct,t as yc,g as vc,j as Sc,k as wr,P as Re,T as bc}from"../chunks/index.UaHqEmIZ.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="2bd67cd9-9390-4a68-97bd-055bbc008591",t._sentryDebugIdIdentifier="sentry-dbid-2bd67cd9-9390-4a68-97bd-055bbc008591")}catch{}})();var kc=Object.defineProperty,va=(t,e)=>{for(var n in e)kc(t,n,{get:e[n],enumerable:!0})},Sa=Object.prototype.toString;function ba(t){switch(Sa.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Nt(t,Error)}}function je(t,e){return Sa.call(t)===`[object ${e}]`}function di(t){return je(t,"ErrorEvent")}function Xi(t){return je(t,"DOMError")}function Tc(t){return je(t,"DOMException")}function It(t){return je(t,"String")}function pi(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function hi(t){return t===null||pi(t)||typeof t!="object"&&typeof t!="function"}function Me(t){return je(t,"Object")}function ir(t){return typeof Event<"u"&&Nt(t,Event)}function wc(t){return typeof Element<"u"&&Nt(t,Element)}function Ec(t){return je(t,"RegExp")}function sr(t){return!!(t&&t.then&&typeof t.then=="function")}function Ic(t){return Me(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function ka(t){return typeof t=="number"&&t!==t}function Nt(t,e){try{return t instanceof e}catch{return!1}}function Ta(t){return!!(typeof t=="object"&&t!==null&&(t.__isVue||t._isVue))}function Te(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function Qi(t,e){if(!Array.isArray(t))return"";let n=[];for(let r=0;r<t.length;r++){let i=t[r];try{Ta(i)?n.push("[VueViewModel]"):n.push(String(i))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function xc(t,e,n=!1){return It(t)?Ec(e)?e.test(t):It(e)?n?t===e:t.includes(e):!1:!1}function Ue(t,e=[],n=!1){return e.some(r=>xc(t,r,n))}function wa(t,e,n=250,r,i,s,a){if(!s.exception||!s.exception.values||!a||!Nt(a.originalException,Error))return;let o=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;o&&(s.exception.values=Cc(Pr(t,e,i,a.originalException,r,s.exception.values,o,0),n))}function Pr(t,e,n,r,i,s,a,o){if(s.length>=n+1)return s;let l=[...s];if(Nt(r[i],Error)){Zi(a,o);let c=t(e,r[i]),d=l.length;ts(c,i,d,o),l=Pr(t,e,n,r[i],i,[c,...l],c,d)}return Array.isArray(r.errors)&&r.errors.forEach((c,d)=>{if(Nt(c,Error)){Zi(a,o);let u=t(e,c),p=l.length;ts(u,`errors[${d}]`,p,o),l=Pr(t,e,n,c,i,[u,...l],u,p)}}),l}function Zi(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function ts(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Cc(t,e){return t.map(n=>(n.value&&(n.value=Te(n.value,e)),n))}function Tn(t){return t&&t.Math==Math?t:void 0}var A=typeof globalThis=="object"&&Tn(globalThis)||typeof window=="object"&&Tn(window)||typeof self=="object"&&Tn(self)||typeof global=="object"&&Tn(global)||function(){return this}()||{};function mi(){return A}function Ea(t,e,n){let r=n||A,i=r.__SENTRY__=r.__SENTRY__||{};return i[t]||(i[t]=e())}var we=mi(),Rc=80;function Jt(t,e={}){if(!t)return"<unknown>";try{let n=t,r=5,i=[],s=0,a=0,o=" > ",l=o.length,c,d=Array.isArray(e)?e:e.keyAttrs,u=!Array.isArray(e)&&e.maxStringLength||Rc;for(;n&&s++<r&&(c=Mc(n,d),!(c==="html"||s>1&&a+i.length*l+c.length>=u));)i.push(c),a+=c.length,n=n.parentNode;return i.reverse().join(o)}catch{return"<unknown>"}}function Mc(t,e){let n=t,r=[],i,s,a,o,l;if(!n||!n.tagName)return"";if(we.HTMLElement&&n instanceof HTMLElement&&n.dataset&&n.dataset.sentryComponent)return n.dataset.sentryComponent;r.push(n.tagName.toLowerCase());let c=e&&e.length?e.filter(u=>n.getAttribute(u)).map(u=>[u,n.getAttribute(u)]):null;if(c&&c.length)c.forEach(u=>{r.push(`[${u[0]}="${u[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&It(i))for(s=i.split(/\s+/),l=0;l<s.length;l++)r.push(`.${s[l]}`);let d=["aria-label","type","name","title","alt"];for(l=0;l<d.length;l++)a=d[l],o=n.getAttribute(a),o&&r.push(`[${a}="${o}"]`);return r.join("")}function Oc(){try{return we.document.location.href}catch{return""}}function fi(t){return we.document&&we.document.querySelector?we.document.querySelector(t):null}function Ia(t){if(!we.HTMLElement)return null;let e=t,n=5;for(let r=0;r<n;r++){if(!e)return null;if(e instanceof HTMLElement&&e.dataset.sentryComponent)return e.dataset.sentryComponent;e=e.parentNode}return null}var Be=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ac="Sentry Logger ",$r=["debug","info","warn","error","log","assert","trace"],$n={};function Gt(t){if(!("console"in A))return t();let e=A.console,n={},r=Object.keys($n);r.forEach(i=>{let s=$n[i];n[i]=e[i],e[i]=s});try{return t()}finally{r.forEach(i=>{e[i]=n[i]})}}function Dc(){let t=!1,e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return Be?$r.forEach(n=>{e[n]=(...r)=>{t&&Gt(()=>{A.console[n](`${Ac}[${n}]:`,...r)})}}):$r.forEach(n=>{e[n]=()=>{}}),e}var f=Dc(),Nc=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Lc(t){return t==="http"||t==="https"}function He(t,e=!1){let{host:n,path:r,pass:i,port:s,projectId:a,protocol:o,publicKey:l}=t;return`${o}://${l}${e&&i?`:${i}`:""}@${n}${s?`:${s}`:""}/${r&&`${r}/`}${a}`}function Pc(t){let e=Nc.exec(t);if(!e){Gt(()=>{console.error(`Invalid Sentry Dsn: ${t}`)});return}let[n,r,i="",s,a="",o]=e.slice(1),l="",c=o,d=c.split("/");if(d.length>1&&(l=d.slice(0,-1).join("/"),c=d.pop()),c){let u=c.match(/^\d+/);u&&(c=u[0])}return xa({host:s,pass:i,path:l,projectId:c,port:a,protocol:n,publicKey:r})}function xa(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function $c(t){if(!Be)return!0;let{port:e,projectId:n,protocol:r}=t;return["protocol","publicKey","host","projectId"].find(i=>t[i]?!1:(f.error(`Invalid Sentry Dsn: ${i} missing`),!0))?!1:n.match(/^\d+$/)?Lc(r)?e&&isNaN(parseInt(e,10))?(f.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):!0:(f.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(f.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function Fc(t){let e=typeof t=="string"?Pc(t):xa(t);if(!(!e||!$c(e)))return e}var wt=class extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}};function nt(t,e,n){if(!(e in t))return;let r=t[e],i=n(r);typeof i=="function"&&Ca(i,r),t[e]=i}function ce(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{Be&&f.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function Ca(t,e){try{let n=e.prototype||{};t.prototype=e.prototype=n,ce(t,"__sentry_original__",e)}catch{}}function gi(t){return t.__sentry_original__}function jc(t){return Object.keys(t).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`).join("&")}function Ra(t){if(ba(t))return{message:t.message,name:t.name,stack:t.stack,...ns(t)};if(ir(t)){let e={type:t.type,target:es(t.target),currentTarget:es(t.currentTarget),...ns(t)};return typeof CustomEvent<"u"&&Nt(t,CustomEvent)&&(e.detail=t.detail),e}else return t}function es(t){try{return wc(t)?Jt(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function ns(t){if(typeof t=="object"&&t!==null){let e={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}else return{}}function Uc(t,e=40){let n=Object.keys(Ra(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return Te(n[0],e);for(let r=n.length;r>0;r--){let i=n.slice(0,r).join(", ");if(!(i.length>e))return r===n.length?i:Te(i,e)}return""}function ot(t){return Fr(t,new Map)}function Fr(t,e){if(Bc(t)){let n=e.get(t);if(n!==void 0)return n;let r={};e.set(t,r);for(let i of Object.keys(t))typeof t[i]<"u"&&(r[i]=Fr(t[i],e));return r}if(Array.isArray(t)){let n=e.get(t);if(n!==void 0)return n;let r=[];return e.set(t,r),t.forEach(i=>{r.push(Fr(i,e))}),r}return t}function Bc(t){if(!Me(t))return!1;try{let e=Object.getPrototypeOf(t).constructor.name;return!e||e==="Object"}catch{return!0}}var Ma=50,rs=/\(error: (.*)\)/,is=/captureMessage|captureException/;function Oa(...t){let e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0)=>{let i=[],s=n.split(`
`);for(let a=r;a<s.length;a++){let o=s[a];if(o.length>1024)continue;let l=rs.test(o)?o.replace(rs,"$1"):o;if(!l.match(/\S*Error: /)){for(let c of e){let d=c(l);if(d){i.push(d);break}}if(i.length>=Ma)break}}return zc(i)}}function Hc(t){return Array.isArray(t)?Oa(...t):t}function zc(t){if(!t.length)return[];let e=Array.from(t);return/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop(),e.reverse(),is.test(e[e.length-1].function||"")&&(e.pop(),is.test(e[e.length-1].function||"")&&e.pop()),e.slice(0,Ma).map(n=>({...n,filename:n.filename||e[e.length-1].filename,function:n.function||"?"}))}var Er="<anonymous>";function Lt(t){try{return!t||typeof t!="function"?Er:t.name||Er}catch{return Er}}var Ln={},ss={};function de(t,e){Ln[t]=Ln[t]||[],Ln[t].push(e)}function pe(t,e){ss[t]||(e(),ss[t]=!0)}function vt(t,e){let n=t&&Ln[t];if(n)for(let r of n)try{r(e)}catch(i){Be&&f.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${Lt(r)}
Error:`,i)}}function qc(t){let e="console";de(e,t),pe(e,Wc)}function Wc(){"console"in A&&$r.forEach(function(t){t in A.console&&nt(A.console,t,function(e){return $n[t]=e,function(...n){vt("console",{args:n,level:t});let r=$n[t];r&&r.apply(A.console,n)}})})}function J(){let t=A,e=t.crypto||t.msCrypto,n=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{let r=new Uint8Array(1);return e.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function Aa(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function Bt(t){let{message:e,event_id:n}=t;if(e)return e;let r=Aa(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function jr(t,e,n){let r=t.exception=t.exception||{},i=r.values=r.values||[],s=i[0]=i[0]||{};s.value||(s.value=e||""),s.type||(s.type=n||"Error")}function sn(t,e){let n=Aa(t);if(!n)return;let r={type:"generic",handled:!0},i=n.mechanism;if(n.mechanism={...r,...i,...e},e&&"data"in e){let s={...i&&i.data,...e.data};n.mechanism.data=s}}function as(t){if(t&&t.__sentry_captured__)return!0;try{ce(t,"__sentry_captured__",!0)}catch{}return!1}function Da(t){return Array.isArray(t)?t:[t]}var _e=A,Vc=1e3,os,Ur,Br;function Na(t){let e="dom";de(e,t),pe(e,Yc)}function Yc(){if(!_e.document)return;let t=vt.bind(null,"dom"),e=ls(t,!0);_e.document.addEventListener("click",e,!1),_e.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{let r=_e[n]&&_e[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(nt(r,"addEventListener",function(i){return function(s,a,o){if(s==="click"||s=="keypress")try{let l=this,c=l.__sentry_instrumentation_handlers__=l.__sentry_instrumentation_handlers__||{},d=c[s]=c[s]||{refCount:0};if(!d.handler){let u=ls(t);d.handler=u,i.call(this,s,u,o)}d.refCount++}catch{}return i.call(this,s,a,o)}}),nt(r,"removeEventListener",function(i){return function(s,a,o){if(s==="click"||s=="keypress")try{let l=this,c=l.__sentry_instrumentation_handlers__||{},d=c[s];d&&(d.refCount--,d.refCount<=0&&(i.call(this,s,d.handler,o),d.handler=void 0,delete c[s]),Object.keys(c).length===0&&delete l.__sentry_instrumentation_handlers__)}catch{}return i.call(this,s,a,o)}}))})}function Kc(t){if(t.type!==Ur)return!1;try{if(!t.target||t.target._sentryId!==Br)return!1}catch{}return!0}function Jc(t,e){return t!=="keypress"?!1:!e||!e.tagName?!0:!(e.tagName==="INPUT"||e.tagName==="TEXTAREA"||e.isContentEditable)}function ls(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;let r=Gc(n);if(Jc(n.type,r))return;ce(n,"_sentryCaptured",!0),r&&!r._sentryId&&ce(r,"_sentryId",J());let i=n.type==="keypress"?"input":n.type;Kc(n)||(t({event:n,name:i,global:e}),Ur=n.type,Br=r?r._sentryId:void 0),clearTimeout(os),os=_e.setTimeout(()=>{Br=void 0,Ur=void 0},Vc)}}function Gc(t){try{return t.target}catch{return null}}var Hr=mi();function La(){if(!("fetch"in Hr))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function zr(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Xc(){if(typeof EdgeRuntime=="string")return!0;if(!La())return!1;if(zr(Hr.fetch))return!0;let t=!1,e=Hr.document;if(e&&typeof e.createElement=="function")try{let n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=zr(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){Be&&f.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}function _i(t){let e="fetch";de(e,t),pe(e,Qc)}function Qc(){Xc()&&nt(A,"fetch",function(t){return function(...e){let{method:n,url:r}=Zc(e),i={args:e,fetchData:{method:n,url:r},startTimestamp:Date.now()};return vt("fetch",{...i}),t.apply(A,e).then(s=>{let a={...i,endTimestamp:Date.now(),response:s};return vt("fetch",a),s},s=>{let a={...i,endTimestamp:Date.now(),error:s};throw vt("fetch",a),s})}})}function qr(t,e){return!!t&&typeof t=="object"&&!!t[e]}function cs(t){return typeof t=="string"?t:t?qr(t,"url")?t.url:t.toString?t.toString():"":""}function Zc(t){if(t.length===0)return{method:"GET",url:""};if(t.length===2){let[n,r]=t;return{url:cs(n),method:qr(r,"method")?String(r.method).toUpperCase():"GET"}}let e=t[0];return{url:cs(e),method:qr(e,"method")?String(e.method).toUpperCase():"GET"}}var wn=null;function Pa(t){let e="error";de(e,t),pe(e,tu)}function tu(){wn=A.onerror,A.onerror=function(t,e,n,r,i){return vt("error",{column:r,error:i,line:n,msg:t,url:e}),wn&&!wn.__SENTRY_LOADER__?wn.apply(this,arguments):!1},A.onerror.__SENTRY_INSTRUMENTED__=!0}var En=null;function $a(t){let e="unhandledrejection";de(e,t),pe(e,eu)}function eu(){En=A.onunhandledrejection,A.onunhandledrejection=function(t){return vt("unhandledrejection",t),En&&!En.__SENTRY_LOADER__?En.apply(this,arguments):!0},A.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}var In=mi();function nu(){let t=In.chrome,e=t&&t.app&&t.app.runtime,n="history"in In&&!!In.history.pushState&&!!In.history.replaceState;return!e&&n}var Ke=A,xn;function dn(t){let e="history";de(e,t),pe(e,ru)}function ru(){if(!nu())return;let t=Ke.onpopstate;Ke.onpopstate=function(...n){let r=Ke.location.href,i=xn;if(xn=r,vt("history",{from:i,to:r}),t)try{return t.apply(this,n)}catch{}};function e(n){return function(...r){let i=r.length>2?r[2]:void 0;if(i){let s=xn,a=String(i);xn=a,vt("history",{from:s,to:a})}return n.apply(this,r)}}nt(Ke.history,"pushState",e),nt(Ke.history,"replaceState",e)}var iu=A,Ht="__sentry_xhr_v3__";function yi(t){let e="xhr";de(e,t),pe(e,su)}function su(){if(!iu.XMLHttpRequest)return;let t=XMLHttpRequest.prototype;nt(t,"open",function(e){return function(...n){let r=Date.now(),i=It(n[0])?n[0].toUpperCase():void 0,s=au(n[1]);if(!i||!s)return e.apply(this,n);this[Ht]={method:i,url:s,request_headers:{}},i==="POST"&&s.match(/sentry_key/)&&(this.__sentry_own_request__=!0);let a=()=>{let o=this[Ht];if(o&&this.readyState===4){try{o.status_code=this.status}catch{}let l={args:[i,s],endTimestamp:Date.now(),startTimestamp:r,xhr:this};vt("xhr",l)}};return"onreadystatechange"in this&&typeof this.onreadystatechange=="function"?nt(this,"onreadystatechange",function(o){return function(...l){return a(),o.apply(this,l)}}):this.addEventListener("readystatechange",a),nt(this,"setRequestHeader",function(o){return function(...l){let[c,d]=l,u=this[Ht];return u&&It(c)&&It(d)&&(u.request_headers[c.toLowerCase()]=d),o.apply(this,l)}}),e.apply(this,n)}}),nt(t,"send",function(e){return function(...n){let r=this[Ht];if(!r)return e.apply(this,n);n[0]!==void 0&&(r.body=n[0]);let i={args:[r.method,r.url],startTimestamp:Date.now(),xhr:this};return vt("xhr",i),e.apply(this,n)}})}function au(t){if(It(t))return t;try{return t.toString()}catch{}}function ou(){return typeof __SENTRY_BROWSER_BUNDLE__<"u"&&!!__SENTRY_BROWSER_BUNDLE__}function lu(){return"npm"}function cu(){return!ou()&&Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]"}function us(){return typeof window<"u"&&(!cu()||uu())}function uu(){return A.process!==void 0&&A.process.type==="renderer"}function du(){let t=typeof WeakSet=="function",e=t?new WeakSet:[];function n(i){if(t)return e.has(i)?!0:(e.add(i),!1);for(let s=0;s<e.length;s++)if(e[s]===i)return!0;return e.push(i),!1}function r(i){if(t)e.delete(i);else for(let s=0;s<e.length;s++)if(e[s]===i){e.splice(s,1);break}}return[n,r]}function kt(t,e=100,n=1/0){try{return Wr("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Fa(t,e=3,n=100*1024){let r=kt(t,e);return fu(r)>n?Fa(t,e-1,n):r}function Wr(t,e,n=1/0,r=1/0,i=du()){let[s,a]=i;if(e==null||["number","boolean","string"].includes(typeof e)&&!ka(e))return e;let o=pu(t,e);if(!o.startsWith("[object "))return o;if(e.__sentry_skip_normalization__)return e;let l=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(l===0)return o.replace("object ","");if(s(e))return"[Circular ~]";let c=e;if(c&&typeof c.toJSON=="function")try{let h=c.toJSON();return Wr("",h,l-1,r,i)}catch{}let d=Array.isArray(e)?[]:{},u=0,p=Ra(e);for(let h in p){if(!Object.prototype.hasOwnProperty.call(p,h))continue;if(u>=r){d[h]="[MaxProperties ~]";break}let m=p[h];d[h]=Wr(h,m,l-1,r,i),u++}return a(e),d}function pu(t,e){try{if(t==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(t==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(Ta(e))return"[VueViewModel]";if(Ic(e))return"[SyntheticEvent]";if(typeof e=="number"&&e!==e)return"[NaN]";if(typeof e=="function")return`[Function: ${Lt(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;let n=hu(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function hu(t){let e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}function mu(t){return~-encodeURI(t).split(/%..|./).length}function fu(t){return mu(JSON.stringify(t))}var Mt;(function(t){t[t.PENDING=0]="PENDING";let e=1;t[t.RESOLVED=e]="RESOLVED";let n=2;t[t.REJECTED=n]="REJECTED"})(Mt||(Mt={}));function Oe(t){return new ze(e=>{e(t)})}function vi(t){return new ze((e,n)=>{n(t)})}var ze=class ie{constructor(e){ie.prototype.__init.call(this),ie.prototype.__init2.call(this),ie.prototype.__init3.call(this),ie.prototype.__init4.call(this),this._state=Mt.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(n){this._reject(n)}}then(e,n){return new ie((r,i)=>{this._handlers.push([!1,s=>{if(!e)r(s);else try{r(e(s))}catch(a){i(a)}},s=>{if(!n)i(s);else try{r(n(s))}catch(a){i(a)}}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new ie((n,r)=>{let i,s;return this.then(a=>{s=!1,i=a,e&&e()},a=>{s=!0,i=a,e&&e()}).then(()=>{if(s){r(i);return}n(i)})})}__init(){this._resolve=e=>{this._setResult(Mt.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(Mt.REJECTED,e)}}__init3(){this._setResult=(e,n)=>{if(this._state===Mt.PENDING){if(sr(n)){n.then(this._resolve,this._reject);return}this._state=e,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===Mt.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===Mt.RESOLVED&&n[1](this._value),this._state===Mt.REJECTED&&n[2](this._value),n[0]=!0)})}}};function gu(t){let e=[];function n(){return t===void 0||e.length<t}function r(a){return e.splice(e.indexOf(a),1)[0]}function i(a){if(!n())return vi(new wt("Not adding Promise because buffer limit was reached."));let o=a();return e.indexOf(o)===-1&&e.push(o),o.then(()=>r(o)).then(null,()=>r(o).then(null,()=>{})),o}function s(a){return new ze((o,l)=>{let c=e.length;if(!c)return o(!0);let d=setTimeout(()=>{a&&a>0&&o(!1)},a);e.forEach(u=>{Oe(u).then(()=>{--c||(clearTimeout(d),o(!0))},l)})})}return{$:e,add:i,drain:s}}function oe(t){if(!t)return{};let e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};let n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}var _u=["fatal","error","warning","log","info","debug"];function yu(t){return t==="warn"?"warning":_u.includes(t)?t:"log"}var ja=1e3;function pn(){return Date.now()/ja}function vu(){let{performance:t}=A;if(!t||!t.now)return pn;let e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/ja}var hn=vu(),lt=(()=>{let{performance:t}=A;if(!t||!t.now)return;let e=3600*1e3,n=t.now(),r=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,s=i<e,a=t.timing&&t.timing.navigationStart,o=typeof a=="number"?Math.abs(a+n-r):e,l=o<e;return s||l?i<=o?t.timeOrigin:a:r})(),Vr="baggage",Ua="sentry-",Su=/^sentry-/,bu=8192;function ku(t){if(!It(t)&&!Array.isArray(t))return;let e={};if(Array.isArray(t))e=t.reduce((r,i)=>{let s=ds(i);for(let a of Object.keys(s))r[a]=s[a];return r},{});else{if(!t)return;e=ds(t)}let n=Object.entries(e).reduce((r,[i,s])=>{if(i.match(Su)){let a=i.slice(Ua.length);r[a]=s}return r},{});if(Object.keys(n).length>0)return n}function Ba(t){if(!t)return;let e=Object.entries(t).reduce((n,[r,i])=>(i&&(n[`${Ua}${r}`]=i),n),{});return Tu(e)}function ds(t){return t.split(",").map(e=>e.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((e,[n,r])=>(e[n]=r,e),{})}function Tu(t){if(Object.keys(t).length!==0)return Object.entries(t).reduce((e,[n,r],i)=>{let s=`${encodeURIComponent(n)}=${encodeURIComponent(r)}`,a=i===0?s:`${e},${s}`;return a.length>bu?(Be&&f.warn(`Not adding key: ${n} with val: ${r} to baggage header due to exceeding baggage size limits.`),e):a},"")}var wu=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Eu(t){if(!t)return;let e=t.match(wu);if(!e)return;let n;return e[3]==="1"?n=!0:e[3]==="0"&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}function Ha(t,e){let n=Eu(t),r=ku(e),{traceId:i,parentSpanId:s,parentSampled:a}=n||{};return n?{traceId:i||J(),parentSpanId:s||J().substring(16),spanId:J().substring(16),sampled:a,dsc:r||{}}:{traceId:i||J(),spanId:J().substring(16)}}function Si(t=J(),e=J().substring(16),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}function Zt(t,e=[]){return[t,e]}function Iu(t,e){let[n,r]=t;return[n,[...r,e]]}function ps(t,e){let n=t[1];for(let r of n){let i=r[0].type;if(e(r,i))return!0}return!1}function Yr(t,e){return(e||new TextEncoder).encode(t)}function xu(t,e){let[n,r]=t,i=JSON.stringify(n);function s(a){typeof i=="string"?i=typeof a=="string"?i+a:[Yr(i,e),a]:i.push(typeof a=="string"?Yr(a,e):a)}for(let a of r){let[o,l]=a;if(s(`
${JSON.stringify(o)}
`),typeof l=="string"||l instanceof Uint8Array)s(l);else{let c;try{c=JSON.stringify(l)}catch{c=JSON.stringify(kt(l))}s(c)}}return typeof i=="string"?i:Cu(i)}function Cu(t){let e=t.reduce((i,s)=>i+s.length,0),n=new Uint8Array(e),r=0;for(let i of t)n.set(i,r),r+=i.length;return n}function Ru(t,e){let n=typeof t.data=="string"?Yr(t.data,e):t.data;return[ot({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}var Mu={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function hs(t){return Mu[t]}function bi(t){if(!t||!t.sdk)return;let{name:e,version:n}=t.sdk;return{name:e,version:n}}function za(t,e,n,r){let i=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:He(r)},...i&&{trace:ot({...i})}}}function Ou(t,e,n){let r=[{type:"client_report"},{timestamp:n||pn(),discarded_events:t}];return Zt(e?{dsn:e}:{},[r])}function Au(t,e=Date.now()){let n=parseInt(`${t}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${t}`);return isNaN(r)?6e4:r-e}function Du(t,e){return t[e]||t.all||0}function qa(t,e,n=Date.now()){return Du(t,e)>n}function Wa(t,{statusCode:e,headers:n},r=Date.now()){let i={...t},s=n&&n["x-sentry-rate-limits"],a=n&&n["retry-after"];if(s)for(let o of s.trim().split(",")){let[l,c,,,d]=o.split(":",5),u=parseInt(l,10),p=(isNaN(u)?60:u)*1e3;if(!c)i.all=r+p;else for(let h of c.split(";"))h==="metric_bucket"?(!d||d.split(";").includes("custom"))&&(i[h]=r+p):i[h]=r+p}else a?i.all=r+Au(a,r):e===429&&(i.all=r+60*1e3);return i}function Nu(t,e){return t(e.stack||"",1)}function Lu(t,e){let n={type:e.name||e.constructor.name,value:e.message},r=Nu(t,e);return r.length&&(n.stacktrace={frames:r}),n}function Pu(t,e){return t??e()}function Ir(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}var T=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,ar="production";function ki(){return Ea("globalEventProcessors",()=>[])}function $u(t){ki().push(t)}function Fn(t,e,n,r=0){return new ze((i,s)=>{let a=t[r];if(e===null||typeof a!="function")i(e);else{let o=a({...e},n);T&&a.id&&o===null&&f.log(`Event processor "${a.id}" dropped event`),sr(o)?o.then(l=>Fn(t,l,n,r+1).then(i)).then(null,s):Fn(t,o,n,r+1).then(i).then(null,s)}})}function Va(t){let e=hn(),n={sid:J(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>Fu(n)};return t&&ue(n,t),n}function ue(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||hn(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:J()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{let n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function Ya(t,e){let n={};e?n={status:e}:t.status==="ok"&&(n={status:"exited"}),ue(t,n)}function Fu(t){return ot({sid:`${t.sid}`,init:t.init,started:new Date(t.started*1e3).toISOString(),timestamp:new Date(t.timestamp*1e3).toISOString(),status:t.status,errors:t.errors,did:typeof t.did=="number"||typeof t.did=="string"?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}var ju=0,Ka=1;function Ti(t){let{spanId:e,traceId:n}=t.spanContext(),{data:r,op:i,parent_span_id:s,status:a,tags:o,origin:l}=X(t);return ot({data:r,op:i,parent_span_id:s,span_id:e,status:a,tags:o,trace_id:n,origin:l})}function or(t){let{traceId:e,spanId:n}=t.spanContext(),r=wi(t);return Si(e,n,r)}function lr(t){return typeof t=="number"?ms(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?ms(t.getTime()):hn()}function ms(t){return t>9999999999?t/1e3:t}function X(t){return Uu(t)?t.getSpanJSON():typeof t.toJSON=="function"?t.toJSON():{}}function Uu(t){return typeof t.getSpanJSON=="function"}function wi(t){let{traceFlags:e}=t.spanContext();return!!(e&Ka)}function Ja(t,e,n,r,i,s){let{normalizeDepth:a=3,normalizeMaxBreadth:o=1e3}=t,l={...e,event_id:e.event_id||n.event_id||J(),timestamp:e.timestamp||pn()},c=n.integrations||t.integrations.map(g=>g.name);Bu(l,t),qu(l,c),e.type===void 0&&Hu(l,t.stackParser);let d=Vu(r,n.captureContext);n.mechanism&&sn(l,n.mechanism);let u=i&&i.getEventProcessors?i.getEventProcessors():[],p=sd().getScopeData();if(s){let g=s.getScopeData();ys(p,g)}if(d){let g=d.getScopeData();ys(p,g)}let h=[...n.attachments||[],...p.attachments];h.length&&(n.attachments=h),Za(l,p);let m=[...u,...ki(),...p.eventProcessors];return Fn(m,l,n).then(g=>(g&&zu(g),typeof a=="number"&&a>0?Wu(g,a,o):g))}function Bu(t,e){let{environment:n,release:r,dist:i,maxValueLength:s=250}=e;"environment"in t||(t.environment="environment"in e?n:ar),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&i!==void 0&&(t.dist=i),t.message&&(t.message=Te(t.message,s));let a=t.exception&&t.exception.values&&t.exception.values[0];a&&a.value&&(a.value=Te(a.value,s));let o=t.request;o&&o.url&&(o.url=Te(o.url,s))}var fs=new WeakMap;function Hu(t,e){let n=A._sentryDebugIds;if(!n)return;let r,i=fs.get(e);i?r=i:(r=new Map,fs.set(e,r));let s=Object.keys(n).reduce((a,o)=>{let l,c=r.get(o);c?l=c:(l=e(o),r.set(o,l));for(let d=l.length-1;d>=0;d--){let u=l[d];if(u.filename){a[u.filename]=n[o];break}}return a},{});try{t.exception.values.forEach(a=>{a.stacktrace.frames.forEach(o=>{o.filename&&(o.debug_id=s[o.filename])})})}catch{}}function zu(t){let e={};try{t.exception.values.forEach(r=>{r.stacktrace.frames.forEach(i=>{i.debug_id&&(i.abs_path?e[i.abs_path]=i.debug_id:i.filename&&(e[i.filename]=i.debug_id),delete i.debug_id)})})}catch{}if(Object.keys(e).length===0)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];let n=t.debug_meta.images;Object.keys(e).forEach(r=>{n.push({type:"sourcemap",code_file:r,debug_id:e[r]})})}function qu(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}function Wu(t,e,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(i=>({...i,...i.data&&{data:kt(i.data,e,n)}}))},...t.user&&{user:kt(t.user,e,n)},...t.contexts&&{contexts:kt(t.contexts,e,n)},...t.extra&&{extra:kt(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=kt(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(i=>{let s=X(i).data;return s&&(i.data=kt(s,e,n)),i})),r}function Vu(t,e){if(!e)return t;let n=t?t.clone():new an;return n.update(e),n}function Yu(t){if(t)return Ku(t)?{captureContext:t}:Gu(t)?{captureContext:t}:t}function Ku(t){return t instanceof an||typeof t=="function"}var Ju=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function Gu(t){return Object.keys(t).some(e=>Ju.includes(e))}function cr(t,e){return ut().captureException(t,Yu(e))}function Ga(t,e){return ut().captureEvent(t,e)}function Xt(t,e){ut().addBreadcrumb(t,e)}function Xu(t,e){ut().setContext(t,e)}function Qu(...t){let e=ut();if(t.length===2){let[n,r]=t;return n?e.withScope(()=>(e.getStackTop().scope=n,r(n))):e.withScope(r)}return e.withScope(t[0])}function $(){return ut().getClient()}function ct(){return ut().getScope()}function gs(t){let e=$(),n=te(),r=ct(),{release:i,environment:s=ar}=e&&e.getOptions()||{},{userAgent:a}=A.navigator||{},o=Va({release:i,environment:s,user:r.getUser()||n.getUser(),...a&&{userAgent:a},...t}),l=n.getSession();return l&&l.status==="ok"&&ue(l,{status:"exited"}),Xa(),n.setSession(o),r.setSession(o),o}function Xa(){let t=te(),e=ct(),n=e.getSession()||t.getSession();n&&Ya(n),Qa(),t.setSession(),e.setSession()}function Qa(){let t=te(),e=ct(),n=$(),r=e.getSession()||t.getSession();r&&n&&n.captureSession&&n.captureSession(r)}function _s(t=!1){if(t){Xa();return}Qa()}function jn(t){return t.transaction}function ur(t,e,n){let r=e.getOptions(),{publicKey:i}=e.getDsn()||{},{segment:s}=n&&n.getUser()||{},a=ot({environment:r.environment||ar,release:r.release,user_segment:s,public_key:i,trace_id:t});return e.emit&&e.emit("createDsc",a),a}function Ae(t){let e=$();if(!e)return{};let n=ur(X(t).trace_id||"",e,ct()),r=jn(t);if(!r)return n;let i=r&&r._frozenDynamicSamplingContext;if(i)return i;let{sampleRate:s,source:a}=r.metadata;s!=null&&(n.sample_rate=`${s}`);let o=X(r);return a&&a!=="url"&&(n.transaction=o.description),n.sampled=String(wi(r)),e.emit&&e.emit("createDsc",n),n}function Za(t,e){let{fingerprint:n,span:r,breadcrumbs:i,sdkProcessingMetadata:s}=e;Zu(t,e),r&&nd(t,r),rd(t,n),td(t,i),ed(t,s)}function ys(t,e){let{extra:n,tags:r,user:i,contexts:s,level:a,sdkProcessingMetadata:o,breadcrumbs:l,fingerprint:c,eventProcessors:d,attachments:u,propagationContext:p,transactionName:h,span:m}=e;Je(t,"extra",n),Je(t,"tags",r),Je(t,"user",i),Je(t,"contexts",s),Je(t,"sdkProcessingMetadata",o),a&&(t.level=a),h&&(t.transactionName=h),m&&(t.span=m),l.length&&(t.breadcrumbs=[...t.breadcrumbs,...l]),c.length&&(t.fingerprint=[...t.fingerprint,...c]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),u.length&&(t.attachments=[...t.attachments,...u]),t.propagationContext={...t.propagationContext,...p}}function Je(t,e,n){if(n&&Object.keys(n).length){t[e]={...t[e]};for(let r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[e][r]=n[r])}}function Zu(t,e){let{extra:n,tags:r,user:i,contexts:s,level:a,transactionName:o}=e,l=ot(n);l&&Object.keys(l).length&&(t.extra={...l,...t.extra});let c=ot(r);c&&Object.keys(c).length&&(t.tags={...c,...t.tags});let d=ot(i);d&&Object.keys(d).length&&(t.user={...d,...t.user});let u=ot(s);u&&Object.keys(u).length&&(t.contexts={...u,...t.contexts}),a&&(t.level=a),o&&(t.transaction=o)}function td(t,e){let n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}function ed(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}function nd(t,e){t.contexts={trace:Ti(e),...t.contexts};let n=jn(e);if(n){t.sdkProcessingMetadata={dynamicSamplingContext:Ae(e),...t.sdkProcessingMetadata};let r=X(n).description;r&&(t.tags={transaction:r,...t.tags})}}function rd(t,e){t.fingerprint=t.fingerprint?Da(t.fingerprint):[],e&&(t.fingerprint=t.fingerprint.concat(e)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}var id=100,xr,an=class Pn{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=vs()}static clone(e){return e?e.clone():new Pn}clone(){let e=new Pn;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},e._user=this._user,e._level=this._level,e._span=this._span,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e}setClient(e){this._client=e}getClient(){return this._client}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,segment:void 0,username:void 0},this._session&&ue(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSpan(e){return this._span=e,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let e=this._span;return e&&e.transaction}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let n=typeof e=="function"?e(this):e;if(n instanceof Pn){let r=n.getScopeData();this._tags={...this._tags,...r.tags},this._extra={...this._extra,...r.extra},this._contexts={...this._contexts,...r.contexts},r.user&&Object.keys(r.user).length&&(this._user=r.user),r.level&&(this._level=r.level),r.fingerprint.length&&(this._fingerprint=r.fingerprint),n.getRequestSession()&&(this._requestSession=n.getRequestSession()),r.propagationContext&&(this._propagationContext=r.propagationContext)}else if(Me(n)){let r=e;this._tags={...this._tags,...r.tags},this._extra={...this._extra,...r.extra},this._contexts={...this._contexts,...r.contexts},r.user&&(this._user=r.user),r.level&&(this._level=r.level),r.fingerprint&&(this._fingerprint=r.fingerprint),r.requestSession&&(this._requestSession=r.requestSession),r.propagationContext&&(this._propagationContext=r.propagationContext)}return this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=vs(),this}addBreadcrumb(e,n){let r=typeof n=="number"?n:id;if(r<=0)return this;let i={timestamp:pn(),...e},s=this._breadcrumbs;return s.push(i),this._breadcrumbs=s.length>r?s.slice(-r):s,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}getAttachments(){return this.getScopeData().attachments}clearAttachments(){return this._attachments=[],this}getScopeData(){let{_breadcrumbs:e,_attachments:n,_contexts:r,_tags:i,_extra:s,_user:a,_level:o,_fingerprint:l,_eventProcessors:c,_propagationContext:d,_sdkProcessingMetadata:u,_transactionName:p,_span:h}=this;return{breadcrumbs:e,attachments:n,contexts:r,tags:i,extra:s,user:a,level:o,fingerprint:l||[],eventProcessors:c,propagationContext:d,sdkProcessingMetadata:u,transactionName:p,span:h}}applyToEvent(e,n={},r=[]){Za(e,this.getScopeData());let i=[...r,...ki(),...this._eventProcessors];return Fn(i,e,n)}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){let r=n&&n.event_id?n.event_id:J();if(!this._client)return f.warn("No client configured on scope - will not capture exception!"),r;let i=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:i,...n,event_id:r},this),r}captureMessage(e,n,r){let i=r&&r.event_id?r.event_id:J();if(!this._client)return f.warn("No client configured on scope - will not capture message!"),i;let s=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:s,...r,event_id:i},this),i}captureEvent(e,n){let r=n&&n.event_id?n.event_id:J();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(f.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}};function sd(){return xr||(xr=new an),xr}function vs(){return{traceId:J(),spanId:J().substring(16)}}var Kr="7.118.0",to=parseFloat(Kr),ad=100,eo=class{constructor(t,e,n,r=to){this._version=r;let i;e?i=e:(i=new an,i.setClient(t));let s;n?s=n:(s=new an,s.setClient(t)),this._stack=[{scope:i}],t&&this.bindClient(t),this._isolationScope=s}isOlderThan(t){return this._version<t}bindClient(t){let e=this.getStackTop();e.client=t,e.scope.setClient(t),t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){let t=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return this.getStack().length<=1?!1:!!this.getStack().pop()}withScope(t){let e=this.pushScope(),n;try{n=t(e)}catch(r){throw this.popScope(),r}return sr(n)?n.then(r=>(this.popScope(),r),r=>{throw this.popScope(),r}):(this.popScope(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,e){let n=this._lastEventId=e&&e.event_id?e.event_id:J(),r=new Error("Sentry syntheticException");return this.getScope().captureException(t,{originalException:t,syntheticException:r,...e,event_id:n}),n}captureMessage(t,e,n){let r=this._lastEventId=n&&n.event_id?n.event_id:J(),i=new Error(t);return this.getScope().captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:r}),r}captureEvent(t,e){let n=e&&e.event_id?e.event_id:J();return t.type||(this._lastEventId=n),this.getScope().captureEvent(t,{...e,event_id:n}),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,e){let{scope:n,client:r}=this.getStackTop();if(!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:s=ad}=r.getOptions&&r.getOptions()||{};if(s<=0)return;let a={timestamp:pn(),...t},o=i?Gt(()=>i(a,e)):a;o!==null&&(r.emit&&r.emit("beforeAddBreadcrumb",o,e),n.addBreadcrumb(o,s))}setUser(t){this.getScope().setUser(t),this.getIsolationScope().setUser(t)}setTags(t){this.getScope().setTags(t),this.getIsolationScope().setTags(t)}setExtras(t){this.getScope().setExtras(t),this.getIsolationScope().setExtras(t)}setTag(t,e){this.getScope().setTag(t,e),this.getIsolationScope().setTag(t,e)}setExtra(t,e){this.getScope().setExtra(t,e),this.getIsolationScope().setExtra(t,e)}setContext(t,e){this.getScope().setContext(t,e),this.getIsolationScope().setContext(t,e)}configureScope(t){let{scope:e,client:n}=this.getStackTop();n&&t(e)}run(t){let e=Ss(this);try{t(this)}finally{Ss(e)}}getIntegration(t){let e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch{return T&&f.warn(`Cannot retrieve integration ${t.id} from the current Hub`),null}}startTransaction(t,e){let n=this._callExtensionMethod("startTransaction",t,e);return T&&!n&&(this.getClient()?f.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`):f.warn("Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'")),n}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){let t=this.getStackTop().scope,e=t.getSession();e&&Ya(e),this._sendSessionUpdate(),t.setSession()}startSession(t){let{scope:e,client:n}=this.getStackTop(),{release:r,environment:i=ar}=n&&n.getOptions()||{},{userAgent:s}=A.navigator||{},a=Va({release:r,environment:i,user:e.getUser(),...s&&{userAgent:s},...t}),o=e.getSession&&e.getSession();return o&&o.status==="ok"&&ue(o,{status:"exited"}),this.endSession(),e.setSession(a),a}shouldSendDefaultPii(){let t=this.getClient(),e=t&&t.getOptions();return!!(e&&e.sendDefaultPii)}_sendSessionUpdate(){let{scope:t,client:e}=this.getStackTop(),n=t.getSession();n&&e&&e.captureSession&&e.captureSession(n)}_callExtensionMethod(t,...e){let n=mn().__SENTRY__;if(n&&n.extensions&&typeof n.extensions[t]=="function")return n.extensions[t].apply(this,e);T&&f.warn(`Extension method ${t} couldn't be found, doing nothing.`)}};function mn(){return A.__SENTRY__=A.__SENTRY__||{extensions:{},hub:void 0},A}function Ss(t){let e=mn(),n=Jr(e);return no(e,t),n}function ut(){let t=mn();if(t.__SENTRY__&&t.__SENTRY__.acs){let e=t.__SENTRY__.acs.getCurrentHub();if(e)return e}return od(t)}function te(){return ut().getIsolationScope()}function od(t=mn()){return(!ld(t)||Jr(t).isOlderThan(to))&&no(t,new eo),Jr(t)}function ld(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Jr(t){return Ea("hub",()=>new eo,t)}function no(t,e){if(!t)return!1;let n=t.__SENTRY__=t.__SENTRY__||{};return n.hub=e,!0}function xt(t){return(t||ut()).getScope().getTransaction()}var bs=!1;function cd(){bs||(bs=!0,Pa(Gr),$a(Gr))}function Gr(){let t=xt();if(t){let e="internal_error";T&&f.log(`[Tracing] Transaction: ${e} -> Global error occured`),t.setStatus(e)}}Gr.tag="sentry_tracingErrorCallback";var ks;(function(t){let e="ok";t.Ok=e;let n="deadline_exceeded";t.DeadlineExceeded=n;let r="unauthenticated";t.Unauthenticated=r;let i="permission_denied";t.PermissionDenied=i;let s="not_found";t.NotFound=s;let a="resource_exhausted";t.ResourceExhausted=a;let o="invalid_argument";t.InvalidArgument=o;let l="unimplemented";t.Unimplemented=l;let c="unavailable";t.Unavailable=c;let d="internal_error";t.InternalError=d;let u="unknown_error";t.UnknownError=u;let p="cancelled";t.Cancelled=p;let h="already_exists";t.AlreadyExists=h;let m="failed_precondition";t.FailedPrecondition=m;let g="aborted";t.Aborted=g;let _="out_of_range";t.OutOfRange=_;let y="data_loss";t.DataLoss=y})(ks||(ks={}));function ud(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}function Ei(t,e){t.setTag("http.status_code",String(e)),t.setData("http.response.status_code",e);let n=ud(e);n!=="unknown_error"&&t.setStatus(n)}function he(t){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;let e=$(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}function Ii(t){if(!he())return;let e=pd(t),n=ut(),r=t.scope?t.scope.getSpan():De();if(t.onlyIfParent&&!r)return;let i=(t.scope||ct()).clone();return dd(n,{parentSpan:r,spanContext:e,forceTransaction:t.forceTransaction,scope:i})}function De(){return ct().getSpan()}function dd(t,{parentSpan:e,spanContext:n,forceTransaction:r,scope:i}){if(!he())return;let s=te(),a;if(e&&!r)a=e.startChild(n);else if(e){let o=Ae(e),{traceId:l,spanId:c}=e.spanContext(),d=wi(e);a=t.startTransaction({traceId:l,parentSpanId:c,parentSampled:d,...n,metadata:{dynamicSamplingContext:o,...n.metadata}})}else{let{traceId:o,dsc:l,parentSpanId:c,sampled:d}={...s.getPropagationContext(),...i.getPropagationContext()};a=t.startTransaction({traceId:o,parentSpanId:c,parentSampled:d,...n,metadata:{dynamicSamplingContext:l,...n.metadata}})}return i.setSpan(a),hd(a,i,s),a}function pd(t){if(t.startTime){let e={...t};return e.startTimestamp=lr(t.startTime),delete e.startTime,e}return t}var ro="_sentryScope",io="_sentryIsolationScope";function hd(t,e,n){t&&(ce(t,io,n),ce(t,ro,e))}function md(t){return{scope:t[ro],isolationScope:t[io]}}var rt="sentry.source",ve="sentry.sample_rate",Cn="sentry.op",At="sentry.origin",fd="profile_id",so=class{constructor(t=1e3){this._maxlen=t,this.spans=[]}add(t){this.spans.length>this._maxlen?t.spanRecorder=void 0:this.spans.push(t)}},ao=class oo{constructor(e={}){this._traceId=e.traceId||J(),this._spanId=e.spanId||J().substring(16),this._startTime=e.startTimestamp||hn(),this.tags=e.tags?{...e.tags}:{},this.data=e.data?{...e.data}:{},this.instrumenter=e.instrumenter||"sentry",this._attributes={},this.setAttributes({[At]:e.origin||"manual",[Cn]:e.op,...e.attributes}),this._name=e.name||e.description,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.status&&(this._status=e.status),e.endTimestamp&&(this._endTime=e.endTimestamp),e.exclusiveTime!==void 0&&(this._exclusiveTime=e.exclusiveTime),this._measurements=e.measurements?{...e.measurements}:{}}get name(){return this._name||""}set name(e){this.updateName(e)}get description(){return this._name}set description(e){this._name=e}get traceId(){return this._traceId}set traceId(e){this._traceId=e}get spanId(){return this._spanId}set spanId(e){this._spanId=e}set parentSpanId(e){this._parentSpanId=e}get parentSpanId(){return this._parentSpanId}get sampled(){return this._sampled}set sampled(e){this._sampled=e}get attributes(){return this._attributes}set attributes(e){this._attributes=e}get startTimestamp(){return this._startTime}set startTimestamp(e){this._startTime=e}get endTimestamp(){return this._endTime}set endTimestamp(e){this._endTime=e}get status(){return this._status}set status(e){this._status=e}get op(){return this._attributes[Cn]}set op(e){this.setAttribute(Cn,e)}get origin(){return this._attributes[At]}set origin(e){this.setAttribute(At,e)}spanContext(){let{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?Ka:ju}}startChild(e){let n=new oo({...e,parentSpanId:this._spanId,sampled:this._sampled,traceId:this._traceId});n.spanRecorder=this.spanRecorder,n.spanRecorder&&n.spanRecorder.add(n);let r=jn(this);if(n.transaction=r,T&&r){let i=e&&e.op||"< unknown op >",s=X(n).description||"< unknown name >",a=r.spanContext().spanId,o=`[Tracing] Starting '${i}' span on transaction '${s}' (${a}).`;f.log(o),this._logMessage=o}return n}setTag(e,n){return this.tags={...this.tags,[e]:n},this}setData(e,n){return this.data={...this.data,[e]:n},this}setAttribute(e,n){n===void 0?delete this._attributes[e]:this._attributes[e]=n}setAttributes(e){Object.keys(e).forEach(n=>this.setAttribute(n,e[n]))}setStatus(e){return this._status=e,this}setHttpStatus(e){return Ei(this,e),this}setName(e){this.updateName(e)}updateName(e){return this._name=e,this}isSuccess(){return this._status==="ok"}finish(e){return this.end(e)}end(e){if(this._endTime)return;let n=jn(this);if(T&&n&&n.spanContext().spanId!==this._spanId){let r=this._logMessage;r&&f.log(r.replace("Starting","Finishing"))}this._endTime=lr(e)}toTraceparent(){return or(this)}toContext(){return ot({data:this._getData(),description:this._name,endTimestamp:this._endTime,op:this.op,parentSpanId:this._parentSpanId,sampled:this._sampled,spanId:this._spanId,startTimestamp:this._startTime,status:this._status,tags:this.tags,traceId:this._traceId})}updateWithContext(e){return this.data=e.data||{},this._name=e.name||e.description,this._endTime=e.endTimestamp,this.op=e.op,this._parentSpanId=e.parentSpanId,this._sampled=e.sampled,this._spanId=e.spanId||this._spanId,this._startTime=e.startTimestamp||this._startTime,this._status=e.status,this.tags=e.tags||{},this._traceId=e.traceId||this._traceId,this}getTraceContext(){return Ti(this)}getSpanJSON(){return ot({data:this._getData(),description:this._name,op:this._attributes[Cn],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:this._status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[At],_metrics_summary:void 0,profile_id:this._attributes[fd],exclusive_time:this._exclusiveTime,measurements:Object.keys(this._measurements).length>0?this._measurements:void 0})}isRecording(){return!this._endTime&&!!this._sampled}toJSON(){return this.getSpanJSON()}_getData(){let{data:e,_attributes:n}=this,r=Object.keys(e).length>0,i=Object.keys(n).length>0;if(!(!r&&!i))return r&&i?{...e,...n}:r?e:n}},lo=class extends ao{constructor(t,e){super(t),this._contexts={},this._hub=e||ut(),this._name=t.name||"",this._metadata={...t.metadata},this._trimEnd=t.trimEnd,this.transaction=this;let n=this._metadata.dynamicSamplingContext;n&&(this._frozenDynamicSamplingContext={...n})}get name(){return this._name}set name(t){this.setName(t)}get metadata(){return{source:"custom",spanMetadata:{},...this._metadata,...this._attributes[rt]&&{source:this._attributes[rt]},...this._attributes[ve]&&{sampleRate:this._attributes[ve]}}}set metadata(t){this._metadata=t}setName(t,e="custom"){this._name=t,this.setAttribute(rt,e)}updateName(t){return this._name=t,this}initSpanRecorder(t=1e3){this.spanRecorder||(this.spanRecorder=new so(t)),this.spanRecorder.add(this)}setContext(t,e){e===null?delete this._contexts[t]:this._contexts[t]=e}setMeasurement(t,e,n=""){this._measurements[t]={value:e,unit:n}}setMetadata(t){this._metadata={...this._metadata,...t}}end(t){let e=lr(t),n=this._finishTransaction(e);if(n)return this._hub.captureEvent(n)}toContext(){let t=super.toContext();return ot({...t,name:this._name,trimEnd:this._trimEnd})}updateWithContext(t){return super.updateWithContext(t),this._name=t.name||"",this._trimEnd=t.trimEnd,this}getDynamicSamplingContext(){return Ae(this)}setHub(t){this._hub=t}getProfileId(){if(this._contexts!==void 0&&this._contexts.profile!==void 0)return this._contexts.profile.profile_id}_finishTransaction(t){if(this._endTime!==void 0)return;this._name||(T&&f.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>"),super.end(t);let e=this._hub.getClient();if(e&&e.emit&&e.emit("finishTransaction",this),this._sampled!==!0){T&&f.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","transaction");return}let n=this.spanRecorder?this.spanRecorder.spans.filter(l=>l!==this&&X(l).timestamp):[];if(this._trimEnd&&n.length>0){let l=n.map(c=>X(c).timestamp).filter(Boolean);this._endTime=l.reduce((c,d)=>c>d?c:d)}let{scope:r,isolationScope:i}=md(this),{metadata:s}=this,{source:a}=s,o={contexts:{...this._contexts,trace:Ti(this)},spans:n,start_timestamp:this._startTime,tags:this.tags,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{...s,capturedSpanScope:r,capturedSpanIsolationScope:i,...ot({dynamicSamplingContext:Ae(this)})},_metrics_summary:void 0,...a&&{transaction_info:{source:a}}};return Object.keys(this._measurements).length>0&&(T&&f.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),o.measurements=this._measurements),T&&f.log(`[Tracing] Finishing ${this.op} transaction: ${this._name}.`),o}},Qe={idleTimeout:1e3,finalTimeout:3e4,heartbeatInterval:5e3},gd="finishReason",fe=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"],_d=class extends so{constructor(t,e,n,r){super(r),this._pushActivity=t,this._popActivity=e,this.transactionSpanId=n}add(t){if(t.spanContext().spanId!==this.transactionSpanId){let e=t.end;t.end=(...n)=>(this._popActivity(t.spanContext().spanId),e.apply(t,n)),X(t).timestamp===void 0&&this._pushActivity(t.spanContext().spanId)}super.add(t)}},yd=class extends lo{constructor(t,e,n=Qe.idleTimeout,r=Qe.finalTimeout,i=Qe.heartbeatInterval,s=!1,a=!1){super(t,e),this._idleHub=e,this._idleTimeout=n,this._finalTimeout=r,this._heartbeatInterval=i,this._onScope=s,this.activities={},this._heartbeatCounter=0,this._finished=!1,this._idleTimeoutCanceledPermanently=!1,this._beforeFinishCallbacks=[],this._finishReason=fe[4],this._autoFinishAllowed=!a,s&&(T&&f.log(`Setting idle transaction on scope. Span ID: ${this.spanContext().spanId}`),e.getScope().setSpan(this)),a||this._restartIdleTimeout(),setTimeout(()=>{this._finished||(this.setStatus("deadline_exceeded"),this._finishReason=fe[3],this.end())},this._finalTimeout)}end(t){let e=lr(t);if(this._finished=!0,this.activities={},this.op==="ui.action.click"&&this.setAttribute(gd,this._finishReason),this.spanRecorder){T&&f.log("[Tracing] finishing IdleTransaction",new Date(e*1e3).toISOString(),this.op);for(let n of this._beforeFinishCallbacks)n(this,e);this.spanRecorder.spans=this.spanRecorder.spans.filter(n=>{if(n.spanContext().spanId===this.spanContext().spanId)return!0;X(n).timestamp||(n.setStatus("cancelled"),n.end(e),T&&f.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(n,void 0,2)));let{start_timestamp:r,timestamp:i}=X(n),s=r&&r<e,a=(this._finalTimeout+this._idleTimeout)/1e3,o=i&&r&&i-r<a;if(T){let l=JSON.stringify(n,void 0,2);s?o||f.log("[Tracing] discarding Span since it finished after Transaction final timeout",l):f.log("[Tracing] discarding Span since it happened after Transaction was finished",l)}return s&&o}),T&&f.log("[Tracing] flushing IdleTransaction")}else T&&f.log("[Tracing] No active IdleTransaction");if(this._onScope){let n=this._idleHub.getScope();n.getTransaction()===this&&n.setSpan(void 0)}return super.end(t)}registerBeforeFinishCallback(t){this._beforeFinishCallbacks.push(t)}initSpanRecorder(t){if(!this.spanRecorder){let e=r=>{this._finished||this._pushActivity(r)},n=r=>{this._finished||this._popActivity(r)};this.spanRecorder=new _d(e,n,this.spanContext().spanId,t),T&&f.log("Starting heartbeat"),this._pingHeartbeat()}this.spanRecorder.add(this)}cancelIdleTimeout(t,{restartOnChildSpanChange:e}={restartOnChildSpanChange:!0}){this._idleTimeoutCanceledPermanently=e===!1,this._idleTimeoutID&&(clearTimeout(this._idleTimeoutID),this._idleTimeoutID=void 0,Object.keys(this.activities).length===0&&this._idleTimeoutCanceledPermanently&&(this._finishReason=fe[5],this.end(t)))}setFinishReason(t){this._finishReason=t}sendAutoFinishSignal(){this._autoFinishAllowed||(T&&f.log("[Tracing] Received finish signal for idle transaction."),this._restartIdleTimeout(),this._autoFinishAllowed=!0)}_restartIdleTimeout(t){this.cancelIdleTimeout(),this._idleTimeoutID=setTimeout(()=>{!this._finished&&Object.keys(this.activities).length===0&&(this._finishReason=fe[1],this.end(t))},this._idleTimeout)}_pushActivity(t){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this._idleTimeoutCanceledPermanently}),T&&f.log(`[Tracing] pushActivity: ${t}`),this.activities[t]=!0,T&&f.log("[Tracing] new activities count",Object.keys(this.activities).length)}_popActivity(t){if(this.activities[t]&&(T&&f.log(`[Tracing] popActivity ${t}`),delete this.activities[t],T&&f.log("[Tracing] new activities count",Object.keys(this.activities).length)),Object.keys(this.activities).length===0){let e=hn();this._idleTimeoutCanceledPermanently?this._autoFinishAllowed&&(this._finishReason=fe[5],this.end(e)):this._restartIdleTimeout(e+this._idleTimeout/1e3)}}_beat(){if(this._finished)return;let t=Object.keys(this.activities).join("");t===this._prevHeartbeatString?this._heartbeatCounter++:this._heartbeatCounter=1,this._prevHeartbeatString=t,this._heartbeatCounter>=3?this._autoFinishAllowed&&(T&&f.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus("deadline_exceeded"),this._finishReason=fe[0],this.end()):this._pingHeartbeat()}_pingHeartbeat(){T&&f.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`),setTimeout(()=>{this._beat()},this._heartbeatInterval)}};function co(t,e,n){if(!he(e))return t.sampled=!1,t;if(t.sampled!==void 0)return t.setAttribute(ve,Number(t.sampled)),t;let r;return typeof e.tracesSampler=="function"?(r=e.tracesSampler(n),t.setAttribute(ve,Number(r))):n.parentSampled!==void 0?r=n.parentSampled:typeof e.tracesSampleRate<"u"?(r=e.tracesSampleRate,t.setAttribute(ve,Number(r))):(r=1,t.setAttribute(ve,r)),uo(r)?r?(t.sampled=Math.random()<r,t.sampled?(T&&f.log(`[Tracing] starting ${t.op} transaction - ${X(t).description}`),t):(T&&f.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),t)):(T&&f.log(`[Tracing] Discarding transaction because ${typeof e.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),t.sampled=!1,t):(T&&f.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)}function uo(t){return ka(t)||!(typeof t=="number"||typeof t=="boolean")?(T&&f.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`),!1):t<0||t>1?(T&&f.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${t}.`),!1):!0}function vd(){let t=this.getScope().getSpan();return t?{"sentry-trace":or(t)}:{}}function Sd(t,e){let n=this.getClient(),r=n&&n.getOptions()||{},i=r.instrumenter||"sentry",s=t.instrumenter||"sentry";i!==s&&(T&&f.error(`A transaction was started with instrumenter=\`${s}\`, but the SDK is configured with the \`${i}\` instrumenter.
The transaction will not be sampled. Please use the ${i} instrumentation to start transactions.`),t.sampled=!1);let a=new lo(t,this);return a=co(a,r,{name:t.name,parentSampled:t.parentSampled,transactionContext:t,attributes:{...t.data,...t.attributes},...e}),a.isRecording()&&a.initSpanRecorder(r._experiments&&r._experiments.maxSpans),n&&n.emit&&n.emit("startTransaction",a),a}function Un(t,e,n,r,i,s,a,o=!1){let l=t.getClient(),c=l&&l.getOptions()||{},d=new yd(e,t,n,r,a,i,o);return d=co(d,c,{name:e.name,parentSampled:e.parentSampled,transactionContext:e,attributes:{...e.data,...e.attributes},...s}),d.isRecording()&&d.initSpanRecorder(c._experiments&&c._experiments.maxSpans),l&&l.emit&&l.emit("startTransaction",d),d}function po(){let t=mn();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=Sd),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=vd),cd())}function bd(t,e,n){let r=xt();r&&r.setMeasurement(t,e,n)}function kd(t,e){return e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]]),t}function Td(t,e,n,r){let i=bi(n),s={sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&e&&{dsn:He(e)}},a="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return Zt(s,[a])}function wd(t,e,n,r){let i=bi(n),s=t.type&&t.type!=="replay_event"?t.type:"event";kd(t,n&&n.sdk);let a=za(t,i,r,e);return delete t.sdkProcessingMetadata,Zt(a,[[{type:s},t]])}var Ed="7";function Id(t){let e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function xd(t){return`${Id(t)}${t.projectId}/envelope/`}function Cd(t,e){return jc({sentry_key:t.publicKey,sentry_version:Ed,...e&&{sentry_client:`${e.name}/${e.version}`}})}function Rd(t,e={}){let n=typeof e=="string"?e:e.tunnel,r=typeof e=="string"||!e._metadata?void 0:e._metadata.sdk;return n||`${xd(t)}?${Cd(t,r)}`}var Ts=[];function Md(t){let e={};return t.forEach(n=>{let{name:r}=n,i=e[r];i&&!i.isDefaultInstance&&n.isDefaultInstance||(e[r]=n)}),Object.keys(e).map(n=>e[n])}function Od(t){let e=t.defaultIntegrations||[],n=t.integrations;e.forEach(a=>{a.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...e,...n]:typeof n=="function"?r=Da(n(e)):r=e;let i=Md(r),s=Dd(i,a=>a.name==="Debug");if(s!==-1){let[a]=i.splice(s,1);i.push(a)}return i}function Ad(t,e){let n={};return e.forEach(r=>{r&&ho(t,r,n)}),n}function ws(t,e){for(let n of e)n&&n.afterAllSetup&&n.afterAllSetup(t)}function ho(t,e,n){if(n[e.name]){T&&f.log(`Integration skipped because it was already installed: ${e.name}`);return}if(n[e.name]=e,Ts.indexOf(e.name)===-1&&(e.setupOnce($u,ut),Ts.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),t.on&&typeof e.preprocessEvent=="function"){let r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(i,s)=>r(i,s,t))}if(t.addEventProcessor&&typeof e.processEvent=="function"){let r=e.processEvent.bind(e),i=Object.assign((s,a)=>r(s,a,t),{id:e.name});t.addEventProcessor(i)}T&&f.log(`Integration installed: ${e.name}`)}function Dd(t,e){for(let n=0;n<t.length;n++)if(e(t[n])===!0)return n;return-1}function Pt(t,e){return Object.assign(function(...n){return e(...n)},{id:t})}function Nd(t){let e="";for(let n of t){let r=Object.entries(n.tags),i=r.length>0?`|#${r.map(([s,a])=>`${s}:${a}`).join(",")}`:"";e+=`${n.name}@${n.unit}:${n.metric}|${n.metricType}${i}|T${n.timestamp}
`}return e}function Ld(t,e,n,r){let i={sent_at:new Date().toISOString()};n&&n.sdk&&(i.sdk={name:n.sdk.name,version:n.sdk.version}),r&&e&&(i.dsn=He(e));let s=Pd(t);return Zt(i,[s])}function Pd(t){let e=Nd(t);return[{type:"statsd",length:e.length},e]}var Es="Not capturing exception because it's already been captured.",$d=class{constructor(t){if(this._options=t,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=Fc(t.dsn):T&&f.warn("No DSN provided, client will not send events."),this._dsn){let e=Rd(this._dsn,t);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){if(as(t)){T&&f.log(Es);return}let r=e&&e.event_id;return this._process(this.eventFromException(t,e).then(i=>this._captureEvent(i,e,n)).then(i=>{r=i})),r}captureMessage(t,e,n,r){let i=n&&n.event_id,s=pi(t)?t:String(t),a=hi(t)?this.eventFromMessage(s,e,n):this.eventFromException(t,n);return this._process(a.then(o=>this._captureEvent(o,n,r)).then(o=>{i=o})),i}captureEvent(t,e,n){if(e&&e.originalException&&as(e.originalException)){T&&f.log(Es);return}let r=e&&e.event_id,i=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,e,i||n).then(s=>{r=s})),r}captureSession(t){typeof t.release!="string"?T&&f.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),ue(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){let e=this._transport;return e?(this.metricsAggregator&&this.metricsAggregator.flush(),this._isClientDoneProcessing(t).then(n=>e.flush(t).then(r=>n&&r))):Oe(!0)}close(t){return this.flush(t).then(e=>(this.getOptions().enabled=!1,this.metricsAggregator&&this.metricsAggregator.close(),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}setupIntegrations(t){(t&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)&&this._setupIntegrations()}init(){this._isEnabled()&&this._setupIntegrations()}getIntegrationById(t){return this.getIntegrationByName(t)}getIntegrationByName(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch{return T&&f.warn(`Cannot retrieve integration ${t.id} from the current Client`),null}}addIntegration(t){let e=this._integrations[t.name];ho(this,t,this._integrations),e||ws(this,[t])}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=wd(t,this._dsn,this._options._metadata,this._options.tunnel);for(let i of e.attachments||[])n=Iu(n,Ru(i,this._options.transportOptions&&this._options.transportOptions.textEncoder));let r=this._sendEnvelope(n);r&&r.then(i=>this.emit("afterSendEvent",t,i),null)}sendSession(t){let e=Td(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}recordDroppedEvent(t,e,n){if(this._options.sendClientReports){let r=`${t}:${e}`;T&&f.log(`Adding outcome: "${r}"`),this._outcomes[r]=this._outcomes[r]+1||1}}captureAggregateMetrics(t){T&&f.log(`Flushing aggregated metrics, number of metrics: ${t.length}`);let e=Ld(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}on(t,e){this._hooks[t]||(this._hooks[t]=[]),this._hooks[t].push(e)}emit(t,...e){this._hooks[t]&&this._hooks[t].forEach(n=>n(...e))}_setupIntegrations(){let{integrations:t}=this._options;this._integrations=Ad(this,t),ws(this,t),this._integrationsInitialized=!0}_updateSessionFromEvent(t,e){let n=!1,r=!1,i=e.exception&&e.exception.values;if(i){r=!0;for(let a of i){let o=a.mechanism;if(o&&o.handled===!1){n=!0;break}}}let s=t.status==="ok";(s&&t.errors===0||s&&n)&&(ue(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new ze(e=>{let n=0,r=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),e(!0)):(n+=r,t&&n>=t&&(clearInterval(i),e(!1)))},r)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,e,n,r=te()){let i=this.getOptions(),s=Object.keys(this._integrations);return!e.integrations&&s.length>0&&(e.integrations=s),this.emit("preprocessEvent",t,e),Ja(i,t,e,n,this,r).then(a=>{if(a===null)return a;let o={...r.getPropagationContext(),...n?n.getPropagationContext():void 0};if(!(a.contexts&&a.contexts.trace)&&o){let{traceId:l,spanId:c,parentSpanId:d,dsc:u}=o;a.contexts={trace:{trace_id:l,span_id:c,parent_span_id:d},...a.contexts};let p=u||ur(l,this,n);a.sdkProcessingMetadata={dynamicSamplingContext:p,...a.sdkProcessingMetadata}}return a})}_captureEvent(t,e={},n){return this._processEvent(t,e,n).then(r=>r.event_id,r=>{if(T){let i=r;i.logLevel==="log"?f.log(i.message):f.warn(i)}})}_processEvent(t,e,n){let r=this.getOptions(),{sampleRate:i}=r,s=fo(t),a=mo(t),o=t.type||"error",l=`before send for type \`${o}\``;if(a&&typeof i=="number"&&Math.random()>i)return this.recordDroppedEvent("sample_rate","error",t),vi(new wt(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let c=o==="replay_event"?"replay":o,d=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,e,n,d).then(u=>{if(u===null)throw this.recordDroppedEvent("event_processor",c,t),new wt("An event processor returned `null`, will not send event.","log");if(e.data&&e.data.__sentry__===!0)return u;let p=jd(r,u,e);return Fd(p,l)}).then(u=>{if(u===null)throw this.recordDroppedEvent("before_send",c,t),new wt(`${l} returned \`null\`, will not send event.`,"log");let p=n&&n.getSession();!s&&p&&this._updateSessionFromEvent(p,u);let h=u.transaction_info;if(s&&h&&u.transaction!==t.transaction){let m="custom";u.transaction_info={...h,source:m}}return this.sendEvent(u,e),u}).then(null,u=>{throw u instanceof wt?u:(this.captureException(u,{data:{__sentry__:!0},originalException:u}),new wt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${u}`))})}_process(t){this._numProcessing++,t.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_sendEnvelope(t){if(this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport)return this._transport.send(t).then(null,e=>{T&&f.error("Error while sending event:",e)});T&&f.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(e=>{let[n,r]=e.split(":");return{reason:n,category:r,quantity:t[e]}})}};function Fd(t,e){let n=`${e} must return \`null\` or a valid event.`;if(sr(t))return t.then(r=>{if(!Me(r)&&r!==null)throw new wt(n);return r},r=>{throw new wt(`${e} rejected with ${r}`)});if(!Me(t)&&t!==null)throw new wt(n);return t}function jd(t,e,n){let{beforeSend:r,beforeSendTransaction:i}=t;return mo(e)&&r?r(e,n):fo(e)&&i?i(e,n):e}function mo(t){return t.type===void 0}function fo(t){return t.type==="transaction"}function go(t){let e=$();!e||!e.addEventProcessor||e.addEventProcessor(t)}function Ud(t,e){e.debug===!0&&(T?f.enable():Gt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),ct().update(e.initialScope);let n=new t(e);Bd(n),Hd(n)}function Bd(t){let e=ut().getStackTop();e.client=t,e.scope.setClient(t)}function Hd(t){t.init?t.init():t.setupIntegrations&&t.setupIntegrations()}var zd=30;function _o(t,e,n=gu(t.bufferSize||zd)){let r={},i=a=>n.drain(a);function s(a){let o=[];if(ps(a,(u,p)=>{let h=hs(p);if(qa(r,h)){let m=Is(u,p);t.recordDroppedEvent("ratelimit_backoff",h,m)}else o.push(u)}),o.length===0)return Oe();let l=Zt(a[0],o),c=u=>{ps(l,(p,h)=>{let m=Is(p,h);t.recordDroppedEvent(u,hs(h),m)})},d=()=>e({body:xu(l,t.textEncoder)}).then(u=>(u.statusCode!==void 0&&(u.statusCode<200||u.statusCode>=300)&&T&&f.warn(`Sentry responded with status code ${u.statusCode} to sent event.`),r=Wa(r,u),u),u=>{throw c("network_error"),u});return n.add(d).then(u=>u,u=>{if(u instanceof wt)return T&&f.error("Skipped sending event because buffer is full."),c("queue_overflow"),Oe();throw u})}return s.__sentry__baseTransport__=!0,{send:s,flush:i}}function Is(t,e){if(!(e!=="event"&&e!=="transaction"))return Array.isArray(t)?t[1]:void 0}function qd(t,e){let n={sent_at:new Date().toISOString()};e&&(n.dsn=He(e));let r=t.map(Wd);return Zt(n,r)}function Wd(t){return[{type:"span"},t]}function Vd(t,e){let n=e&&Jd(e)?e.getClient():e,r=n&&n.getDsn(),i=n&&n.getOptions().tunnel;return Kd(t,r)||Yd(t,i)}function Yd(t,e){return e?xs(t)===xs(e):!1}function Kd(t,e){return e?t.includes(e.host):!1}function xs(t){return t[t.length-1]==="/"?t.slice(0,-1):t}function Jd(t){return t.getClient!==void 0}function xi(t,e,n=[e],r="npm"){let i=t._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${e}`,packages:n.map(s=>({name:`${r}:@sentry/${s}`,version:Kr})),version:Kr}),t._metadata=i}var Gd=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],Xd=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/],yo="InboundFilters",Qd=(t={})=>({name:yo,setupOnce(){},processEvent(e,n,r){let i=r.getOptions(),s=tp(t,i);return ep(e,s)?null:e}}),vo=Qd,Zd=Pt(yo,vo);function tp(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:Gd],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[],...t.disableTransactionDefaults?[]:Xd],ignoreInternal:t.ignoreInternal!==void 0?t.ignoreInternal:!0}}function ep(t,e){return e.ignoreInternal&&op(t)?(T&&f.warn(`Event dropped due to being internal Sentry Error.
Event: ${Bt(t)}`),!0):np(t,e.ignoreErrors)?(T&&f.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Bt(t)}`),!0):rp(t,e.ignoreTransactions)?(T&&f.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Bt(t)}`),!0):ip(t,e.denyUrls)?(T&&f.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Bt(t)}.
Url: ${Bn(t)}`),!0):sp(t,e.allowUrls)?!1:(T&&f.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Bt(t)}.
Url: ${Bn(t)}`),!0)}function np(t,e){return t.type||!e||!e.length?!1:ap(t).some(n=>Ue(n,e))}function rp(t,e){if(t.type!=="transaction"||!e||!e.length)return!1;let n=t.transaction;return n?Ue(n,e):!1}function ip(t,e){if(!e||!e.length)return!1;let n=Bn(t);return n?Ue(n,e):!1}function sp(t,e){if(!e||!e.length)return!0;let n=Bn(t);return n?Ue(n,e):!0}function ap(t){let e=[];t.message&&e.push(t.message);let n;try{n=t.exception.values[t.exception.values.length-1]}catch{}return n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`)),T&&e.length===0&&f.error(`Could not extract message for event ${Bt(t)}`),e}function op(t){try{return t.exception.values[0].type==="SentryError"}catch{}return!1}function lp(t=[]){for(let e=t.length-1;e>=0;e--){let n=t[e];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Bn(t){try{let e;try{e=t.exception.values[0].stacktrace.frames}catch{}return e?lp(e):null}catch{return T&&f.error(`Cannot extract url for event ${Bt(t)}`),null}}var Cs,So="FunctionToString",Rs=new WeakMap,cp=()=>({name:So,setupOnce(){Cs=Function.prototype.toString;try{Function.prototype.toString=function(...t){let e=gi(this),n=Rs.has($())&&e!==void 0?e:this;return Cs.apply(n,t)}}catch{}},setup(t){Rs.set(t,!0)}}),bo=cp,up=Pt(So,bo),dp="cause",pp=5,ko="LinkedErrors",hp=(t={})=>{let e=t.limit||pp,n=t.key||dp;return{name:ko,setupOnce(){},preprocessEvent(r,i,s){let a=s.getOptions();wa(Lu,a.stackParser,a.maxValueLength,n,e,r,i)}}},mp=hp,fp=Pt(ko,mp),To={};va(To,{FunctionToString:()=>up,InboundFilters:()=>Zd,LinkedErrors:()=>fp});var gp=To,M=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,k=A;function wo(){k.document?k.document.addEventListener("visibilitychange",()=>{let t=xt();if(k.document.hidden&&t){let e="cancelled",{op:n,status:r}=X(t);M&&f.log(`[Tracing] Transaction: ${e} -> since tab moved to the background, op: ${n}`),r||t.setStatus(e),t.setTag("visibilitychange","document.hidden"),t.end()}}):M&&f.warn("[Tracing] Could not set up background tab detection due to lack of global document")}var fn=(t,e,n)=>{let r,i;return s=>{e.value>=0&&(s||n)&&(i=e.value-(r||0),(i||r===void 0)&&(r=e.value,e.delta=i,t(e)))}},_p=()=>`v3-${Date.now()}-${Math.floor(Math.random()*8999999999999)+1e12}`,yp=()=>{let t=k.performance.timing,e=k.performance.navigation.type,n={entryType:"navigation",startTime:0,type:e==2?"back_forward":e===1?"reload":"navigate"};for(let r in t)r!=="navigationStart"&&r!=="toJSON"&&(n[r]=Math.max(t[r]-t.navigationStart,0));return n},dr=()=>k.__WEB_VITALS_POLYFILL__?k.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||yp()):k.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0],Ci=()=>{let t=dr();return t&&t.activationStart||0},gn=(t,e)=>{let n=dr(),r="navigate";return n&&(k.document&&k.document.prerendering||Ci()>0?r="prerender":r=n.type.replace(/_/g,"-")),{name:t,value:typeof e>"u"?-1:e,rating:"good",delta:0,entries:[],id:_p(),navigationType:r}},qe=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){let r=new PerformanceObserver(i=>{e(i.getEntries())});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch{}},_n=(t,e)=>{let n=r=>{(r.type==="pagehide"||k.document.visibilityState==="hidden")&&(t(r),e&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};k.document&&(addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0))},vp=(t,e={})=>{let n=gn("CLS",0),r,i=0,s=[],a=l=>{l.forEach(c=>{if(!c.hadRecentInput){let d=s[0],u=s[s.length-1];i&&s.length!==0&&c.startTime-u.startTime<1e3&&c.startTime-d.startTime<5e3?(i+=c.value,s.push(c)):(i=c.value,s=[c]),i>n.value&&(n.value=i,n.entries=s,r&&r())}})},o=qe("layout-shift",a);if(o){r=fn(t,n,e.reportAllChanges);let l=()=>{a(o.takeRecords()),r(!0)};return _n(l),l}},Hn=-1,Sp=()=>{k.document&&k.document.visibilityState&&(Hn=k.document.visibilityState==="hidden"&&!k.document.prerendering?0:1/0)},bp=()=>{_n(({timeStamp:t})=>{Hn=t},!0)},Ri=()=>(Hn<0&&(Sp(),bp()),{get firstHiddenTime(){return Hn}}),kp=t=>{let e=Ri(),n=gn("FID"),r,i=o=>{o.startTime<e.firstHiddenTime&&(n.value=o.processingStart-o.startTime,n.entries.push(o),r(!0))},s=o=>{o.forEach(i)},a=qe("first-input",s);r=fn(t,n),a&&_n(()=>{s(a.takeRecords()),a.disconnect()},!0)},Eo=0,Cr=1/0,Rn=0,Tp=t=>{t.forEach(e=>{e.interactionId&&(Cr=Math.min(Cr,e.interactionId),Rn=Math.max(Rn,e.interactionId),Eo=Rn?(Rn-Cr)/7+1:0)})},Xr,wp=()=>Xr?Eo:performance.interactionCount||0,Ep=()=>{"interactionCount"in performance||Xr||(Xr=qe("event",Tp,{type:"event",buffered:!0,durationThreshold:0}))},Io=()=>wp(),Ms=10,Ot=[],Rr={},Os=t=>{let e=Ot[Ot.length-1],n=Rr[t.interactionId];if(n||Ot.length<Ms||t.duration>e.latency){if(n)n.entries.push(t),n.latency=Math.max(n.latency,t.duration);else{let r={id:t.interactionId,latency:t.duration,entries:[t]};Rr[r.id]=r,Ot.push(r)}Ot.sort((r,i)=>i.latency-r.latency),Ot.splice(Ms).forEach(r=>{delete Rr[r.id]})}},Ip=()=>{let t=Math.min(Ot.length-1,Math.floor(Io()/50));return Ot[t]},xp=(t,e)=>{e=e||{},Ep();let n=gn("INP"),r,i=a=>{a.forEach(l=>{l.interactionId&&Os(l),l.entryType==="first-input"&&!Ot.some(c=>c.entries.some(d=>l.duration===d.duration&&l.startTime===d.startTime))&&Os(l)});let o=Ip();o&&o.latency!==n.value&&(n.value=o.latency,n.entries=o.entries,r())},s=qe("event",i,{durationThreshold:e.durationThreshold||40});r=fn(t,n,e.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),_n(()=>{i(s.takeRecords()),n.value<0&&Io()>0&&(n.value=0,n.entries=[]),r(!0)}))},As={},Cp=t=>{let e=Ri(),n=gn("LCP"),r,i=a=>{let o=a[a.length-1];if(o){let l=Math.max(o.startTime-Ci(),0);l<e.firstHiddenTime&&(n.value=l,n.entries=[o],r())}},s=qe("largest-contentful-paint",i);if(s){r=fn(t,n);let a=()=>{As[n.id]||(i(s.takeRecords()),s.disconnect(),As[n.id]=!0,r(!0))};return["keydown","click"].forEach(o=>{k.document&&addEventListener(o,a,{once:!0,capture:!0})}),_n(a,!0),a}},Qr=t=>{k.document&&(k.document.prerendering?addEventListener("prerenderingchange",()=>Qr(t),!0):k.document.readyState!=="complete"?addEventListener("load",()=>Qr(t),!0):setTimeout(t,0))},Rp=(t,e)=>{e=e||{};let n=gn("TTFB"),r=fn(t,n,e.reportAllChanges);Qr(()=>{let i=dr();if(i){if(n.value=Math.max(i.responseStart-Ci(),0),n.value<0||n.value>performance.now())return;n.entries=[i],r(!0)}})},Ze={},zn={},xo,Co,Ro,Mo,Oo;function Mp(t,e=!1){return yn("cls",t,Np,xo,e)}function Ao(t,e=!1){return yn("lcp",t,Pp,Ro,e)}function Op(t){return yn("ttfb",t,$p,Mo)}function Ap(t){return yn("fid",t,Lp,Co)}function Dp(t){return yn("inp",t,Fp,Oo)}function Qt(t,e){return Do(t,e),zn[t]||(jp(t),zn[t]=!0),No(t,e)}function We(t,e){let n=Ze[t];if(!(!n||!n.length))for(let r of n)try{r(e)}catch(i){M&&f.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${Lt(r)}
Error:`,i)}}function Np(){return vp(t=>{We("cls",{metric:t}),xo=t},{reportAllChanges:!0})}function Lp(){return kp(t=>{We("fid",{metric:t}),Co=t})}function Pp(){return Cp(t=>{We("lcp",{metric:t}),Ro=t})}function $p(){return Rp(t=>{We("ttfb",{metric:t}),Mo=t})}function Fp(){return xp(t=>{We("inp",{metric:t}),Oo=t})}function yn(t,e,n,r,i=!1){Do(t,e);let s;return zn[t]||(s=n(),zn[t]=!0),r&&e({metric:r}),No(t,e,i?s:void 0)}function jp(t){let e={};t==="event"&&(e.durationThreshold=0),qe(t,n=>{We(t,{entries:n})},e)}function Do(t,e){Ze[t]=Ze[t]||[],Ze[t].push(e)}function No(t,e,n){return()=>{n&&n();let r=Ze[t];if(!r)return;let i=r.indexOf(e);i!==-1&&r.splice(i,1)}}function Mr(t){return typeof t=="number"&&isFinite(t)}function Ne(t,{startTimestamp:e,...n}){return e&&t.startTimestamp>e&&(t.startTimestamp=e),t.startChild({startTimestamp:e,...n})}var Up=2147483647;function et(t){return t/1e3}function Mi(){return k&&k.addEventListener&&k.performance}var Ds=0,K={},bt,tn;function Lo(){let t=Mi();if(t&&lt){t.mark&&k.performance.mark("sentry-tracing-init");let e=zp(),n=Bp(),r=Hp(),i=qp();return()=>{e(),n(),r(),i()}}return()=>{}}function Po(){Qt("longtask",({entries:t})=>{for(let e of t){let n=xt();if(!n)return;let r=et(lt+e.startTime),i=et(e.duration);n.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:r,endTimestamp:r+i})}})}function $o(){Qt("event",({entries:t})=>{for(let e of t){let n=xt();if(!n)return;if(e.name==="click"){let r=et(lt+e.startTime),i=et(e.duration),s={description:Jt(e.target),op:`ui.interaction.${e.name}`,origin:"auto.ui.browser.metrics",startTimestamp:r,endTimestamp:r+i},a=Ia(e.target);a&&(s.attributes={"ui.component_name":a}),n.startChild(s)}}})}function Fo(t,e){if(Mi()&&lt){let n=Wp(t,e);return()=>{n()}}return()=>{}}function Bp(){return Mp(({metric:t})=>{let e=t.entries[t.entries.length-1];e&&(M&&f.log("[Measurements] Adding CLS"),K.cls={value:t.value,unit:""},tn=e)},!0)}function Hp(){return Ao(({metric:t})=>{let e=t.entries[t.entries.length-1];e&&(M&&f.log("[Measurements] Adding LCP"),K.lcp={value:t.value,unit:"millisecond"},bt=e)},!0)}function zp(){return Ap(({metric:t})=>{let e=t.entries[t.entries.length-1];if(!e)return;let n=et(lt),r=et(e.startTime);M&&f.log("[Measurements] Adding FID"),K.fid={value:t.value,unit:"millisecond"},K["mark.fid"]={value:n+r,unit:"second"}})}function qp(){return Op(({metric:t})=>{t.entries[t.entries.length-1]&&(M&&f.log("[Measurements] Adding TTFB"),K.ttfb={value:t.value,unit:"millisecond"})})}var Ns={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Wp(t,e){return Dp(({metric:n})=>{if(n.value===void 0)return;let r=n.entries.find(w=>w.duration===n.value&&Ns[w.name]!==void 0),i=$();if(!r||!i)return;let s=Ns[r.name],a=i.getOptions(),o=et(lt+r.startTime),l=et(n.value),c=r.interactionId!==void 0?t[r.interactionId]:void 0;if(c===void 0)return;let{routeName:d,parentContext:u,activeTransaction:p,user:h,replayId:m}=c,g=h!==void 0?h.email||h.id||h.ip_address:void 0,_=p!==void 0?p.getProfileId():void 0,y=new ao({startTimestamp:o,endTimestamp:o+l,op:`ui.interaction.${s}`,name:Jt(r.target),attributes:{release:a.release,environment:a.environment,transaction:d,...g!==void 0&&g!==""?{user:g}:{},..._!==void 0?{profile_id:_}:{},...m!==void 0?{replay_id:m}:{}},exclusiveTime:n.value,measurements:{inp:{value:n.value,unit:"millisecond"}}}),S=Zp(u,a,e);if(S&&Math.random()<S){let w=y?qd([y],i.getDsn()):void 0,D=i&&i.getTransport();D&&w&&D.send(w).then(null,F=>{M&&f.error("Error while sending interaction:",F)});return}})}function jo(t){let e=Mi();if(!e||!k.performance.getEntries||!lt)return;M&&f.log("[Tracing] Adding & adjusting spans using Performance API");let n=et(lt),r=e.getEntries(),{op:i,start_timestamp:s}=X(t);if(r.slice(Ds).forEach(a=>{let o=et(a.startTime),l=et(a.duration);if(!(t.op==="navigation"&&s&&n+o<s))switch(a.entryType){case"navigation":{Yp(t,a,n);break}case"mark":case"paint":case"measure":{Vp(t,a,o,l,n);let c=Ri(),d=a.startTime<c.firstHiddenTime;a.name==="first-paint"&&d&&(M&&f.log("[Measurements] Adding FP"),K.fp={value:a.startTime,unit:"millisecond"}),a.name==="first-contentful-paint"&&d&&(M&&f.log("[Measurements] Adding FCP"),K.fcp={value:a.startTime,unit:"millisecond"});break}case"resource":{Jp(t,a,a.name,o,l,n);break}}}),Ds=Math.max(r.length-1,0),Gp(t),i==="pageload"){Qp(K),["fcp","fp","lcp"].forEach(o=>{if(!K[o]||!s||n>=s)return;let l=K[o].value,c=n+et(l),d=Math.abs((c-s)*1e3),u=d-l;M&&f.log(`[Measurements] Normalized ${o} from ${l} to ${d} (${u})`),K[o].value=d});let a=K["mark.fid"];a&&K.fid&&(Ne(t,{description:"first input delay",endTimestamp:a.value+et(K.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:a.value}),delete K["mark.fid"]),"fcp"in K||delete K.cls,Object.keys(K).forEach(o=>{bd(o,K[o].value,K[o].unit)}),Xp(t)}bt=void 0,tn=void 0,K={}}function Vp(t,e,n,r,i){let s=i+n,a=s+r;return Ne(t,{description:e.name,endTimestamp:a,op:e.entryType,origin:"auto.resource.browser.metrics",startTimestamp:s}),s}function Yp(t,e,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(r=>{Mn(t,e,r,n)}),Mn(t,e,"secureConnection",n,"TLS/SSL","connectEnd"),Mn(t,e,"fetch",n,"cache","domainLookupStart"),Mn(t,e,"domainLookup",n,"DNS"),Kp(t,e,n)}function Mn(t,e,n,r,i,s){let a=s?e[s]:e[`${n}End`],o=e[`${n}Start`];!o||!a||Ne(t,{op:"browser",origin:"auto.browser.browser.metrics",description:i||n,startTimestamp:r+et(o),endTimestamp:r+et(a)})}function Kp(t,e,n){e.responseEnd&&(Ne(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:n+et(e.requestStart),endTimestamp:n+et(e.responseEnd)}),Ne(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:n+et(e.responseStart),endTimestamp:n+et(e.responseEnd)}))}function Jp(t,e,n,r,i,s){if(e.initiatorType==="xmlhttprequest"||e.initiatorType==="fetch")return;let a=oe(n),o={};Or(o,e,"transferSize","http.response_transfer_size"),Or(o,e,"encodedBodySize","http.response_content_length"),Or(o,e,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in e&&(o["resource.render_blocking_status"]=e.renderBlockingStatus),a.protocol&&(o["url.scheme"]=a.protocol.split(":").pop()),a.host&&(o["server.address"]=a.host),o["url.same_origin"]=n.includes(k.location.origin);let l=s+r,c=l+i;Ne(t,{description:n.replace(k.location.origin,""),endTimestamp:c,op:e.initiatorType?`resource.${e.initiatorType}`:"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:l,data:o})}function Gp(t){let e=k.navigator;if(!e)return;let n=e.connection;n&&(n.effectiveType&&t.setTag("effectiveConnectionType",n.effectiveType),n.type&&t.setTag("connectionType",n.type),Mr(n.rtt)&&(K["connection.rtt"]={value:n.rtt,unit:"millisecond"})),Mr(e.deviceMemory)&&t.setTag("deviceMemory",`${e.deviceMemory} GB`),Mr(e.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(e.hardwareConcurrency))}function Xp(t){bt&&(M&&f.log("[Measurements] Adding LCP Data"),bt.element&&t.setTag("lcp.element",Jt(bt.element)),bt.id&&t.setTag("lcp.id",bt.id),bt.url&&t.setTag("lcp.url",bt.url.trim().slice(0,200)),t.setTag("lcp.size",bt.size)),tn&&tn.sources&&(M&&f.log("[Measurements] Adding CLS Data"),tn.sources.forEach((e,n)=>t.setTag(`cls.source.${n+1}`,Jt(e.node))))}function Or(t,e,n,r){let i=e[n];i!=null&&i<Up&&(t[r]=i)}function Qp(t){let e=dr();if(!e)return;let{responseStart:n,requestStart:r}=e;r<=n&&(M&&f.log("[Measurements] Adding TTFB Request Time"),t["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}function Zp(t,e,n){if(!he(e))return!1;let r;return t!==void 0&&typeof e.tracesSampler=="function"?r=e.tracesSampler({transactionContext:t,name:t.name,parentSampled:t.parentSampled,attributes:{...t.data,...t.attributes},location:k.location}):t!==void 0&&t.sampled!==void 0?r=t.sampled:typeof e.tracesSampleRate<"u"?r=e.tracesSampleRate:r=1,uo(r)?r===!0?n:r===!1?0:r*n:(M&&f.warn("[Tracing] Discarding interaction span because of invalid sample rate."),!1)}function th(t,e,n,r,i="auto.http.browser"){if(!he()||!t.fetchData)return;let s=e(t.fetchData.url);if(t.endTimestamp&&s){let h=t.fetchData.__span;if(!h)return;let m=r[h];m&&(rh(m,t),delete r[h]);return}let a=ct(),o=$(),{method:l,url:c}=t.fetchData,d=nh(c),u=d?oe(d).host:void 0,p=s?Ii({name:`${l} ${c}`,onlyIfParent:!0,attributes:{url:c,type:"fetch","http.method":l,"http.url":d,"server.address":u,[At]:i},op:"http.client"}):void 0;if(p&&(t.fetchData.__span=p.spanContext().spanId,r[p.spanContext().spanId]=p),n(t.fetchData.url)&&o){let h=t.args[0];t.args[1]=t.args[1]||{};let m=t.args[1];m.headers=eh(h,o,a,m,p)}return p}function eh(t,e,n,r,i){let s=i||n.getSpan(),a=te(),{traceId:o,spanId:l,sampled:c,dsc:d}={...a.getPropagationContext(),...n.getPropagationContext()},u=s?or(s):Si(o,l,c),p=Ba(d||(s?Ae(s):ur(o,e,n))),h=r.headers||(typeof Request<"u"&&Nt(t,Request)?t.headers:void 0);if(h)if(typeof Headers<"u"&&Nt(h,Headers)){let m=new Headers(h);return m.append("sentry-trace",u),p&&m.append(Vr,p),m}else if(Array.isArray(h)){let m=[...h,["sentry-trace",u]];return p&&m.push([Vr,p]),m}else{let m="baggage"in h?h.baggage:void 0,g=[];return Array.isArray(m)?g.push(...m):m&&g.push(m),p&&g.push(p),{...h,"sentry-trace":u,baggage:g.length>0?g.join(","):void 0}}else return{"sentry-trace":u,baggage:p}}function nh(t){try{return new URL(t).href}catch{return}}function rh(t,e){if(e.response){Ei(t,e.response.status);let n=e.response&&e.response.headers&&e.response.headers.get("content-length");if(n){let r=parseInt(n);r>0&&t.setAttribute("http.response_content_length",r)}}else e.error&&t.setStatus("internal_error");t.end()}var Zr=["localhost",/^\/(?!\/)/],qn={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:Zr,tracePropagationTargets:Zr};function Uo(t){let{traceFetch:e,traceXHR:n,tracePropagationTargets:r,tracingOrigins:i,shouldCreateSpanForRequest:s,enableHTTPTimings:a}={traceFetch:qn.traceFetch,traceXHR:qn.traceXHR,...t},o=typeof s=="function"?s:d=>!0,l=d=>oh(d,r||i),c={};e&&_i(d=>{let u=th(d,o,l,c);if(u){let p=Bo(d.fetchData.url),h=p?oe(p).host:void 0;u.setAttributes({"http.url":p,"server.address":h})}a&&u&&Ls(u)}),n&&yi(d=>{let u=lh(d,o,l,c);a&&u&&Ls(u)})}function ih(t){return t.entryType==="resource"&&"initiatorType"in t&&typeof t.nextHopProtocol=="string"&&(t.initiatorType==="fetch"||t.initiatorType==="xmlhttprequest")}function Ls(t){let{url:e}=X(t).data||{};if(!e||typeof e!="string")return;let n=Qt("resource",({entries:r})=>{r.forEach(i=>{ih(i)&&i.name.endsWith(e)&&(ah(i).forEach(s=>t.setAttribute(...s)),setTimeout(n))})})}function sh(t){let e="unknown",n="unknown",r="";for(let i of t){if(i==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(i))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=i}return r===t&&(e=r),{name:e,version:n}}function St(t=0){return((lt||performance.timeOrigin)+t)/1e3}function ah(t){let{name:e,version:n}=sh(t.nextHopProtocol),r=[];return r.push(["network.protocol.version",n],["network.protocol.name",e]),lt?[...r,["http.request.redirect_start",St(t.redirectStart)],["http.request.fetch_start",St(t.fetchStart)],["http.request.domain_lookup_start",St(t.domainLookupStart)],["http.request.domain_lookup_end",St(t.domainLookupEnd)],["http.request.connect_start",St(t.connectStart)],["http.request.secure_connection_start",St(t.secureConnectionStart)],["http.request.connection_end",St(t.connectEnd)],["http.request.request_start",St(t.requestStart)],["http.request.response_start",St(t.responseStart)],["http.request.response_end",St(t.responseEnd)]]:r}function oh(t,e){return Ue(t,e||Zr)}function lh(t,e,n,r){let i=t.xhr,s=i&&i[Ht];if(!he()||!i||i.__sentry_own_request__||!s)return;let a=e(s.url);if(t.endTimestamp&&a){let h=i.__sentry_xhr_span_id__;if(!h)return;let m=r[h];m&&s.status_code!==void 0&&(Ei(m,s.status_code),m.end(),delete r[h]);return}let o=ct(),l=te(),c=Bo(s.url),d=c?oe(c).host:void 0,u=a?Ii({name:`${s.method} ${s.url}`,onlyIfParent:!0,attributes:{type:"xhr","http.method":s.method,"http.url":c,url:s.url,"server.address":d,[At]:"auto.http.browser"},op:"http.client"}):void 0;u&&(i.__sentry_xhr_span_id__=u.spanContext().spanId,r[i.__sentry_xhr_span_id__]=u);let p=$();if(i.setRequestHeader&&n(s.url)&&p){let{traceId:h,spanId:m,sampled:g,dsc:_}={...l.getPropagationContext(),...o.getPropagationContext()},y=u?or(u):Si(h,m,g),S=Ba(_||(u?Ae(u):ur(h,p,o)));ch(i,y,S)}return u}function ch(t,e,n){try{t.setRequestHeader("sentry-trace",e),n&&t.setRequestHeader(Vr,n)}catch{}}function Bo(t){try{return new URL(t,k.location.origin).href}catch{return}}function uh(t,e=!0,n=!0){if(!k||!k.location){M&&f.warn("Could not initialize routing instrumentation due to invalid location");return}let r=k.location.href,i;e&&(i=t({name:k.location.pathname,startTimestamp:lt?lt/1e3:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}})),n&&dn(({to:s,from:a})=>{if(a===void 0&&r&&r.indexOf(s)!==-1){r=void 0;return}a!==s&&(r=void 0,i&&(M&&f.log(`[Tracing] Finishing current transaction with op: ${i.op}`),i.end()),i=t({name:k.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}}))})}var dh="BrowserTracing",ph={...Qe,markBackgroundTransactions:!0,routingInstrumentation:uh,startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...qn},Ps=10,hh=class{constructor(t){this.name=dh,this._hasSetTracePropagationTargets=!1,po(),M&&(this._hasSetTracePropagationTargets=!!(t&&(t.tracePropagationTargets||t.tracingOrigins))),this.options={...ph,...t},this.options._experiments.enableLongTask!==void 0&&(this.options.enableLongTask=this.options._experiments.enableLongTask),t&&!t.tracePropagationTargets&&t.tracingOrigins&&(this.options.tracePropagationTargets=t.tracingOrigins),this._collectWebVitals=Lo(),this._interactionIdToRouteNameMapping={},this.options.enableInp&&Fo(this._interactionIdToRouteNameMapping,this.options.interactionsSampleRate),this.options.enableLongTask&&Po(),this.options._experiments.enableInteractions&&$o(),this._latestRoute={name:void 0,context:void 0}}setupOnce(t,e){this._getCurrentHub=e;let n=e().getClient(),r=n&&n.getOptions(),{routingInstrumentation:i,startTransactionOnLocationChange:s,startTransactionOnPageLoad:a,markBackgroundTransactions:o,traceFetch:l,traceXHR:c,shouldCreateSpanForRequest:d,enableHTTPTimings:u,_experiments:p}=this.options,h=r&&r.tracePropagationTargets,m=h||this.options.tracePropagationTargets;M&&this._hasSetTracePropagationTargets&&h&&f.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used."),i(g=>{let _=this._createRouteTransaction(g);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction(_,g,e),_},a,s),o&&wo(),p.enableInteractions&&this._registerInteractionListener(),this.options.enableInp&&this._registerInpInteractionListener(),Uo({traceFetch:l,traceXHR:c,tracePropagationTargets:m,shouldCreateSpanForRequest:d,enableHTTPTimings:u})}_createRouteTransaction(t){if(!this._getCurrentHub){M&&f.warn(`[Tracing] Did not create ${t.op} transaction because _getCurrentHub is invalid.`);return}let e=this._getCurrentHub(),{beforeNavigate:n,idleTimeout:r,finalTimeout:i,heartbeatInterval:s}=this.options,a=t.op==="pageload",o;if(a){let p=a?$s("sentry-trace"):"",h=a?$s("baggage"):void 0,{traceId:m,dsc:g,parentSpanId:_,sampled:y}=Ha(p,h);o={traceId:m,parentSpanId:_,parentSampled:y,...t,metadata:{...t.metadata,dynamicSamplingContext:g},trimEnd:!0}}else o={trimEnd:!0,...t};let l=typeof n=="function"?n(o):o,c=l===void 0?{...o,sampled:!1}:l;c.metadata=c.name!==o.name?{...c.metadata,source:"custom"}:c.metadata,this._latestRoute.name=c.name,this._latestRoute.context=c,c.sampled===!1&&M&&f.log(`[Tracing] Will not send ${c.op} transaction because of beforeNavigate.`),M&&f.log(`[Tracing] Starting ${c.op} transaction on scope`);let{location:d}=k,u=Un(e,c,r,i,!0,{location:d},s,a);return a&&k.document&&(k.document.addEventListener("readystatechange",()=>{["interactive","complete"].includes(k.document.readyState)&&u.sendAutoFinishSignal()}),["interactive","complete"].includes(k.document.readyState)&&u.sendAutoFinishSignal()),u.registerBeforeFinishCallback(p=>{this._collectWebVitals(),jo(p)}),u}_registerInteractionListener(){let t,e=()=>{let{idleTimeout:n,finalTimeout:r,heartbeatInterval:i}=this.options,s="ui.action.click",a=xt();if(a&&a.op&&["navigation","pageload"].includes(a.op)){M&&f.warn(`[Tracing] Did not create ${s} transaction because a pageload or navigation transaction is in progress.`);return}if(t&&(t.setFinishReason("interactionInterrupted"),t.end(),t=void 0),!this._getCurrentHub){M&&f.warn(`[Tracing] Did not create ${s} transaction because _getCurrentHub is invalid.`);return}if(!this._latestRoute.name){M&&f.warn(`[Tracing] Did not create ${s} transaction because _latestRouteName is missing.`);return}let o=this._getCurrentHub(),{location:l}=k,c={name:this._latestRoute.name,op:s,trimEnd:!0,data:{[rt]:this._latestRoute.context?mh(this._latestRoute.context):"url"}};t=Un(o,c,n,r,!0,{location:l},i)};["click"].forEach(n=>{k.document&&addEventListener(n,e,{once:!1,capture:!0})})}_registerInpInteractionListener(){let t=({entries:e})=>{let n=$(),r=n!==void 0&&n.getIntegrationByName!==void 0?n.getIntegrationByName("Replay"):void 0,i=r!==void 0?r.getReplayId():void 0,s=xt(),a=ct(),o=a!==void 0?a.getUser():void 0;e.forEach(l=>{if(fh(l)){let c=l.interactionId;if(c===void 0)return;let d=this._interactionIdToRouteNameMapping[c],u=l.duration,p=l.startTime,h=Object.keys(this._interactionIdToRouteNameMapping),m=h.length>0?h.reduce((g,_)=>this._interactionIdToRouteNameMapping[g].duration<this._interactionIdToRouteNameMapping[_].duration?g:_):void 0;if(l.entryType==="first-input"&&h.map(g=>this._interactionIdToRouteNameMapping[g]).some(g=>g.duration===u&&g.startTime===p)||!c)return;if(d)d.duration=Math.max(d.duration,u);else if(h.length<Ps||m===void 0||u>this._interactionIdToRouteNameMapping[m].duration){let g=this._latestRoute.name,_=this._latestRoute.context;g&&_&&(m&&Object.keys(this._interactionIdToRouteNameMapping).length>=Ps&&delete this._interactionIdToRouteNameMapping[m],this._interactionIdToRouteNameMapping[c]={routeName:g,duration:u,parentContext:_,user:o,activeTransaction:s,replayId:i,startTime:p})}}})};Qt("event",t),Qt("first-input",t)}};function $s(t){let e=fi(`meta[name=${t}]`);return e?e.getAttribute("content"):void 0}function mh(t){let e=t.attributes&&t.attributes[rt],n=t.data&&t.data[rt],r=t.metadata&&t.metadata.source;return e||n||r}function fh(t){return"duration"in t}var gh="BrowserTracing",_h={...Qe,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...qn},yh=(t={})=>{let e=M?!!(t.tracePropagationTargets||t.tracingOrigins):!1;po(),!t.tracePropagationTargets&&t.tracingOrigins&&(t.tracePropagationTargets=t.tracingOrigins);let n={..._h,...t},r=Lo(),i={};n.enableInp&&Fo(i,n.interactionsSampleRate),n.enableLongTask&&Po(),n._experiments.enableInteractions&&$o();let s={name:void 0,context:void 0};function a(o){let l=ut(),{beforeStartSpan:c,idleTimeout:d,finalTimeout:u,heartbeatInterval:p}=n,h=o.op==="pageload",m;if(h){let S=h?Fs("sentry-trace"):"",w=h?Fs("baggage"):void 0,{traceId:D,dsc:F,parentSpanId:b,sampled:v}=Ha(S,w);m={traceId:D,parentSpanId:b,parentSampled:v,...o,metadata:{...o.metadata,dynamicSamplingContext:F},trimEnd:!0}}else m={trimEnd:!0,...o};let g=c?c(m):m;g.metadata=g.name!==m.name?{...g.metadata,source:"custom"}:g.metadata,s.name=g.name,s.context=g,g.sampled===!1&&M&&f.log(`[Tracing] Will not send ${g.op} transaction because of beforeNavigate.`),M&&f.log(`[Tracing] Starting ${g.op} transaction on scope`);let{location:_}=k,y=Un(l,g,d,u,!0,{location:_},p,h);return h&&k.document&&(k.document.addEventListener("readystatechange",()=>{["interactive","complete"].includes(k.document.readyState)&&y.sendAutoFinishSignal()}),["interactive","complete"].includes(k.document.readyState)&&y.sendAutoFinishSignal()),y.registerBeforeFinishCallback(S=>{r(),jo(S)}),y}return{name:gh,setupOnce:()=>{},afterAllSetup(o){let l=o.getOptions(),{markBackgroundSpan:c,traceFetch:d,traceXHR:u,shouldCreateSpanForRequest:p,enableHTTPTimings:h,_experiments:m}=n,g=l&&l.tracePropagationTargets,_=g||n.tracePropagationTargets;M&&e&&g&&f.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");let y,S=k.location&&k.location.href;if(o.on&&(o.on("startNavigationSpan",w=>{y&&(M&&f.log(`[Tracing] Finishing current transaction with op: ${X(y).op}`),y.end()),y=a({op:"navigation",...w})}),o.on("startPageLoadSpan",w=>{y&&(M&&f.log(`[Tracing] Finishing current transaction with op: ${X(y).op}`),y.end()),y=a({op:"pageload",...w})})),n.instrumentPageLoad&&o.emit&&k.location){let w={name:k.location.pathname,startTimestamp:lt?lt/1e3:void 0,origin:"auto.pageload.browser",attributes:{[rt]:"url"}};Ho(o,w)}n.instrumentNavigation&&o.emit&&k.location&&dn(({to:w,from:D})=>{if(D===void 0&&S&&S.indexOf(w)!==-1){S=void 0;return}if(D!==w){S=void 0;let F={name:k.location.pathname,origin:"auto.navigation.browser",attributes:{[rt]:"url"}};zo(o,F)}}),c&&wo(),m.enableInteractions&&vh(n,s),n.enableInp&&bh(i,s),Uo({traceFetch:d,traceXHR:u,tracePropagationTargets:_,shouldCreateSpanForRequest:p,enableHTTPTimings:h})},options:n}};function Ho(t,e){if(!t.emit)return;t.emit("startPageLoadSpan",e);let n=De();return(n&&X(n).op)==="pageload"?n:void 0}function zo(t,e){if(!t.emit)return;t.emit("startNavigationSpan",e);let n=De();return(n&&X(n).op)==="navigation"?n:void 0}function Fs(t){let e=fi(`meta[name=${t}]`);return e?e.getAttribute("content"):void 0}function vh(t,e){let n,r=()=>{let{idleTimeout:i,finalTimeout:s,heartbeatInterval:a}=t,o="ui.action.click",l=xt();if(l&&l.op&&["navigation","pageload"].includes(l.op)){M&&f.warn(`[Tracing] Did not create ${o} transaction because a pageload or navigation transaction is in progress.`);return}if(n&&(n.setFinishReason("interactionInterrupted"),n.end(),n=void 0),!e.name){M&&f.warn(`[Tracing] Did not create ${o} transaction because _latestRouteName is missing.`);return}let{location:c}=k,d={name:e.name,op:o,trimEnd:!0,data:{[rt]:e.context?kh(e.context):"url"}};n=Un(ut(),d,i,s,!0,{location:c},a)};["click"].forEach(i=>{k.document&&addEventListener(i,r,{once:!1,capture:!0})})}function Sh(t){return"duration"in t}var js=10;function bh(t,e){let n=({entries:r})=>{let i=$(),s=i!==void 0&&i.getIntegrationByName!==void 0?i.getIntegrationByName("Replay"):void 0,a=s!==void 0?s.getReplayId():void 0,o=xt(),l=ct(),c=l!==void 0?l.getUser():void 0;r.forEach(d=>{if(Sh(d)){let u=d.interactionId;if(u===void 0)return;let p=t[u],h=d.duration,m=d.startTime,g=Object.keys(t),_=g.length>0?g.reduce((y,S)=>t[y].duration<t[S].duration?y:S):void 0;if(d.entryType==="first-input"&&g.map(y=>t[y]).some(y=>y.duration===h&&y.startTime===m)||!u)return;if(p)p.duration=Math.max(p.duration,h);else if(g.length<js||_===void 0||h>t[_].duration){let y=e.name,S=e.context;y&&S&&(_&&Object.keys(t).length>=js&&delete t[_],t[u]={routeName:y,duration:h,parentContext:S,user:c,activeTransaction:o,replayId:a,startTime:m})}}})};Qt("event",n),Qt("first-input",n)}function kh(t){let e=t.attributes&&t.attributes[rt],n=t.data&&t.data[rt],r=t.metadata&&t.metadata.source;return e||n||r}var I=A,ti=0;function qo(){return ti>0}function Th(){ti++,setTimeout(()=>{ti--})}function Le(t,e={},n){if(typeof t!="function")return t;try{let i=t.__sentry_wrapped__;if(i)return i;if(gi(t))return t}catch{return t}let r=function(){let i=Array.prototype.slice.call(arguments);try{n&&typeof n=="function"&&n.apply(this,arguments);let s=i.map(a=>Le(a,e));return t.apply(this,s)}catch(s){throw Th(),Qu(a=>{a.addEventProcessor(o=>(e.mechanism&&(jr(o,void 0,void 0),sn(o,e.mechanism)),o.extra={...o.extra,arguments:i},o)),cr(s)}),s}};try{for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}catch{}Ca(r,t),ce(t,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get(){return t.name}})}catch{}return r}var Dt=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Wo(t,e){let n=Oi(t,e),r={type:e&&e.name,value:xh(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function wh(t,e,n,r){let i=$(),s=i&&i.getOptions().normalizeDepth,a={exception:{values:[{type:ir(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Mh(e,{isUnhandledRejection:r})}]},extra:{__serialized__:Fa(e,s)}};if(n){let o=Oi(t,n);o.length&&(a.exception.values[0].stacktrace={frames:o})}return a}function Ar(t,e){return{exception:{values:[Wo(t,e)]}}}function Oi(t,e){let n=e.stacktrace||e.stack||"",r=Ih(e);try{return t(n,r)}catch{}return[]}var Eh=/Minified React error #\d+;/i;function Ih(t){if(t){if(typeof t.framesToPop=="number")return t.framesToPop;if(Eh.test(t.message))return 1}return 0}function xh(t){let e=t&&t.message;return e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function Ch(t,e,n,r){let i=n&&n.syntheticException||void 0,s=Ai(t,e,i,r);return sn(s),s.level="error",n&&n.event_id&&(s.event_id=n.event_id),Oe(s)}function Rh(t,e,n="info",r,i){let s=r&&r.syntheticException||void 0,a=ei(t,e,s,i);return a.level=n,r&&r.event_id&&(a.event_id=r.event_id),Oe(a)}function Ai(t,e,n,r,i){let s;if(di(e)&&e.error)return Ar(t,e.error);if(Xi(e)||Tc(e)){let a=e;if("stack"in e)s=Ar(t,e);else{let o=a.name||(Xi(a)?"DOMError":"DOMException"),l=a.message?`${o}: ${a.message}`:o;s=ei(t,l,n,r),jr(s,l)}return"code"in a&&(s.tags={...s.tags,"DOMException.code":`${a.code}`}),s}return ba(e)?Ar(t,e):Me(e)||ir(e)?(s=wh(t,e,n,i),sn(s,{synthetic:!0}),s):(s=ei(t,e,n,r),jr(s,`${e}`,void 0),sn(s,{synthetic:!0}),s)}function ei(t,e,n,r){let i={};if(r&&n){let s=Oi(t,n);s.length&&(i.exception={values:[{value:e,stacktrace:{frames:s}}]})}if(pi(e)){let{__sentry_template_string__:s,__sentry_template_values__:a}=e;return i.logentry={message:s,params:a},i}return i.message=e,i}function Mh(t,{isUnhandledRejection:e}){let n=Uc(t),r=e?"promise rejection":"exception";return di(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:ir(t)?`Event \`${Oh(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function Oh(t){try{let e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch{}}function Ah(t,{metadata:e,tunnel:n,dsn:r}){let i={event_id:t.event_id,sent_at:new Date().toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!r&&{dsn:He(r)}},s=Dh(t);return Zt(i,[s])}function Dh(t){return[{type:"user_report"},t]}var Nh=class extends $d{constructor(t){let e=I.SENTRY_SDK_SOURCE||lu();xi(t,"browser",["browser"],e),super(t),t.sendClientReports&&I.document&&I.document.addEventListener("visibilitychange",()=>{I.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,e){return Ch(this._options.stackParser,t,e,this._options.attachStacktrace)}eventFromMessage(t,e="info",n){return Rh(this._options.stackParser,t,e,n,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){Dt&&f.warn("SDK not enabled, will not capture user feedback.");return}let e=Ah(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this._sendEnvelope(e)}_prepareEvent(t,e,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,e,n)}_flushOutcomes(){let t=this._clearOutcomes();if(t.length===0){Dt&&f.log("No outcomes to send");return}if(!this._dsn){Dt&&f.log("No dsn provided, will not send outcomes");return}Dt&&f.log("Sending outcomes:",t);let e=Ou(t,this._options.tunnel&&He(this._dsn));this._sendEnvelope(e)}},Xe;function Lh(){if(Xe)return Xe;if(zr(I.fetch))return Xe=I.fetch.bind(I);let t=I.document,e=I.fetch;if(t&&typeof t.createElement=="function")try{let n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n);let r=n.contentWindow;r&&r.fetch&&(e=r.fetch),t.head.removeChild(n)}catch(n){Dt&&f.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return Xe=e.bind(I)}function Ph(){Xe=void 0}function $h(t,e=Lh()){let n=0,r=0;function i(s){let a=s.body.length;n+=a,r++;let o={body:s.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};try{return e(t.url,o).then(l=>(n-=a,r--,{statusCode:l.status,headers:{"x-sentry-rate-limits":l.headers.get("X-Sentry-Rate-Limits"),"retry-after":l.headers.get("Retry-After")}}))}catch(l){return Ph(),n-=a,r--,vi(l)}}return _o(t,i)}var Fh=4;function jh(t){function e(n){return new ze((r,i)=>{let s=new XMLHttpRequest;s.onerror=i,s.onreadystatechange=()=>{s.readyState===Fh&&r({statusCode:s.status,headers:{"x-sentry-rate-limits":s.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":s.getResponseHeader("Retry-After")}})},s.open("POST",t.url);for(let a in t.headers)Object.prototype.hasOwnProperty.call(t.headers,a)&&s.setRequestHeader(a,t.headers[a]);s.send(n.body)})}return _o(t,e)}var pr="?",Uh=30,Bh=40,Hh=50;function Di(t,e,n,r){let i={filename:t,function:e,in_app:!0};return n!==void 0&&(i.lineno=n),r!==void 0&&(i.colno=r),i}var zh=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,qh=/\((\S*)(?::(\d+))(?::(\d+))\)/,Wh=t=>{let e=zh.exec(t);if(e){if(e[2]&&e[2].indexOf("eval")===0){let i=qh.exec(e[2]);i&&(e[2]=i[1],e[3]=i[2],e[4]=i[3])}let[n,r]=Vo(e[1]||pr,e[2]);return Di(r,n,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}},Vh=[Uh,Wh],Yh=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Kh=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Jh=t=>{let e=Yh.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){let i=Kh.exec(e[3]);i&&(e[1]=e[1]||"eval",e[3]=i[1],e[4]=i[2],e[5]="")}let n=e[3],r=e[1]||pr;return[r,n]=Vo(r,n),Di(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}},Gh=[Hh,Jh],Xh=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Qh=t=>{let e=Xh.exec(t);return e?Di(e[2],e[1]||pr,+e[3],e[4]?+e[4]:void 0):void 0},Zh=[Bh,Qh],tm=[Vh,Gh,Zh],em=Oa(...tm),Vo=(t,e)=>{let n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:pr,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},On=1024,Yo="Breadcrumbs",nm=(t={})=>{let e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:Yo,setupOnce(){},setup(n){e.console&&qc(am(n)),e.dom&&Na(sm(n,e.dom)),e.xhr&&yi(om(n)),e.fetch&&_i(lm(n)),e.history&&dn(cm(n)),e.sentry&&n.on&&n.on("beforeSendEvent",im(n))}}},Ko=nm,rm=Pt(Yo,Ko);function im(t){return function(e){$()===t&&Xt({category:`sentry.${e.type==="transaction"?"transaction":"event"}`,event_id:e.event_id,level:e.level,message:Bt(e)},{event:e})}}function sm(t,e){return function(n){if($()!==t)return;let r,i,s=typeof e=="object"?e.serializeAttribute:void 0,a=typeof e=="object"&&typeof e.maxStringLength=="number"?e.maxStringLength:void 0;a&&a>On&&(Dt&&f.warn(`\`dom.maxStringLength\` cannot exceed ${On}, but a value of ${a} was configured. Sentry will use ${On} instead.`),a=On),typeof s=="string"&&(s=[s]);try{let l=n.event,c=um(l)?l.target:l;r=Jt(c,{keyAttrs:s,maxStringLength:a}),i=Ia(c)}catch{r="<unknown>"}if(r.length===0)return;let o={category:`ui.${n.name}`,message:r};i&&(o.data={"ui.component_name":i}),Xt(o,{event:n.event,name:n.name,global:n.global})}}function am(t){return function(e){if($()!==t)return;let n={category:"console",data:{arguments:e.args,logger:"console"},level:yu(e.level),message:Qi(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)n.message=`Assertion failed: ${Qi(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1);else return;Xt(n,{input:e.args,level:e.level})}}function om(t){return function(e){if($()!==t)return;let{startTimestamp:n,endTimestamp:r}=e,i=e.xhr[Ht];if(!n||!r||!i)return;let{method:s,url:a,status_code:o,body:l}=i,c={method:s,url:a,status_code:o},d={xhr:e.xhr,input:l,startTimestamp:n,endTimestamp:r};Xt({category:"xhr",data:c,type:"http"},d)}}function lm(t){return function(e){if($()!==t)return;let{startTimestamp:n,endTimestamp:r}=e;if(r&&!(e.fetchData.url.match(/sentry_key/)&&e.fetchData.method==="POST"))if(e.error){let i=e.fetchData,s={data:e.error,input:e.args,startTimestamp:n,endTimestamp:r};Xt({category:"fetch",data:i,level:"error",type:"http"},s)}else{let i=e.response,s={...e.fetchData,status_code:i&&i.status},a={input:e.args,response:i,startTimestamp:n,endTimestamp:r};Xt({category:"fetch",data:s,type:"http"},a)}}}function cm(t){return function(e){if($()!==t)return;let n=e.from,r=e.to,i=oe(I.location.href),s=n?oe(n):void 0,a=oe(r);(!s||!s.path)&&(s=i),i.protocol===a.protocol&&i.host===a.host&&(r=a.relative),i.protocol===s.protocol&&i.host===s.host&&(n=s.relative),Xt({category:"navigation",data:{from:n,to:r}})}}function um(t){return!!t&&!!t.target}var Jo="Dedupe",dm=()=>{let t;return{name:Jo,setupOnce(){},processEvent(e){if(e.type)return e;try{if(hm(e,t))return Dt&&f.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}},Go=dm,pm=Pt(Jo,Go);function hm(t,e){return e?!!(mm(t,e)||fm(t,e)):!1}function mm(t,e){let n=t.message,r=e.message;return!(!n&&!r||n&&!r||!n&&r||n!==r||!Qo(t,e)||!Xo(t,e))}function fm(t,e){let n=Us(e),r=Us(t);return!(!n||!r||n.type!==r.type||n.value!==r.value||!Qo(t,e)||!Xo(t,e))}function Xo(t,e){let n=Bs(t),r=Bs(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||(n=n,r=r,r.length!==n.length))return!1;for(let i=0;i<r.length;i++){let s=r[i],a=n[i];if(s.filename!==a.filename||s.lineno!==a.lineno||s.colno!==a.colno||s.function!==a.function)return!1}return!0}function Qo(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return n.join("")===r.join("")}catch{return!1}}function Us(t){return t.exception&&t.exception.values&&t.exception.values[0]}function Bs(t){let e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch{return}}var Zo="GlobalHandlers",gm=(t={})=>{let e={onerror:!0,onunhandledrejection:!0,...t};return{name:Zo,setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(ym(n),Hs("onerror")),e.onunhandledrejection&&(vm(n),Hs("onunhandledrejection"))}}},tl=gm,_m=Pt(Zo,tl);function ym(t){Pa(e=>{let{stackParser:n,attachStacktrace:r}=nl();if($()!==t||qo())return;let{msg:i,url:s,line:a,column:o,error:l}=e,c=l===void 0&&It(i)?km(i,s,a,o):el(Ai(n,l||i,void 0,r,!1),s,a,o);c.level="error",Ga(c,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})}function vm(t){$a(e=>{let{stackParser:n,attachStacktrace:r}=nl();if($()!==t||qo())return;let i=Sm(e),s=hi(i)?bm(i):Ai(n,i,void 0,r,!0);s.level="error",Ga(s,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function Sm(t){if(hi(t))return t;let e=t;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return t}function bm(t){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}}function km(t,e,n,r){let i=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i,s=di(t)?t.message:t,a="Error",o=s.match(i);return o&&(a=o[1],s=o[2]),el({exception:{values:[{type:a,value:s}]}},e,n,r)}function el(t,e,n,r){let i=t.exception=t.exception||{},s=i.values=i.values||[],a=s[0]=s[0]||{},o=a.stacktrace=a.stacktrace||{},l=o.frames=o.frames||[],c=isNaN(parseInt(r,10))?void 0:r,d=isNaN(parseInt(n,10))?void 0:n,u=It(e)&&e.length>0?e:Oc();return l.length===0&&l.push({colno:c,filename:u,function:"?",in_app:!0,lineno:d}),t}function Hs(t){Dt&&f.log(`Global Handler attached: ${t}`)}function nl(){let t=$();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}var rl="HttpContext",Tm=()=>({name:rl,setupOnce(){},preprocessEvent(t){if(!I.navigator&&!I.location&&!I.document)return;let e=t.request&&t.request.url||I.location&&I.location.href,{referrer:n}=I.document||{},{userAgent:r}=I.navigator||{},i={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},s={...t.request,...e&&{url:e},headers:i};t.request=s}}),il=Tm,wm=Pt(rl,il),Em="cause",Im=5,sl="LinkedErrors",xm=(t={})=>{let e=t.limit||Im,n=t.key||Em;return{name:sl,setupOnce(){},preprocessEvent(r,i,s){let a=s.getOptions();wa(Wo,a.stackParser,a.maxValueLength,n,e,r,i)}}},al=xm,Cm=Pt(sl,al),Rm=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],ol="TryCatch",Mm=(t={})=>{let e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:ol,setupOnce(){e.setTimeout&&nt(I,"setTimeout",zs),e.setInterval&&nt(I,"setInterval",zs),e.requestAnimationFrame&&nt(I,"requestAnimationFrame",Am),e.XMLHttpRequest&&"XMLHttpRequest"in I&&nt(XMLHttpRequest.prototype,"send",Dm);let n=e.eventTarget;n&&(Array.isArray(n)?n:Rm).forEach(Nm)}}},ll=Mm,Om=Pt(ol,ll);function zs(t){return function(...e){let n=e[0];return e[0]=Le(n,{mechanism:{data:{function:Lt(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Am(t){return function(e){return t.apply(this,[Le(e,{mechanism:{data:{function:"requestAnimationFrame",handler:Lt(t)},handled:!1,type:"instrument"}})])}}function Dm(t){return function(...e){let n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&nt(n,r,function(i){let s={mechanism:{data:{function:r,handler:Lt(i)},handled:!1,type:"instrument"}},a=gi(i);return a&&(s.mechanism.data.handler=Lt(a)),Le(i,s)})}),t.apply(this,e)}}function Nm(t){let e=I,n=e[t]&&e[t].prototype;!n||!n.hasOwnProperty||!n.hasOwnProperty("addEventListener")||(nt(n,"addEventListener",function(r){return function(i,s,a){try{typeof s.handleEvent=="function"&&(s.handleEvent=Le(s.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Lt(s),target:t},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[i,Le(s,{mechanism:{data:{function:"addEventListener",handler:Lt(s),target:t},handled:!1,type:"instrument"}}),a])}}),nt(n,"removeEventListener",function(r){return function(i,s,a){let o=s;try{let l=o&&o.__sentry_wrapped__;l&&r.call(this,i,l,a)}catch{}return r.call(this,i,o,a)}}))}var Lm=[vo(),bo(),ll(),Ko(),tl(),al(),Go(),il()];function cl(t){return[...Lm]}function Pm(t={}){t.defaultIntegrations===void 0&&(t.defaultIntegrations=cl()),t.release===void 0&&(typeof __SENTRY_RELEASE__=="string"&&(t.release=__SENTRY_RELEASE__),I.SENTRY_RELEASE&&I.SENTRY_RELEASE.id&&(t.release=I.SENTRY_RELEASE.id)),t.autoSessionTracking===void 0&&(t.autoSessionTracking=!0),t.sendClientReports===void 0&&(t.sendClientReports=!0);let e={...t,stackParser:Hc(t.stackParser||em),integrations:Od(t),transport:t.transport||(La()?$h:jh)};Ud(Nh,e),t.autoSessionTracking&&$m()}function $m(){if(typeof I.document>"u"){Dt&&f.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}gs({ignoreDuration:!0}),_s(),dn(({from:t,to:e})=>{t!==void 0&&t!==e&&(gs({ignoreDuration:!0}),_s())})}var ul={};va(ul,{Breadcrumbs:()=>rm,Dedupe:()=>pm,GlobalHandlers:()=>_m,HttpContext:()=>wm,LinkedErrors:()=>Cm,TryCatch:()=>Om});var H=A,Ni="sentryReplaySession",Fm="replay_event",Li="Unable to send Replay",jm=3e5,Um=9e5,Bm=5e3,Hm=5500,zm=6e4,qm=5e3,Wm=3,qs=15e4,An=5e3,Vm=3e3,Ym=300,Pi=2e7,Km=4999,Jm=15e3,Ws=36e5;function Gm(t,e){return t??e()}function Wn(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}var tt;(function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"})(tt||(tt={}));function Xm(t){return t.nodeType===t.ELEMENT_NODE}function en(t){let e=Wn([t,"optionalAccess",n=>n.host]);return Wn([e,"optionalAccess",n=>n.shadowRoot])===t}function nn(t){return Object.prototype.toString.call(t)==="[object ShadowRoot]"}function Qm(t){return t.includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t}function Zm(t){let{cssText:e}=t;if(e.split('"').length<3)return e;let n=["@import",`url(${JSON.stringify(t.href)})`];return t.layerName===""?n.push("layer"):t.layerName&&n.push(`layer(${t.layerName})`),t.supportsText&&n.push(`supports(${t.supportsText})`),t.media.length&&n.push(t.media.mediaText),n.join(" ")+";"}function Vn(t){try{let e=t.rules||t.cssRules;return e?Qm(Array.from(e,dl).join("")):null}catch{return null}}function dl(t){let e;if(ef(t))try{e=Vn(t.styleSheet)||Zm(t)}catch{}else if(nf(t)&&t.selectorText.includes(":"))return tf(t.cssText);return e||t.cssText}function tf(t){let e=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return t.replace(e,"$1\\$2")}function ef(t){return"styleSheet"in t}function nf(t){return"selectorText"in t}var pl=class{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(t){if(!t)return-1;let e=Wn([this,"access",n=>n.getMeta,"call",n=>n(t),"optionalAccess",n=>n.id]);return Gm(e,()=>-1)}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){let e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach(n=>this.removeNodeFromMap(n))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){let n=e.id;this.idNodeMap.set(n,t),this.nodeMetaMap.set(t,e)}replace(t,e){let n=this.getNode(t);if(n){let r=this.nodeMetaMap.get(n);r&&this.nodeMetaMap.set(e,r)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}};function rf(){return new pl}function hr({maskInputOptions:t,tagName:e,type:n}){return e==="OPTION"&&(e="SELECT"),!!(t[e.toLowerCase()]||n&&t[n]||n==="password"||e==="INPUT"&&!n&&t.text)}function on({isMasked:t,element:e,value:n,maskInputFn:r}){let i=n||"";return t?(r&&(i=r(i,e)),"*".repeat(i.length)):i}function Pe(t){return t.toLowerCase()}function ni(t){return t.toUpperCase()}var Vs="__rrweb_original__";function sf(t){let e=t.getContext("2d");if(!e)return!0;let n=50;for(let r=0;r<t.width;r+=n)for(let i=0;i<t.height;i+=n){let s=e.getImageData,a=Vs in s?s[Vs]:s;if(new Uint32Array(a.call(e,r,i,Math.min(n,t.width-r),Math.min(n,t.height-i)).data.buffer).some(o=>o!==0))return!1}return!0}function $i(t){let e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?Pe(e):null}function Yn(t,e,n){return e==="INPUT"&&(n==="radio"||n==="checkbox")?t.getAttribute("value")||"":t.value}var af=1,of=new RegExp("[^a-z0-9-_:]"),ln=-2;function Fi(){return af++}function lf(t){if(t instanceof HTMLFormElement)return"form";let e=Pe(t.tagName);return of.test(e)?"div":e}function cf(t){let e="";return t.indexOf("//")>-1?e=t.split("/").slice(0,3).join("/"):e=t.split("/")[0],e=e.split("?")[0],e}var ge,Ys,uf=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,df=/^(?:[a-z+]+:)?\/\//i,pf=/^www\..*/i,hf=/^(data:)([^,]*),(.*)/i;function Kn(t,e){return(t||"").replace(uf,(n,r,i,s,a,o)=>{let l=i||a||o,c=r||s||"";if(!l)return n;if(df.test(l)||pf.test(l))return`url(${c}${l}${c})`;if(hf.test(l))return`url(${c}${l}${c})`;if(l[0]==="/")return`url(${c}${cf(e)+l}${c})`;let d=e.split("/"),u=l.split("/");d.pop();for(let p of u)p!=="."&&(p===".."?d.pop():d.push(p));return`url(${c}${d.join("/")}${c})`})}var mf=/^[^ \t\n\r\u000c]+/,ff=/^[, \t\n\r\u000c]+/;function gf(t,e){if(e.trim()==="")return e;let n=0;function r(s){let a,o=s.exec(e.substring(n));return o?(a=o[0],n+=a.length,a):""}let i=[];for(;r(ff),!(n>=e.length);){let s=r(mf);if(s.slice(-1)===",")s=Jn(t,s.substring(0,s.length-1)),i.push(s);else{let a="";s=Jn(t,s);let o=!1;for(;;){let l=e.charAt(n);if(l===""){i.push((s+a).trim());break}else if(o)l===")"&&(o=!1);else if(l===","){n+=1,i.push((s+a).trim());break}else l==="("&&(o=!0);a+=l,n+=1}}}return i.join(", ")}function Jn(t,e){if(!e||e.trim()==="")return e;let n=t.createElement("a");return n.href=e,n.href}function _f(t){return!!(t.tagName==="svg"||t.ownerSVGElement)}function ji(){let t=document.createElement("a");return t.href="",t.href}function hl(t,e,n,r,i,s){return r&&(n==="src"||n==="href"&&!(e==="use"&&r[0]==="#")||n==="xlink:href"&&r[0]!=="#"||n==="background"&&(e==="table"||e==="td"||e==="th")?Jn(t,r):n==="srcset"?gf(t,r):n==="style"?Kn(r,ji()):e==="object"&&n==="data"?Jn(t,r):typeof s=="function"?s(n,r,i):r)}function ml(t,e,n){return(t==="video"||t==="audio")&&e==="autoplay"}function yf(t,e,n,r){try{if(r&&t.matches(r))return!1;if(typeof e=="string"){if(t.classList.contains(e))return!0}else for(let i=t.classList.length;i--;){let s=t.classList[i];if(e.test(s))return!0}if(n)return t.matches(n)}catch{}return!1}function vf(t,e){for(let n=t.classList.length;n--;){let r=t.classList[n];if(e.test(r))return!0}return!1}function ae(t,e,n=1/0,r=0){return!t||t.nodeType!==t.ELEMENT_NODE||r>n?-1:e(t)?r:ae(t.parentNode,e,n,r+1)}function Se(t,e){return n=>{let r=n;if(r===null)return!1;try{if(t){if(typeof t=="string"){if(r.matches(`.${t}`))return!0}else if(vf(r,t))return!0}return!!(e&&r.matches(e))}catch{return!1}}}function $e(t,e,n,r,i,s){try{let a=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(a===null)return!1;if(a.tagName==="INPUT"){let c=a.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(c))return!0}let o=-1,l=-1;if(s){if(l=ae(a,Se(r,i)),l<0)return!0;o=ae(a,Se(e,n),l>=0?l:1/0)}else{if(o=ae(a,Se(e,n)),o<0)return!1;l=ae(a,Se(r,i),o>=0?o:1/0)}return o>=0?l>=0?o<=l:!0:l>=0?!1:!!s}catch{}return!!s}function Sf(t,e,n){let r=t.contentWindow;if(!r)return;let i=!1,s;try{s=r.document.readyState}catch{return}if(s!=="complete"){let o=setTimeout(()=>{i||(e(),i=!0)},n);t.addEventListener("load",()=>{clearTimeout(o),i=!0,e()});return}let a="about:blank";if(r.location.href!==a||t.src===a||t.src==="")return setTimeout(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}function bf(t,e,n){let r=!1,i;try{i=t.sheet}catch{return}if(i)return;let s=setTimeout(()=>{r||(e(),r=!0)},n);t.addEventListener("load",()=>{clearTimeout(s),r=!0,e()})}function kf(t,e){let{doc:n,mirror:r,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:o,maskAttributeFn:l,maskTextClass:c,unmaskTextClass:d,maskTextSelector:u,unmaskTextSelector:p,inlineStylesheet:h,maskInputOptions:m={},maskTextFn:g,maskInputFn:_,dataURLOptions:y={},inlineImages:S,recordCanvas:w,keepIframeSrcFn:D,newlyAddedElement:F=!1}=e,b=Tf(n,r);switch(t.nodeType){case t.DOCUMENT_NODE:return t.compatMode!=="CSS1Compat"?{type:tt.Document,childNodes:[],compatMode:t.compatMode}:{type:tt.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:tt.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:b};case t.ELEMENT_NODE:return Ef(t,{doc:n,blockClass:i,blockSelector:s,unblockSelector:a,inlineStylesheet:h,maskAttributeFn:l,maskInputOptions:m,maskInputFn:_,dataURLOptions:y,inlineImages:S,recordCanvas:w,keepIframeSrcFn:D,newlyAddedElement:F,rootId:b,maskAllText:o,maskTextClass:c,unmaskTextClass:d,maskTextSelector:u,unmaskTextSelector:p});case t.TEXT_NODE:return wf(t,{maskAllText:o,maskTextClass:c,unmaskTextClass:d,maskTextSelector:u,unmaskTextSelector:p,maskTextFn:g,maskInputOptions:m,maskInputFn:_,rootId:b});case t.CDATA_SECTION_NODE:return{type:tt.CDATA,textContent:"",rootId:b};case t.COMMENT_NODE:return{type:tt.Comment,textContent:t.textContent||"",rootId:b};default:return!1}}function Tf(t,e){if(!e.hasNode(t))return;let n=e.getId(t);return n===1?void 0:n}function wf(t,e){let{maskAllText:n,maskTextClass:r,unmaskTextClass:i,maskTextSelector:s,unmaskTextSelector:a,maskTextFn:o,maskInputOptions:l,maskInputFn:c,rootId:d}=e,u=t.parentNode&&t.parentNode.tagName,p=t.textContent,h=u==="STYLE"?!0:void 0,m=u==="SCRIPT"?!0:void 0,g=u==="TEXTAREA"?!0:void 0;if(h&&p){try{t.nextSibling||t.previousSibling||Wn([t,"access",y=>y.parentNode,"access",y=>y.sheet,"optionalAccess",y=>y.cssRules])&&(p=Vn(t.parentNode.sheet))}catch(y){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${y}`,t)}p=Kn(p,ji())}m&&(p="SCRIPT_PLACEHOLDER");let _=$e(t,r,s,i,a,n);if(!h&&!m&&!g&&p&&_&&(p=o?o(p,t.parentElement):p.replace(/[\S]/g,"*")),g&&p&&(l.textarea||_)&&(p=c?c(p,t.parentNode):p.replace(/[\S]/g,"*")),u==="OPTION"&&p){let y=hr({type:null,tagName:u,maskInputOptions:l});p=on({isMasked:$e(t,r,s,i,a,y),element:t,value:p,maskInputFn:c})}return{type:tt.Text,textContent:p||"",isStyle:h,rootId:d}}function Ef(t,e){let{doc:n,blockClass:r,blockSelector:i,unblockSelector:s,inlineStylesheet:a,maskInputOptions:o={},maskAttributeFn:l,maskInputFn:c,dataURLOptions:d={},inlineImages:u,recordCanvas:p,keepIframeSrcFn:h,newlyAddedElement:m=!1,rootId:g,maskAllText:_,maskTextClass:y,unmaskTextClass:S,maskTextSelector:w,unmaskTextSelector:D}=e,F=yf(t,r,i,s),b=lf(t),v={},G=t.attributes.length;for(let E=0;E<G;E++){let x=t.attributes[E];x.name&&!ml(b,x.name,x.value)&&(v[x.name]=hl(n,b,Pe(x.name),x.value,t,l))}if(b==="link"&&a){let E=Array.from(n.styleSheets).find(W=>W.href===t.href),x=null;E&&(x=Vn(E)),x&&(delete v.rel,delete v.href,v._cssText=Kn(x,E.href))}if(b==="style"&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){let E=Vn(t.sheet);E&&(v._cssText=Kn(E,ji()))}if(b==="input"||b==="textarea"||b==="select"||b==="option"){let E=t,x=$i(E),W=Yn(E,ni(b),x),V=E.checked;if(x!=="submit"&&x!=="button"&&W){let dt=$e(E,y,w,S,D,hr({type:x,tagName:ni(b),maskInputOptions:o}));v.value=on({isMasked:dt,element:E,value:W,maskInputFn:c})}V&&(v.checked=V)}if(b==="option"&&(t.selected&&!o.select?v.selected=!0:delete v.selected),b==="canvas"&&p){if(t.__context==="2d")sf(t)||(v.rr_dataURL=t.toDataURL(d.type,d.quality));else if(!("__context"in t)){let E=t.toDataURL(d.type,d.quality),x=document.createElement("canvas");x.width=t.width,x.height=t.height;let W=x.toDataURL(d.type,d.quality);E!==W&&(v.rr_dataURL=E)}}if(b==="img"&&u){ge||(ge=n.createElement("canvas"),Ys=ge.getContext("2d"));let E=t,x=E.crossOrigin;E.crossOrigin="anonymous";let W=()=>{E.removeEventListener("load",W);try{ge.width=E.naturalWidth,ge.height=E.naturalHeight,Ys.drawImage(E,0,0),v.rr_dataURL=ge.toDataURL(d.type,d.quality)}catch(V){console.warn(`Cannot inline img src=${E.currentSrc}! Error: ${V}`)}x?v.crossOrigin=x:E.removeAttribute("crossorigin")};E.complete&&E.naturalWidth!==0?W():E.addEventListener("load",W)}if((b==="audio"||b==="video")&&(v.rr_mediaState=t.paused?"paused":"played",v.rr_mediaCurrentTime=t.currentTime),m||(t.scrollLeft&&(v.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(v.rr_scrollTop=t.scrollTop)),F){let{width:E,height:x}=t.getBoundingClientRect();v={class:v.class,rr_width:`${E}px`,rr_height:`${x}px`}}b==="iframe"&&!h(v.src)&&(t.contentDocument||(v.rr_src=v.src),delete v.src);let z;try{customElements.get(b)&&(z=!0)}catch{}return{type:tt.Element,tagName:b,attributes:v,childNodes:[],isSVG:_f(t)||void 0,needBlock:F,rootId:g,isCustom:z}}function B(t){return t==null?"":t.toLowerCase()}function If(t,e){return!!(e.comment&&t.type===tt.Comment||t.type===tt.Element&&(e.script&&(t.tagName==="script"||t.tagName==="link"&&(t.attributes.rel==="preload"||t.attributes.rel==="modulepreload")&&t.attributes.as==="script"||t.tagName==="link"&&t.attributes.rel==="prefetch"&&typeof t.attributes.href=="string"&&t.attributes.href.endsWith(".js"))||e.headFavicon&&(t.tagName==="link"&&t.attributes.rel==="shortcut icon"||t.tagName==="meta"&&(B(t.attributes.name).match(/^msapplication-tile(image|color)$/)||B(t.attributes.name)==="application-name"||B(t.attributes.rel)==="icon"||B(t.attributes.rel)==="apple-touch-icon"||B(t.attributes.rel)==="shortcut icon"))||t.tagName==="meta"&&(e.headMetaDescKeywords&&B(t.attributes.name).match(/^description|keywords$/)||e.headMetaSocial&&(B(t.attributes.property).match(/^(og|twitter|fb):/)||B(t.attributes.name).match(/^(og|twitter):/)||B(t.attributes.name)==="pinterest")||e.headMetaRobots&&(B(t.attributes.name)==="robots"||B(t.attributes.name)==="googlebot"||B(t.attributes.name)==="bingbot")||e.headMetaHttpEquiv&&t.attributes["http-equiv"]!==void 0||e.headMetaAuthorship&&(B(t.attributes.name)==="author"||B(t.attributes.name)==="generator"||B(t.attributes.name)==="framework"||B(t.attributes.name)==="publisher"||B(t.attributes.name)==="progid"||B(t.attributes.property).match(/^article:/)||B(t.attributes.property).match(/^product:/))||e.headMetaVerification&&(B(t.attributes.name)==="google-site-verification"||B(t.attributes.name)==="yandex-verification"||B(t.attributes.name)==="csrf-token"||B(t.attributes.name)==="p:domain_verify"||B(t.attributes.name)==="verify-v1"||B(t.attributes.name)==="verification"||B(t.attributes.name)==="shopify-checkout-api-token"))))}function be(t,e){let{doc:n,mirror:r,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:o,maskTextClass:l,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:u,skipChild:p=!1,inlineStylesheet:h=!0,maskInputOptions:m={},maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:S,dataURLOptions:w={},inlineImages:D=!1,recordCanvas:F=!1,onSerialize:b,onIframeLoad:v,iframeLoadTimeout:G=5e3,onStylesheetLoad:z,stylesheetLoadTimeout:E=5e3,keepIframeSrcFn:x=()=>!1,newlyAddedElement:W=!1}=e,{preserveWhiteSpace:V=!0}=e,dt=kf(t,{doc:n,mirror:r,blockClass:i,blockSelector:s,maskAllText:o,unblockSelector:a,maskTextClass:l,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:u,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,dataURLOptions:w,inlineImages:D,recordCanvas:F,keepIframeSrcFn:x,newlyAddedElement:W});if(!dt)return console.warn(t,"not serialized"),null;let pt;r.hasNode(t)?pt=r.getId(t):If(dt,S)||!V&&dt.type===tt.Text&&!dt.isStyle&&!dt.textContent.replace(/^\s+|\s+$/gm,"").length?pt=ln:pt=Fi();let j=Object.assign(dt,{id:pt});if(r.add(t,j),pt===ln)return null;b&&b(t);let ht=!p;if(j.type===tt.Element){ht=ht&&!j.needBlock,delete j.needBlock;let Q=t.shadowRoot;Q&&nn(Q)&&(j.isShadowHost=!0)}if((j.type===tt.Document||j.type===tt.Element)&&ht){S.headWhitespace&&j.type===tt.Element&&j.tagName==="head"&&(V=!1);let Q={doc:n,mirror:r,blockClass:i,blockSelector:s,maskAllText:o,unblockSelector:a,maskTextClass:l,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:u,skipChild:p,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:S,dataURLOptions:w,inlineImages:D,recordCanvas:F,preserveWhiteSpace:V,onSerialize:b,onIframeLoad:v,iframeLoadTimeout:G,onStylesheetLoad:z,stylesheetLoadTimeout:E,keepIframeSrcFn:x};for(let mt of Array.from(t.childNodes)){let ft=be(mt,Q);ft&&j.childNodes.push(ft)}if(Xm(t)&&t.shadowRoot)for(let mt of Array.from(t.shadowRoot.childNodes)){let ft=be(mt,Q);ft&&(nn(t.shadowRoot)&&(ft.isShadow=!0),j.childNodes.push(ft))}}return t.parentNode&&en(t.parentNode)&&nn(t.parentNode)&&(j.isShadow=!0),j.type===tt.Element&&j.tagName==="iframe"&&Sf(t,()=>{let Q=t.contentDocument;if(Q&&v){let mt=be(Q,{doc:Q,mirror:r,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:o,maskTextClass:l,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:S,dataURLOptions:w,inlineImages:D,recordCanvas:F,preserveWhiteSpace:V,onSerialize:b,onIframeLoad:v,iframeLoadTimeout:G,onStylesheetLoad:z,stylesheetLoadTimeout:E,keepIframeSrcFn:x});mt&&v(t,mt)}},G),j.type===tt.Element&&j.tagName==="link"&&j.attributes.rel==="stylesheet"&&bf(t,()=>{if(z){let Q=be(t,{doc:n,mirror:r,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:o,maskTextClass:l,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:S,dataURLOptions:w,inlineImages:D,recordCanvas:F,preserveWhiteSpace:V,onSerialize:b,onIframeLoad:v,iframeLoadTimeout:G,onStylesheetLoad:z,stylesheetLoadTimeout:E,keepIframeSrcFn:x});Q&&z(t,Q)}},E),j}function xf(t,e){let{mirror:n=new pl,blockClass:r="rr-block",blockSelector:i=null,unblockSelector:s=null,maskAllText:a=!1,maskTextClass:o="rr-mask",unmaskTextClass:l=null,maskTextSelector:c=null,unmaskTextSelector:d=null,inlineStylesheet:u=!0,inlineImages:p=!1,recordCanvas:h=!1,maskAllInputs:m=!1,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOM:S=!1,dataURLOptions:w,preserveWhiteSpace:D,onSerialize:F,onIframeLoad:b,iframeLoadTimeout:v,onStylesheetLoad:G,stylesheetLoadTimeout:z,keepIframeSrcFn:E=()=>!1}=e||{};return be(t,{doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,maskAllText:a,maskTextClass:o,unmaskTextClass:l,maskTextSelector:c,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:u,maskInputOptions:m===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:m===!1?{}:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:S===!0||S==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:S==="all",headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:S===!1?{}:S,dataURLOptions:w,inlineImages:p,recordCanvas:h,preserveWhiteSpace:D,onSerialize:F,onIframeLoad:b,iframeLoadTimeout:v,onStylesheetLoad:G,stylesheetLoadTimeout:z,keepIframeSrcFn:E,newlyAddedElement:!1})}function Ft(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}function at(t,e,n=document){let r={capture:!0,passive:!0};return n.addEventListener(t,e,r),()=>n.removeEventListener(t,e,r)}var ye=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`,Ks={map:{},getId(){return console.error(ye),-1},getNode(){return console.error(ye),null},removeNodeFromMap(){console.error(ye)},has(){return console.error(ye),!1},reset(){console.error(ye)}};typeof window<"u"&&window.Proxy&&window.Reflect&&(Ks=new Proxy(Ks,{get(t,e,n){return e==="map"&&console.error(ye),Reflect.get(t,e,n)}}));function cn(t,e,n={}){let r=null,i=0;return function(...s){let a=Date.now();!i&&n.leading===!1&&(i=a);let o=e-(a-i),l=this;o<=0||o>e?(r&&(Nf(r),r=null),i=a,t.apply(l,s)):!r&&n.trailing!==!1&&(r=mr(()=>{i=n.leading===!1?0:Date.now(),r=null,t.apply(l,s)},o))}}function fl(t,e,n,r,i=window){let s=i.Object.getOwnPropertyDescriptor(t,e);return i.Object.defineProperty(t,e,r?n:{set(a){mr(()=>{n.set.call(this,a)},0),s&&s.set&&s.set.call(this,a)}}),()=>fl(t,e,s||{},!0)}function Ui(t,e,n){try{if(!(e in t))return()=>{};let r=t[e],i=n(r);return typeof i=="function"&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=i,()=>{t[e]=r}}catch{return()=>{}}}var Gn=Date.now;/[1-9][0-9]{12}/.test(Date.now().toString())||(Gn=()=>new Date().getTime());function gl(t){let e=t.document;return{left:e.scrollingElement?e.scrollingElement.scrollLeft:t.pageXOffset!==void 0?t.pageXOffset:Ft([e,"optionalAccess",n=>n.documentElement,"access",n=>n.scrollLeft])||Ft([e,"optionalAccess",n=>n.body,"optionalAccess",n=>n.parentElement,"optionalAccess",n=>n.scrollLeft])||Ft([e,"optionalAccess",n=>n.body,"optionalAccess",n=>n.scrollLeft])||0,top:e.scrollingElement?e.scrollingElement.scrollTop:t.pageYOffset!==void 0?t.pageYOffset:Ft([e,"optionalAccess",n=>n.documentElement,"access",n=>n.scrollTop])||Ft([e,"optionalAccess",n=>n.body,"optionalAccess",n=>n.parentElement,"optionalAccess",n=>n.scrollTop])||Ft([e,"optionalAccess",n=>n.body,"optionalAccess",n=>n.scrollTop])||0}}function _l(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function yl(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function vl(t){return t?t.nodeType===t.ELEMENT_NODE?t:t.parentElement:null}function yt(t,e,n,r,i){if(!t)return!1;let s=vl(t);if(!s)return!1;let a=Se(e,n);if(!i){let c=r&&s.matches(r);return a(s)&&!c}let o=ae(s,a),l=-1;return o<0?!1:(r&&(l=ae(s,Se(null,r))),o>-1&&l<0?!0:o<l)}function Cf(t,e){return e.getId(t)!==-1}function Dr(t,e){return e.getId(t)===ln}function Sl(t,e){if(en(t))return!1;let n=e.getId(t);return e.has(n)?t.parentNode&&t.parentNode.nodeType===t.DOCUMENT_NODE?!1:t.parentNode?Sl(t.parentNode,e):!0:!0}function ri(t){return!!t.changedTouches}function Rf(t=window){"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let n=e[0];if(!(0 in e))throw new TypeError("1 argument is required");do if(this===n)return!0;while(n=n&&n.parentNode);return!1})}function bl(t,e){return!!(t.nodeName==="IFRAME"&&e.getMeta(t))}function kl(t,e){return!!(t.nodeName==="LINK"&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&t.getAttribute("rel")==="stylesheet"&&e.getMeta(t))}function ii(t){return!!Ft([t,"optionalAccess",e=>e.shadowRoot])}var Mf=class{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){return Pu(this.styleIDMap.get(t),()=>-1)}has(t){return this.styleIDMap.has(t)}add(t,e){if(this.has(t))return this.getId(t);let n;return e===void 0?n=this.id++:n=e,this.styleIDMap.set(t,n),this.idStyleMap.set(n,t),n}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}};function Tl(t){let e=null;return Ft([t,"access",n=>n.getRootNode,"optionalCall",n=>n(),"optionalAccess",n=>n.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&t.getRootNode().host&&(e=t.getRootNode().host),e}function Of(t){let e=t,n;for(;n=Tl(e);)e=n;return e}function Af(t){let e=t.ownerDocument;if(!e)return!1;let n=Of(t);return e.contains(n)}function wl(t){let e=t.ownerDocument;return e?e.contains(t)||Af(t):!1}var Js={};function Bi(t){let e=Js[t];if(e)return e;let n=window.document,r=window[t];if(n&&typeof n.createElement=="function")try{let i=n.createElement("iframe");i.hidden=!0,n.head.appendChild(i);let s=i.contentWindow;s&&s[t]&&(r=s[t]),n.head.removeChild(i)}catch{}return Js[t]=r.bind(window)}function Df(...t){return Bi("requestAnimationFrame")(...t)}function mr(...t){return Bi("setTimeout")(...t)}function Nf(...t){return Bi("clearTimeout")(...t)}var C=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(C||{}),R=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(R||{}),it=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(it||{}),Rt=(t=>(t[t.Mouse=0]="Mouse",t[t.Pen=1]="Pen",t[t.Touch=2]="Touch",t))(Rt||{});function Lf(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}function Gs(t){return"__ln"in t}var Pf=class{constructor(){this.length=0,this.head=null,this.tail=null}get(t){if(t>=this.length)throw new Error("Position outside of list range");let e=this.head;for(let n=0;n<t;n++)e=Lf([e,"optionalAccess",r=>r.next])||null;return e}addNode(t){let e={value:t,previous:null,next:null};if(t.__ln=e,t.previousSibling&&Gs(t.previousSibling)){let n=t.previousSibling.__ln.next;e.next=n,e.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=e,n&&(n.previous=e)}else if(t.nextSibling&&Gs(t.nextSibling)&&t.nextSibling.__ln.previous){let n=t.nextSibling.__ln.previous;e.previous=n,e.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=e,n&&(n.next=e)}else this.head&&(this.head.previous=e),e.next=this.head,this.head=e;e.next===null&&(this.tail=e),this.length++}removeNode(t){let e=t.__ln;this.head&&(e.previous?(e.previous.next=e.next,e.next?e.next.previous=e.previous:this.tail=e.previous):(this.head=e.next,this.head?this.head.previous=null:this.tail=null),t.__ln&&delete t.__ln,this.length--)}},Xs=(t,e)=>`${t}@${e}`,$f=class{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=t=>{t.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;let t=[],e=new Set,n=new Pf,r=o=>{let l=o,c=ln;for(;c===ln;)l=l&&l.nextSibling,c=l&&this.mirror.getId(l);return c},i=o=>{if(!o.parentNode||!wl(o))return;let l=en(o.parentNode)?this.mirror.getId(Tl(o)):this.mirror.getId(o.parentNode),c=r(o);if(l===-1||c===-1)return n.addNode(o);let d=be(o,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:u=>{bl(u,this.mirror)&&this.iframeManager.addIframe(u),kl(u,this.mirror)&&this.stylesheetManager.trackLinkElement(u),ii(o)&&this.shadowDomManager.addShadowRoot(o.shadowRoot,this.doc)},onIframeLoad:(u,p)=>{this.iframeManager.attachIframe(u,p),this.shadowDomManager.observeAttachShadow(u)},onStylesheetLoad:(u,p)=>{this.stylesheetManager.attachLinkElement(u,p)}});d&&(t.push({parentId:l,nextId:c,node:d}),e.add(d.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let o of this.movedSet)Qs(this.removes,o,this.mirror)&&!this.movedSet.has(o.parentNode)||i(o);for(let o of this.addedSet)!Zs(this.droppedSet,o)&&!Qs(this.removes,o,this.mirror)||Zs(this.movedSet,o)?i(o):this.droppedSet.add(o);let s=null;for(;n.length;){let o=null;if(s){let l=this.mirror.getId(s.value.parentNode),c=r(s.value);l!==-1&&c!==-1&&(o=s)}if(!o){let l=n.tail;for(;l;){let c=l;if(l=l.previous,c){let d=this.mirror.getId(c.value.parentNode);if(r(c.value)===-1)continue;if(d!==-1){o=c;break}else{let u=c.value;if(u.parentNode&&u.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let p=u.parentNode.host;if(this.mirror.getId(p)!==-1){o=c;break}}}}}}if(!o){for(;n.head;)n.removeNode(n.head.value);break}s=o.previous,n.removeNode(o.value),i(o.value)}let a={texts:this.texts.map(o=>({id:this.mirror.getId(o.node),value:o.value})).filter(o=>!e.has(o.id)).filter(o=>this.mirror.has(o.id)),attributes:this.attributes.map(o=>{let{attributes:l}=o;if(typeof l.style=="string"){let c=JSON.stringify(o.styleDiff),d=JSON.stringify(o._unchangedStyles);c.length<l.style.length&&(c+d).split("var(").length===l.style.split("var(").length&&(l.style=o.styleDiff)}return{id:this.mirror.getId(o.node),attributes:l}}).filter(o=>!e.has(o.id)).filter(o=>this.mirror.has(o.id)),removes:this.removes,adds:t};!a.texts.length&&!a.attributes.length&&!a.removes.length&&!a.adds.length||(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(a))},this.processMutation=t=>{if(!Dr(t.target,this.mirror))switch(t.type){case"characterData":{let e=t.target.textContent;!yt(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&e!==t.oldValue&&this.texts.push({value:$e(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&e?this.maskTextFn?this.maskTextFn(e,vl(t.target)):e.replace(/[\S]/g,"*"):e,node:t.target});break}case"attributes":{let e=t.target,n=t.attributeName,r=t.target.getAttribute(n);if(n==="value"){let s=$i(e),a=e.tagName;r=Yn(e,a,s);let o=hr({maskInputOptions:this.maskInputOptions,tagName:a,type:s}),l=$e(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,o);r=on({isMasked:l,element:e,value:r,maskInputFn:this.maskInputFn})}if(yt(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||r===t.oldValue)return;let i=this.attributeMap.get(t.target);if(e.tagName==="IFRAME"&&n==="src"&&!this.keepIframeSrcFn(r))if(!e.contentDocument)n="rr_src";else return;if(i||(i={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(t.target,i)),n==="type"&&e.tagName==="INPUT"&&(t.oldValue||"").toLowerCase()==="password"&&e.setAttribute("data-rr-is-password","true"),!ml(e.tagName,n)&&(i.attributes[n]=hl(this.doc,Pe(e.tagName),Pe(n),r,e,this.maskAttributeFn),n==="style")){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch{this.unattachedDoc=this.doc}let s=this.unattachedDoc.createElement("span");t.oldValue&&s.setAttribute("style",t.oldValue);for(let a of Array.from(e.style)){let o=e.style.getPropertyValue(a),l=e.style.getPropertyPriority(a);o!==s.style.getPropertyValue(a)||l!==s.style.getPropertyPriority(a)?l===""?i.styleDiff[a]=o:i.styleDiff[a]=[o,l]:i._unchangedStyles[a]=[o,l]}for(let a of Array.from(s.style))e.style.getPropertyValue(a)===""&&(i.styleDiff[a]=!1)}break}case"childList":{if(yt(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;t.addedNodes.forEach(e=>this.genAdds(e,t.target)),t.removedNodes.forEach(e=>{let n=this.mirror.getId(e),r=en(t.target)?this.mirror.getId(t.target.host):this.mirror.getId(t.target);yt(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||Dr(e,this.mirror)||!Cf(e,this.mirror)||(this.addedSet.has(e)?(si(this.addedSet,e),this.droppedSet.add(e)):this.addedSet.has(t.target)&&n===-1||Sl(t.target,this.mirror)||(this.movedSet.has(e)&&this.movedMap[Xs(n,r)]?si(this.movedSet,e):this.removes.push({parentId:r,id:n,isShadow:en(t.target)&&nn(t.target)?!0:void 0})),this.mapRemoves.push(e))});break}}},this.genAdds=(t,e)=>{if(!this.processedNodeManager.inOtherBuffer(t,this)&&!(this.addedSet.has(t)||this.movedSet.has(t))){if(this.mirror.hasNode(t)){if(Dr(t,this.mirror))return;this.movedSet.add(t);let n=null;e&&this.mirror.hasNode(e)&&(n=this.mirror.getId(e)),n&&n!==-1&&(this.movedMap[Xs(this.mirror.getId(t),n)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);yt(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(t.childNodes.forEach(n=>this.genAdds(n)),ii(t)&&t.shadowRoot.childNodes.forEach(n=>{this.processedNodeManager.add(n,this),this.genAdds(n,t)}))}}}init(t){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(e=>{this[e]=t[e]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}};function si(t,e){t.delete(e),e.childNodes.forEach(n=>si(t,n))}function Qs(t,e,n){return t.length===0?!1:El(t,e,n)}function El(t,e,n){let{parentNode:r}=e;if(!r)return!1;let i=n.getId(r);return t.some(s=>s.id===i)?!0:El(t,r,n)}function Zs(t,e){return t.size===0?!1:Il(t,e)}function Il(t,e){let{parentNode:n}=e;return n?t.has(n)?!0:Il(t,n):!1}var rn;function Ff(t){rn=t}function jf(){rn=void 0}var O=t=>rn?(...e)=>{try{return t(...e)}catch(n){if(rn&&rn(n)===!0)return()=>{};throw n}}:t;function Tt(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}var ke=[];function vn(t){try{if("composedPath"in t){let e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0]}catch{}return t&&t.target}function xl(t,e){let n=new $f;ke.push(n),n.init(t);let r=window.MutationObserver||window.__rrMutationObserver,i=Tt([window,"optionalAccess",a=>a.Zone,"optionalAccess",a=>a.__symbol__,"optionalCall",a=>a("MutationObserver")]);i&&window[i]&&(r=window[i]);let s=new r(O(a=>{t.onMutation&&t.onMutation(a)===!1||n.processMutations.bind(n)(a)}));return s.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),s}function Uf({mousemoveCb:t,sampling:e,doc:n,mirror:r}){if(e.mousemove===!1)return()=>{};let i=typeof e.mousemove=="number"?e.mousemove:50,s=typeof e.mousemoveCallback=="number"?e.mousemoveCallback:500,a=[],o,l=cn(O(u=>{let p=Date.now()-o;t(a.map(h=>(h.timeOffset-=p,h)),u),a=[],o=null}),s),c=O(cn(O(u=>{let p=vn(u),{clientX:h,clientY:m}=ri(u)?u.changedTouches[0]:u;o||(o=Gn()),a.push({x:h,y:m,id:r.getId(p),timeOffset:Gn()-o}),l(typeof DragEvent<"u"&&u instanceof DragEvent?R.Drag:u instanceof MouseEvent?R.MouseMove:R.TouchMove)}),i,{trailing:!1})),d=[at("mousemove",c,n),at("touchmove",c,n),at("drag",c,n)];return O(()=>{d.forEach(u=>u())})}function Bf({mouseInteractionCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,sampling:a}){if(a.mouseInteraction===!1)return()=>{};let o=a.mouseInteraction===!0||a.mouseInteraction===void 0?{}:a.mouseInteraction,l=[],c=null,d=u=>p=>{let h=vn(p);if(yt(h,r,i,s,!0))return;let m=null,g=u;if("pointerType"in p){switch(p.pointerType){case"mouse":m=Rt.Mouse;break;case"touch":m=Rt.Touch;break;case"pen":m=Rt.Pen;break}m===Rt.Touch?it[u]===it.MouseDown?g="TouchStart":it[u]===it.MouseUp&&(g="TouchEnd"):Rt.Pen}else ri(p)&&(m=Rt.Touch);m!==null?(c=m,(g.startsWith("Touch")&&m===Rt.Touch||g.startsWith("Mouse")&&m===Rt.Mouse)&&(m=null)):it[u]===it.Click&&(m=c,c=null);let _=ri(p)?p.changedTouches[0]:p;if(!_)return;let y=n.getId(h),{clientX:S,clientY:w}=_;O(t)({type:it[g],id:y,x:S,y:w,...m!==null&&{pointerType:m}})};return Object.keys(it).filter(u=>Number.isNaN(Number(u))&&!u.endsWith("_Departed")&&o[u]!==!1).forEach(u=>{let p=Pe(u),h=d(u);if(window.PointerEvent)switch(it[u]){case it.MouseDown:case it.MouseUp:p=p.replace("mouse","pointer");break;case it.TouchStart:case it.TouchEnd:return}l.push(at(p,h,e))}),O(()=>{l.forEach(u=>u())})}function Cl({scrollCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,sampling:a}){let o=O(cn(O(l=>{let c=vn(l);if(!c||yt(c,r,i,s,!0))return;let d=n.getId(c);if(c===e&&e.defaultView){let u=gl(e.defaultView);t({id:d,x:u.left,y:u.top})}else t({id:d,x:c.scrollLeft,y:c.scrollTop})}),a.scroll||100));return at("scroll",o,e)}function Hf({viewportResizeCb:t},{win:e}){let n=-1,r=-1,i=O(cn(O(()=>{let s=_l(),a=yl();(n!==s||r!==a)&&(t({width:Number(a),height:Number(s)}),n=s,r=a)}),200));return at("resize",i,e)}var zf=["INPUT","TEXTAREA","SELECT"],ta=new WeakMap;function qf({inputCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,ignoreClass:a,ignoreSelector:o,maskInputOptions:l,maskInputFn:c,sampling:d,userTriggeredOnInput:u,maskTextClass:p,unmaskTextClass:h,maskTextSelector:m,unmaskTextSelector:g}){function _(b){let v=vn(b),G=b.isTrusted,z=v&&ni(v.tagName);if(z==="OPTION"&&(v=v.parentElement),!v||!z||zf.indexOf(z)<0||yt(v,r,i,s,!0))return;let E=v;if(E.classList.contains(a)||o&&E.matches(o))return;let x=$i(v),W=Yn(E,z,x),V=!1,dt=hr({maskInputOptions:l,tagName:z,type:x}),pt=$e(v,p,m,h,g,dt);(x==="radio"||x==="checkbox")&&(V=v.checked),W=on({isMasked:pt,element:v,value:W,maskInputFn:c}),y(v,u?{text:W,isChecked:V,userTriggered:G}:{text:W,isChecked:V});let j=v.name;x==="radio"&&j&&V&&e.querySelectorAll(`input[type="radio"][name="${j}"]`).forEach(ht=>{if(ht!==v){let Q=on({isMasked:pt,element:ht,value:Yn(ht,z,x),maskInputFn:c});y(ht,u?{text:Q,isChecked:!V,userTriggered:!1}:{text:Q,isChecked:!V})}})}function y(b,v){let G=ta.get(b);if(!G||G.text!==v.text||G.isChecked!==v.isChecked){ta.set(b,v);let z=n.getId(b);O(t)({...v,id:z})}}let S=(d.input==="last"?["change"]:["input","change"]).map(b=>at(b,O(_),e)),w=e.defaultView;if(!w)return()=>{S.forEach(b=>b())};let D=w.Object.getOwnPropertyDescriptor(w.HTMLInputElement.prototype,"value"),F=[[w.HTMLInputElement.prototype,"value"],[w.HTMLInputElement.prototype,"checked"],[w.HTMLSelectElement.prototype,"value"],[w.HTMLTextAreaElement.prototype,"value"],[w.HTMLSelectElement.prototype,"selectedIndex"],[w.HTMLOptionElement.prototype,"selected"]];return D&&D.set&&S.push(...F.map(b=>fl(b[0],b[1],{set(){O(_)({target:this,isTrusted:!1})}},!1,w))),O(()=>{S.forEach(b=>b())})}function Xn(t){let e=[];function n(r,i){if(Dn("CSSGroupingRule")&&r.parentRule instanceof CSSGroupingRule||Dn("CSSMediaRule")&&r.parentRule instanceof CSSMediaRule||Dn("CSSSupportsRule")&&r.parentRule instanceof CSSSupportsRule||Dn("CSSConditionRule")&&r.parentRule instanceof CSSConditionRule){let s=Array.from(r.parentRule.cssRules).indexOf(r);i.unshift(s)}else if(r.parentStyleSheet){let s=Array.from(r.parentStyleSheet.cssRules).indexOf(r);i.unshift(s)}return i}return n(t,e)}function jt(t,e,n){let r,i;return t?(t.ownerNode?r=e.getId(t.ownerNode):i=n.getId(t),{styleId:i,id:r}):{}}function Wf({styleSheetRuleCb:t,mirror:e,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};let i=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(i,{apply:O((d,u,p)=>{let[h,m]=p,{id:g,styleId:_}=jt(u,e,n.styleMirror);return(g&&g!==-1||_&&_!==-1)&&t({id:g,styleId:_,adds:[{rule:h,index:m}]}),d.apply(u,p)})});let s=r.CSSStyleSheet.prototype.deleteRule;r.CSSStyleSheet.prototype.deleteRule=new Proxy(s,{apply:O((d,u,p)=>{let[h]=p,{id:m,styleId:g}=jt(u,e,n.styleMirror);return(m&&m!==-1||g&&g!==-1)&&t({id:m,styleId:g,removes:[{index:h}]}),d.apply(u,p)})});let a;r.CSSStyleSheet.prototype.replace&&(a=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(a,{apply:O((d,u,p)=>{let[h]=p,{id:m,styleId:g}=jt(u,e,n.styleMirror);return(m&&m!==-1||g&&g!==-1)&&t({id:m,styleId:g,replace:h}),d.apply(u,p)})}));let o;r.CSSStyleSheet.prototype.replaceSync&&(o=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(o,{apply:O((d,u,p)=>{let[h]=p,{id:m,styleId:g}=jt(u,e,n.styleMirror);return(m&&m!==-1||g&&g!==-1)&&t({id:m,styleId:g,replaceSync:h}),d.apply(u,p)})}));let l={};Nn("CSSGroupingRule")?l.CSSGroupingRule=r.CSSGroupingRule:(Nn("CSSMediaRule")&&(l.CSSMediaRule=r.CSSMediaRule),Nn("CSSConditionRule")&&(l.CSSConditionRule=r.CSSConditionRule),Nn("CSSSupportsRule")&&(l.CSSSupportsRule=r.CSSSupportsRule));let c={};return Object.entries(l).forEach(([d,u])=>{c[d]={insertRule:u.prototype.insertRule,deleteRule:u.prototype.deleteRule},u.prototype.insertRule=new Proxy(c[d].insertRule,{apply:O((p,h,m)=>{let[g,_]=m,{id:y,styleId:S}=jt(h.parentStyleSheet,e,n.styleMirror);return(y&&y!==-1||S&&S!==-1)&&t({id:y,styleId:S,adds:[{rule:g,index:[...Xn(h),_||0]}]}),p.apply(h,m)})}),u.prototype.deleteRule=new Proxy(c[d].deleteRule,{apply:O((p,h,m)=>{let[g]=m,{id:_,styleId:y}=jt(h.parentStyleSheet,e,n.styleMirror);return(_&&_!==-1||y&&y!==-1)&&t({id:_,styleId:y,removes:[{index:[...Xn(h),g]}]}),p.apply(h,m)})})}),O(()=>{r.CSSStyleSheet.prototype.insertRule=i,r.CSSStyleSheet.prototype.deleteRule=s,a&&(r.CSSStyleSheet.prototype.replace=a),o&&(r.CSSStyleSheet.prototype.replaceSync=o),Object.entries(l).forEach(([d,u])=>{u.prototype.insertRule=c[d].insertRule,u.prototype.deleteRule=c[d].deleteRule})})}function Rl({mirror:t,stylesheetManager:e},n){let r=null;n.nodeName==="#document"?r=t.getId(n):r=t.getId(n.host);let i=n.nodeName==="#document"?Tt([n,"access",a=>a.defaultView,"optionalAccess",a=>a.Document]):Tt([n,"access",a=>a.ownerDocument,"optionalAccess",a=>a.defaultView,"optionalAccess",a=>a.ShadowRoot]),s=Tt([i,"optionalAccess",a=>a.prototype])?Object.getOwnPropertyDescriptor(Tt([i,"optionalAccess",a=>a.prototype]),"adoptedStyleSheets"):void 0;return r===null||r===-1||!i||!s?()=>{}:(Object.defineProperty(n,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get(){return Tt([s,"access",a=>a.get,"optionalAccess",a=>a.call,"call",a=>a(this)])},set(a){let o=Tt([s,"access",l=>l.set,"optionalAccess",l=>l.call,"call",l=>l(this,a)]);if(r!==null&&r!==-1)try{e.adoptStyleSheets(a,r)}catch{}return o}}),O(()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get:s.get,set:s.set})}))}function Vf({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:n,stylesheetManager:r},{win:i}){let s=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:O((o,l,c)=>{let[d,u,p]=c;if(n.has(d))return s.apply(l,[d,u,p]);let{id:h,styleId:m}=jt(Tt([l,"access",g=>g.parentRule,"optionalAccess",g=>g.parentStyleSheet]),e,r.styleMirror);return(h&&h!==-1||m&&m!==-1)&&t({id:h,styleId:m,set:{property:d,value:u,priority:p},index:Xn(l.parentRule)}),o.apply(l,c)})});let a=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(a,{apply:O((o,l,c)=>{let[d]=c;if(n.has(d))return a.apply(l,[d]);let{id:u,styleId:p}=jt(Tt([l,"access",h=>h.parentRule,"optionalAccess",h=>h.parentStyleSheet]),e,r.styleMirror);return(u&&u!==-1||p&&p!==-1)&&t({id:u,styleId:p,remove:{property:d},index:Xn(l.parentRule)}),o.apply(l,c)})}),O(()=>{i.CSSStyleDeclaration.prototype.setProperty=s,i.CSSStyleDeclaration.prototype.removeProperty=a})}function Yf({mediaInteractionCb:t,blockClass:e,blockSelector:n,unblockSelector:r,mirror:i,sampling:s,doc:a}){let o=O(c=>cn(O(d=>{let u=vn(d);if(!u||yt(u,e,n,r,!0))return;let{currentTime:p,volume:h,muted:m,playbackRate:g}=u;t({type:c,id:i.getId(u),currentTime:p,volume:h,muted:m,playbackRate:g})}),s.media||500)),l=[at("play",o(0),a),at("pause",o(1),a),at("seeked",o(2),a),at("volumechange",o(3),a),at("ratechange",o(4),a)];return O(()=>{l.forEach(c=>c())})}function Kf({fontCb:t,doc:e}){let n=e.defaultView;if(!n)return()=>{};let r=[],i=new WeakMap,s=n.FontFace;n.FontFace=function(o,l,c){let d=new s(o,l,c);return i.set(d,{family:o,buffer:typeof l!="string",descriptors:c,fontSource:typeof l=="string"?l:JSON.stringify(Array.from(new Uint8Array(l)))}),d};let a=Ui(e.fonts,"add",function(o){return function(l){return mr(O(()=>{let c=i.get(l);c&&(t(c),i.delete(l))}),0),o.apply(this,[l])}});return r.push(()=>{n.FontFace=s}),r.push(a),O(()=>{r.forEach(o=>o())})}function Jf(t){let{doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,selectionCb:a}=t,o=!0,l=O(()=>{let c=e.getSelection();if(!c||o&&Tt([c,"optionalAccess",p=>p.isCollapsed]))return;o=c.isCollapsed||!1;let d=[],u=c.rangeCount||0;for(let p=0;p<u;p++){let h=c.getRangeAt(p),{startContainer:m,startOffset:g,endContainer:_,endOffset:y}=h;yt(m,r,i,s,!0)||yt(_,r,i,s,!0)||d.push({start:n.getId(m),startOffset:g,end:n.getId(_),endOffset:y})}a({ranges:d})});return l(),at("selectionchange",l)}function Gf({doc:t,customElementCb:e}){let n=t.defaultView;return!n||!n.customElements?()=>{}:Ui(n.customElements,"define",function(r){return function(i,s,a){try{e({define:{name:i}})}catch{}return r.apply(this,[i,s,a])}})}function Xf(t,e={}){let n=t.doc.defaultView;if(!n)return()=>{};let r=xl(t,t.doc),i=Uf(t),s=Bf(t),a=Cl(t),o=Hf(t,{win:n}),l=qf(t),c=Yf(t),d=Wf(t,{win:n}),u=Rl(t,t.doc),p=Vf(t,{win:n}),h=t.collectFonts?Kf(t):()=>{},m=Jf(t),g=Gf(t),_=[];for(let y of t.plugins)_.push(y.observer(y.callback,n,y.options));return O(()=>{ke.forEach(y=>y.reset()),r.disconnect(),i(),s(),a(),o(),l(),c(),d(),u(),p(),h(),m(),g(),_.forEach(y=>y())})}function Dn(t){return typeof window[t]<"u"}function Nn(t){return!!(typeof window[t]<"u"&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}var ai=class{constructor(t){this.generateIdFn=t,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(t,e,n,r){let i=n||this.getIdToRemoteIdMap(t),s=r||this.getRemoteIdToIdMap(t),a=i.get(e);return a||(a=this.generateIdFn(),i.set(e,a),s.set(a,e)),a}getIds(t,e){let n=this.getIdToRemoteIdMap(t),r=this.getRemoteIdToIdMap(t);return e.map(i=>this.getId(t,i,n,r))}getRemoteId(t,e,n){let r=n||this.getRemoteIdToIdMap(t);return typeof e!="number"?e:r.get(e)||-1}getRemoteIds(t,e){let n=this.getRemoteIdToIdMap(t);return e.map(r=>this.getRemoteId(t,r,n))}reset(t){if(!t){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let e=this.iframeIdToRemoteIdMap.get(t);return e||(e=new Map,this.iframeIdToRemoteIdMap.set(t,e)),e}getRemoteIdToIdMap(t){let e=this.iframeRemoteIdToIdMap.get(t);return e||(e=new Map,this.iframeRemoteIdToIdMap.set(t,e)),e}};function ea(t){let e,n=t[0],r=1;for(;r<t.length;){let i=t[r],s=t[r+1];if(r+=2,(i==="optionalAccess"||i==="optionalCall")&&n==null)return;i==="access"||i==="optionalAccess"?(e=n,n=s(n)):(i==="call"||i==="optionalCall")&&(n=s((...a)=>n.call(e,...a)),e=void 0)}return n}var Qf=class{constructor(){this.crossOriginIframeMirror=new ai(Fi),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}},Zf=class{constructor(t){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new ai(Fi),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new ai(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),ea([this,"access",n=>n.loadListener,"optionalCall",n=>n(t)]),t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){let e=t;if(e.data.type!=="rrweb"||e.origin!==e.data.origin||!t.source)return;let n=this.crossOriginIframeMap.get(t.source);if(!n)return;let r=this.transformCrossOriginEvent(n,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(t,e){switch(e.type){case C.FullSnapshot:{this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(e.data.node,t);let n=e.data.node.id;return this.crossOriginIframeRootIdMap.set(t,n),this.patchRootIdOnNode(e.data.node,n),{timestamp:e.timestamp,type:C.IncrementalSnapshot,data:{source:R.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case C.Meta:case C.Load:case C.DomContentLoaded:return!1;case C.Plugin:return e;case C.Custom:return this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]),e;case C.IncrementalSnapshot:switch(e.data.source){case R.Mutation:return e.data.adds.forEach(n=>{this.replaceIds(n,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(n.node,t);let r=this.crossOriginIframeRootIdMap.get(t);r&&this.patchRootIdOnNode(n.node,r)}),e.data.removes.forEach(n=>{this.replaceIds(n,t,["parentId","id"])}),e.data.attributes.forEach(n=>{this.replaceIds(n,t,["id"])}),e.data.texts.forEach(n=>{this.replaceIds(n,t,["id"])}),e;case R.Drag:case R.TouchMove:case R.MouseMove:return e.data.positions.forEach(n=>{this.replaceIds(n,t,["id"])}),e;case R.ViewportResize:return!1;case R.MediaInteraction:case R.MouseInteraction:case R.Scroll:case R.CanvasMutation:case R.Input:return this.replaceIds(e.data,t,["id"]),e;case R.StyleSheetRule:case R.StyleDeclaration:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleId"]),e;case R.Font:return e;case R.Selection:return e.data.ranges.forEach(n=>{this.replaceIds(n,t,["start","end"])}),e;case R.AdoptedStyleSheet:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleIds"]),ea([e,"access",n=>n.data,"access",n=>n.styles,"optionalAccess",n=>n.forEach,"call",n=>n(r=>{this.replaceStyleIds(r,t,["styleId"])})]),e}}return!1}replace(t,e,n,r){for(let i of r)!Array.isArray(e[i])&&typeof e[i]!="number"||(Array.isArray(e[i])?e[i]=t.getIds(n,e[i]):e[i]=t.getId(n,e[i]));return e}replaceIds(t,e,n){return this.replace(this.crossOriginIframeMirror,t,e,n)}replaceStyleIds(t,e,n){return this.replace(this.crossOriginIframeStyleMirror,t,e,n)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]),"childNodes"in t&&t.childNodes.forEach(n=>{this.replaceIdOnNode(n,e)})}patchRootIdOnNode(t,e){t.type!==tt.Document&&!t.rootId&&(t.rootId=e),"childNodes"in t&&t.childNodes.forEach(n=>{this.patchRootIdOnNode(n,e)})}},tg=class{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}},eg=class{constructor(t){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(!nn(t)||this.shadowDoms.has(t))return;this.shadowDoms.add(t);let n=xl({...this.bypassOptions,doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},t);this.restoreHandlers.push(()=>n.disconnect()),this.restoreHandlers.push(Cl({...this.bypassOptions,scrollCb:this.scrollCb,doc:t,mirror:this.mirror})),mr(()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(t.host)),this.restoreHandlers.push(Rl({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))},0)}observeAttachShadow(t){!t.contentWindow||!t.contentDocument||this.patchAttachShadow(t.contentWindow.Element,t.contentDocument)}patchAttachShadow(t,e){let n=this;this.restoreHandlers.push(Ui(t.prototype,"attachShadow",function(r){return function(i){let s=r.call(this,i);return this.shadowRoot&&wl(this)&&n.addShadowRoot(this.shadowRoot,e),s}}))}reset(){this.restoreHandlers.forEach(t=>{try{t()}catch{}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet}},na=class{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}},ng=class{constructor(t){this.trackedLinkElements=new WeakSet,this.styleMirror=new Mf,this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){"_cssText"in e.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,e){if(t.length===0)return;let n={id:e,styleIds:[]},r=[];for(let i of t){let s;this.styleMirror.has(i)?s=this.styleMirror.getId(i):(s=this.styleMirror.add(i),r.push({styleId:s,rules:Array.from(i.rules||CSSRule,(a,o)=>({rule:dl(a),index:o}))})),n.styleIds.push(s)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}},rg=class{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){Df(()=>{this.clear(),this.loop&&this.periodicallyClear()})}inOtherBuffer(t,e){let n=this.nodeMap.get(t);return n&&Array.from(n).some(r=>r!==e)}add(t,e){this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}},Y,Qn,_t=rf();function zt(t={}){let{emit:e,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:i="rr-block",blockSelector:s=null,unblockSelector:a=null,ignoreClass:o="rr-ignore",ignoreSelector:l=null,maskAllText:c=!1,maskTextClass:d="rr-mask",unmaskTextClass:u=null,maskTextSelector:p=null,unmaskTextSelector:h=null,inlineStylesheet:m=!0,maskAllInputs:g,maskInputOptions:_,slimDOMOptions:y,maskAttributeFn:S,maskInputFn:w,maskTextFn:D,maxCanvasSize:F=null,packFn:b,sampling:v={},dataURLOptions:G={},mousemoveWait:z,recordCanvas:E=!1,recordCrossOriginIframes:x=!1,recordAfter:W=t.recordAfter==="DOMContentLoaded"?t.recordAfter:"load",userTriggeredOnInput:V=!1,collectFonts:dt=!1,inlineImages:pt=!1,plugins:j,keepIframeSrcFn:ht=()=>!1,ignoreCSSAttributes:Q=new Set([]),errorHandler:mt,onMutation:ft,getCanvasManager:yr}=t;Ff(mt);let me=x?window.parent===window:!0,$t=!1;if(!me)try{window.parent.document&&($t=!1)}catch{$t=!0}if(me&&!e)throw new Error("emit function is required");z!==void 0&&v.mousemove===void 0&&(v.mousemove=z),_t.reset();let Ve=g===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:_!==void 0?_:{},Ye=y===!0||y==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:y==="all",headMetaDescKeywords:y==="all"}:y||{};Rf();let vr,Sr=0,Ki=P=>{for(let gt of j||[])gt.eventProcessor&&(P=gt.eventProcessor(P));return b&&!$t&&(P=b(P)),P};Y=(P,gt)=>{let N=P;if(N.timestamp=Gn(),Ir([ke,"access",q=>q[0],"optionalAccess",q=>q.isFrozen,"call",q=>q()])&&N.type!==C.FullSnapshot&&!(N.type===C.IncrementalSnapshot&&N.data.source===R.Mutation)&&ke.forEach(q=>q.unfreeze()),me)Ir([e,"optionalCall",q=>q(Ki(N),gt)]);else if($t){let q={type:"rrweb",event:Ki(N),origin:window.location.origin,isCheckout:gt};window.parent.postMessage(q,"*")}if(N.type===C.FullSnapshot)vr=N,Sr=0;else if(N.type===C.IncrementalSnapshot){if(N.data.source===R.Mutation&&N.data.isAttachIframe)return;Sr++;let q=r&&Sr>=r,L=n&&vr&&N.timestamp-vr.timestamp>n;(q||L)&&kr(!0)}};let bn=P=>{Y({type:C.IncrementalSnapshot,data:{source:R.Mutation,...P}})},Ji=P=>Y({type:C.IncrementalSnapshot,data:{source:R.Scroll,...P}}),ic=P=>Y({type:C.IncrementalSnapshot,data:{source:R.CanvasMutation,...P}}),sc=P=>Y({type:C.IncrementalSnapshot,data:{source:R.AdoptedStyleSheet,...P}}),ee=new ng({mutationCb:bn,adoptedStyleSheetCb:sc}),ne=typeof __RRWEB_EXCLUDE_IFRAME__=="boolean"&&__RRWEB_EXCLUDE_IFRAME__?new Qf:new Zf({mirror:_t,mutationCb:bn,stylesheetManager:ee,recordCrossOriginIframes:x,wrappedEmit:Y});for(let P of j||[])P.getMirror&&P.getMirror({nodeMirror:_t,crossOriginIframeMirror:ne.crossOriginIframeMirror,crossOriginIframeStyleMirror:ne.crossOriginIframeStyleMirror});let br=new rg,Gi=sg(yr,{mirror:_t,win:window,mutationCb:P=>Y({type:C.IncrementalSnapshot,data:{source:R.CanvasMutation,...P}}),recordCanvas:E,blockClass:i,blockSelector:s,unblockSelector:a,maxCanvasSize:F,sampling:v.canvas,dataURLOptions:G,errorHandler:mt}),kn=typeof __RRWEB_EXCLUDE_SHADOW_DOM__=="boolean"&&__RRWEB_EXCLUDE_SHADOW_DOM__?new tg:new eg({mutationCb:bn,scrollCb:Ji,bypassOptions:{onMutation:ft,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:c,maskTextClass:d,unmaskTextClass:u,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:m,maskInputOptions:Ve,dataURLOptions:G,maskAttributeFn:S,maskTextFn:D,maskInputFn:w,recordCanvas:E,inlineImages:pt,sampling:v,slimDOMOptions:Ye,iframeManager:ne,stylesheetManager:ee,canvasManager:Gi,keepIframeSrcFn:ht,processedNodeManager:br},mirror:_t}),kr=(P=!1)=>{Y({type:C.Meta,data:{href:window.location.href,width:yl(),height:_l()}},P),ee.reset(),kn.init(),ke.forEach(N=>N.lock());let gt=xf(document,{mirror:_t,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:c,maskTextClass:d,unmaskTextClass:u,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:m,maskAllInputs:Ve,maskAttributeFn:S,maskInputFn:w,maskTextFn:D,slimDOM:Ye,dataURLOptions:G,recordCanvas:E,inlineImages:pt,onSerialize:N=>{bl(N,_t)&&ne.addIframe(N),kl(N,_t)&&ee.trackLinkElement(N),ii(N)&&kn.addShadowRoot(N.shadowRoot,document)},onIframeLoad:(N,q)=>{ne.attachIframe(N,q),kn.observeAttachShadow(N)},onStylesheetLoad:(N,q)=>{ee.attachLinkElement(N,q)},keepIframeSrcFn:ht});if(!gt)return console.warn("Failed to snapshot the document");Y({type:C.FullSnapshot,data:{node:gt,initialOffset:gl(window)}}),ke.forEach(N=>N.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&ee.adoptStyleSheets(document.adoptedStyleSheets,_t.getId(document))};Qn=kr;try{let P=[],gt=q=>O(Xf)({onMutation:ft,mutationCb:bn,mousemoveCb:(L,re)=>Y({type:C.IncrementalSnapshot,data:{source:re,positions:L}}),mouseInteractionCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.MouseInteraction,...L}}),scrollCb:Ji,viewportResizeCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.ViewportResize,...L}}),inputCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.Input,...L}}),mediaInteractionCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.MediaInteraction,...L}}),styleSheetRuleCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.StyleSheetRule,...L}}),styleDeclarationCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.StyleDeclaration,...L}}),canvasMutationCb:ic,fontCb:L=>Y({type:C.IncrementalSnapshot,data:{source:R.Font,...L}}),selectionCb:L=>{Y({type:C.IncrementalSnapshot,data:{source:R.Selection,...L}})},customElementCb:L=>{Y({type:C.IncrementalSnapshot,data:{source:R.CustomElement,...L}})},blockClass:i,ignoreClass:o,ignoreSelector:l,maskAllText:c,maskTextClass:d,unmaskTextClass:u,maskTextSelector:p,unmaskTextSelector:h,maskInputOptions:Ve,inlineStylesheet:m,sampling:v,recordCanvas:E,inlineImages:pt,userTriggeredOnInput:V,collectFonts:dt,doc:q,maskAttributeFn:S,maskInputFn:w,maskTextFn:D,keepIframeSrcFn:ht,blockSelector:s,unblockSelector:a,slimDOMOptions:Ye,dataURLOptions:G,mirror:_t,iframeManager:ne,stylesheetManager:ee,shadowDomManager:kn,processedNodeManager:br,canvasManager:Gi,ignoreCSSAttributes:Q,plugins:Ir([j,"optionalAccess",L=>L.filter,"call",L=>L(re=>re.observer),"optionalAccess",L=>L.map,"call",L=>L(re=>({observer:re.observer,options:re.options,callback:ac=>Y({type:C.Plugin,data:{plugin:re.name,payload:ac}})}))])||[]},{});ne.addLoadListener(q=>{try{P.push(gt(q.contentDocument))}catch(L){console.warn(L)}});let N=()=>{kr(),P.push(gt(document))};return document.readyState==="interactive"||document.readyState==="complete"?N():(P.push(at("DOMContentLoaded",()=>{Y({type:C.DomContentLoaded,data:{}}),W==="DOMContentLoaded"&&N()})),P.push(at("load",()=>{Y({type:C.Load,data:{}}),W==="load"&&N()},window))),()=>{P.forEach(q=>q()),br.destroy(),Qn=void 0,jf()}}catch(P){console.warn(P)}}function ig(t){if(!Qn)throw new Error("please take full snapshot after start recording");Qn(t)}zt.mirror=_t;zt.takeFullSnapshot=ig;function sg(t,e){try{return t?t(e):new na}catch{return console.warn("Unable to initialize CanvasManager"),new na}}var ag=3,og=5;function Hi(t){return t>9999999999?t:t*1e3}function Nr(t){return t>9999999999?t/1e3:t}function Sn(t,e){e.category!=="sentry.transaction"&&(["ui.click","ui.input"].includes(e.category)?t.triggerUserActivity():t.checkAndHandleExpiredSession(),t.addUpdate(()=>(t.throttledAddEvent({type:C.Custom,timestamp:(e.timestamp||0)*1e3,data:{tag:"breadcrumb",payload:kt(e,10,1e3)}}),e.category==="console")))}var lg="button,a";function Ml(t){return t.closest(lg)||t}function Ol(t){let e=Al(t);return!e||!(e instanceof Element)?e:Ml(e)}function Al(t){return cg(t)?t.target:t}function cg(t){return typeof t=="object"&&!!t&&"target"in t}var Ut;function ug(t){return Ut||(Ut=[],dg()),Ut.push(t),()=>{let e=Ut?Ut.indexOf(t):-1;e>-1&&Ut.splice(e,1)}}function dg(){nt(H,"open",function(t){return function(...e){if(Ut)try{Ut.forEach(n=>n())}catch{}return t.apply(H,e)}})}function pg(t,e,n){t.handleClick(e,n)}var hg=class{constructor(t,e,n=Sn){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=e.timeout/1e3,this._threshold=e.threshold/1e3,this._scollTimeout=e.scrollTimeout/1e3,this._replay=t,this._ignoreSelector=e.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){let t=ug(()=>{this._lastMutation=ra()});this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(t,e){if(fg(e,this._ignoreSelector)||!gg(t))return;let n={timestamp:Nr(t.timestamp),clickBreadcrumb:t,clickCount:0,node:e};this._clicks.some(r=>r.node===n.node&&Math.abs(r.timestamp-n.timestamp)<1)||(this._clicks.push(n),this._clicks.length===1&&this._scheduleCheckClicks())}registerMutation(t=Date.now()){this._lastMutation=Nr(t)}registerScroll(t=Date.now()){this._lastScroll=Nr(t)}registerClick(t){let e=Ml(t);this._handleMultiClick(e)}_handleMultiClick(t){this._getClicks(t).forEach(e=>{e.clickCount++})}_getClicks(t){return this._clicks.filter(e=>e.node===t)}_checkClicks(){let t=[],e=ra();this._clicks.forEach(n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=e&&t.push(n)});for(let n of t){let r=this._clicks.indexOf(n);r>-1&&(this._generateBreadcrumbs(n),this._clicks.splice(r,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(t){let e=this._replay,n=t.scrollAfter&&t.scrollAfter<=this._scollTimeout,r=t.mutationAfter&&t.mutationAfter<=this._threshold,i=!n&&!r,{clickCount:s,clickBreadcrumb:a}=t;if(i){let o=Math.min(t.mutationAfter||this._timeout,this._timeout)*1e3,l=o<this._timeout*1e3?"mutation":"timeout",c={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.slowClickDetected",data:{...a.data,url:H.location.href,route:e.getCurrentRoute(),timeAfterClickMs:o,endReason:l,clickCount:s||1}};this._addBreadcrumbEvent(e,c);return}if(s>1){let o={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.multiClick",data:{...a.data,url:H.location.href,route:e.getCurrentRoute(),clickCount:s,metric:!0}};this._addBreadcrumbEvent(e,o)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout(()=>this._checkClicks(),1e3)}},mg=["A","BUTTON","INPUT"];function fg(t,e){return!!(!mg.includes(t.tagName)||t.tagName==="INPUT"&&!["submit","button"].includes(t.getAttribute("type")||"")||t.tagName==="A"&&(t.hasAttribute("download")||t.hasAttribute("target")&&t.getAttribute("target")!=="_self")||e&&t.matches(e))}function gg(t){return!!(t.data&&typeof t.data.nodeId=="number"&&t.timestamp)}function ra(){return Date.now()/1e3}function _g(t,e){try{if(!yg(e))return;let{source:n}=e.data;if(n===R.Mutation&&t.registerMutation(e.timestamp),n===R.Scroll&&t.registerScroll(e.timestamp),vg(e)){let{type:r,id:i}=e.data,s=zt.mirror.getNode(i);s instanceof HTMLElement&&r===it.Click&&t.registerClick(s)}}catch{}}function yg(t){return t.type===ag}function vg(t){return t.data.source===R.MouseInteraction}function Et(t){return{timestamp:Date.now()/1e3,type:"default",...t}}var Zn;(function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"})(Zn||(Zn={}));var Sg=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function bg(t){let e={};for(let n in t)if(Sg.has(n)){let r=n;(n==="data-testid"||n==="data-test-id")&&(r="testId"),e[r]=t[n]}return e}var kg=t=>e=>{if(!t.isEnabled())return;let n=Tg(e);if(!n)return;let r=e.name==="click",i=r?e.event:void 0;r&&t.clickDetector&&i&&i.target&&!i.altKey&&!i.metaKey&&!i.ctrlKey&&!i.shiftKey&&pg(t.clickDetector,n,Ol(e.event)),Sn(t,n)};function Dl(t,e){let n=zt.mirror.getId(t),r=n&&zt.mirror.getNode(n),i=r&&zt.mirror.getMeta(r),s=i&&Eg(i)?i:null;return{message:e,data:s?{nodeId:n,node:{id:n,tagName:s.tagName,textContent:Array.from(s.childNodes).map(a=>a.type===Zn.Text&&a.textContent).filter(Boolean).map(a=>a.trim()).join(""),attributes:bg(s.attributes)}}:{}}}function Tg(t){let{target:e,message:n}=wg(t);return Et({category:`ui.${t.name}`,...Dl(e,n)})}function wg(t){let e=t.name==="click",n,r=null;try{r=e?Ol(t.event):Al(t.event),n=Jt(r,{maxStringLength:200})||"<unknown>"}catch{n="<unknown>"}return{target:r,message:n}}function Eg(t){return t.type===Zn.Element}function Ig(t,e){if(!t.isEnabled())return;t.updateUserActivity();let n=xg(e);n&&Sn(t,n)}function xg(t){let{metaKey:e,shiftKey:n,ctrlKey:r,altKey:i,key:s,target:a}=t;if(!a||Cg(a)||!s)return null;let o=e||r||i,l=s.length===1;if(!o&&l)return null;let c=Jt(a,{maxStringLength:200})||"<unknown>",d=Dl(a,c);return Et({category:"ui.keyDown",message:c,data:{...d.data,metaKey:e,shiftKey:n,ctrlKey:r,altKey:i,key:s}})}function Cg(t){return t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable}var ia={resource:Dg,paint:Og,navigation:Ag};function Rg(t){return t.map(Mg).filter(Boolean)}function Mg(t){return ia[t.entryType]?ia[t.entryType](t):null}function Fe(t){return((lt||H.performance.timeOrigin)+t)/1e3}function Og(t){let{duration:e,entryType:n,name:r,startTime:i}=t,s=Fe(i);return{type:n,name:r,start:s,end:s+e,data:void 0}}function Ag(t){let{entryType:e,name:n,decodedBodySize:r,duration:i,domComplete:s,encodedBodySize:a,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,domInteractive:c,loadEventStart:d,loadEventEnd:u,redirectCount:p,startTime:h,transferSize:m,type:g}=t;return i===0?null:{type:`${e}.${g}`,start:Fe(h),end:Fe(s),name:n,data:{size:m,decodedBodySize:r,encodedBodySize:a,duration:i,domInteractive:c,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,loadEventStart:d,loadEventEnd:u,domComplete:s,redirectCount:p}}}function Dg(t){let{entryType:e,initiatorType:n,name:r,responseEnd:i,startTime:s,decodedBodySize:a,encodedBodySize:o,responseStatus:l,transferSize:c}=t;return["fetch","xmlhttprequest"].includes(n)?null:{type:`${e}.${n}`,start:Fe(s),end:Fe(i),name:r,data:{size:c,statusCode:l,decodedBodySize:a,encodedBodySize:o}}}function Ng(t){let e=t.entries,n=e[e.length-1],r=n?n.element:void 0,i=t.value,s=Fe(i);return{type:"largest-contentful-paint",name:"largest-contentful-paint",start:s,end:s,data:{value:i,size:i,nodeId:r?zt.mirror.getId(r):void 0}}}function Lg(t){function e(i){t.performanceEntries.includes(i)||t.performanceEntries.push(i)}function n({entries:i}){i.forEach(e)}let r=[];return["navigation","paint","resource"].forEach(i=>{r.push(Qt(i,n))}),r.push(Ao(({metric:i})=>{t.replayPerformanceEntries.push(Ng(i))})),()=>{r.forEach(i=>i())}}var U=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Pg='var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==r||r<0)&&(r=0),(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},A=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},_=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=x(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},x=function(t,n,r){return-1==t.s?Math.max(x(t.l,n,r+1),x(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},C=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=_(f,15),M=b.t,E=b.l,x=_(h,15),C=x.t,U=x.l,F=D(M),I=F.c,S=F.n,L=D(C),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=_(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,C)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(C,U,0),R=C;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){A(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;A(r,m,Q[et]),m+=R[et],et>3&&(A(r,m,rt>>5&8191),m+=i[et])}else A(r,m,N[rt]),m+=P[rt]}return A(r,m,N[256]),m+P[256]},U=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},L=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=U[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,A=c.p||new n(32768),_=c.h||new n(z+1),x=Math.ceil(o/3),D=2*x,T=function(t){return(a[t]^a[t+1]<<x^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=_[H];if(A[J]=K,_[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=C(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-A[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=A[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=C(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=_,c.p=A,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},O=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},j=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(L(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();function q(t,n){n||(n={});var r=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}}(),e=t.length;r.p(t);var i,a=L(t,n,10+((i=n).filename?i.filename.length+1:0),8),s=a.length;return function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&O(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}}(a,n),O(a,s-8,r.d()),O(a,s-4,e),a}var B=function(){function t(t,n){this.c=S(),this.v=1,j.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),j.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=L(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=S();i.p(n.dictionary),O(t,2,i.d())}}(r,this.o),this.v=0),n&&O(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),G="undefined"!=typeof TextEncoder&&new TextEncoder,H="undefined"!=typeof TextDecoder&&new TextDecoder;try{H.decode(F,{stream:!0})}catch(t){}var J=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(K(t),this.d=n||!1)},t}();function K(n,r){if(r){for(var e=new t(n.length),i=0;i<n.length;++i)e[i]=n.charCodeAt(i);return e}if(G)return G.encode(n);var a=n.length,s=new t(n.length+(n.length>>1)),o=0,f=function(t){s[o++]=t};for(i=0;i<a;++i){if(o+5>s.length){var h=new t(o+8+(a-i<<1));h.set(s),s=h}var l=n.charCodeAt(i);l<128||r?f(l):l<2048?(f(192|l>>6),f(128|63&l)):l>55295&&l<57344?(f(240|(l=65536+(1047552&l)|1023&n.charCodeAt(++i))>>18),f(128|l>>12&63),f(128|l>>6&63),f(128|63&l)):(f(224|l>>12),f(128|l>>6&63),f(128|63&l))}return b(s,0,o)}const N=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(let r=0,e=t.length;r<e;r++)n+=t[r].length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new B,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new J(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},P={clear:()=>{N.clear()},addEvent:t=>N.addEvent(t),finish:()=>N.finish(),compress:t=>function(t){return q(K(t))}(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in P&&"function"==typeof P[n])try{const t=P[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});';function $g(){let t=new Blob([Pg]);return URL.createObjectURL(t)}function st(t,e){U&&(f.info(t),e&&Nl(t))}function Ee(t,e){U&&(f.info(t),e&&setTimeout(()=>{Nl(t)},0))}function Nl(t){Xt({category:"console",data:{logger:"replay"},level:"info",message:t},{level:"info"})}var zi=class extends Error{constructor(){super(`Event buffer exceeded maximum size of ${Pi}.`)}},Ll=class{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(t){let e=JSON.stringify(t).length;if(this._totalSize+=e,this._totalSize>Pi)throw new zi;this.events.push(t)}finish(){return new Promise(t=>{let e=this.events;this.clear(),t(JSON.stringify(e))})}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){let t=this.events.map(e=>e.timestamp).sort()[0];return t?Hi(t):null}},Fg=class{constructor(t){this._worker=t,this._id=0}ensureReady(){return this._ensureReadyPromise?this._ensureReadyPromise:(this._ensureReadyPromise=new Promise((t,e)=>{this._worker.addEventListener("message",({data:n})=>{n.success?t():e()},{once:!0}),this._worker.addEventListener("error",n=>{e(n)},{once:!0})}),this._ensureReadyPromise)}destroy(){st("[Replay] Destroying compression worker"),this._worker.terminate()}postMessage(t,e){let n=this._getAndIncrementId();return new Promise((r,i)=>{let s=({data:a})=>{let o=a;if(o.method===t&&o.id===n){if(this._worker.removeEventListener("message",s),!o.success){U&&f.error("[Replay]",o.response),i(new Error("Error in compression worker"));return}r(o.response)}};this._worker.addEventListener("message",s),this._worker.postMessage({id:n,method:t,arg:e})})}_getAndIncrementId(){return this._id++}},jg=class{constructor(t){this._worker=new Fg(t),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(t){let e=Hi(t.timestamp);(!this._earliestTimestamp||e<this._earliestTimestamp)&&(this._earliestTimestamp=e);let n=JSON.stringify(t);return this._totalSize+=n.length,this._totalSize>Pi?Promise.reject(new zi):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,t=>{U&&f.warn('[Replay] Sending "clear" message to worker failed',t)})}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(t){return this._worker.postMessage("addEvent",t)}async _finishRequest(){let t=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,t}},Ug=class{constructor(t){this._fallback=new Ll,this._compression=new jg(t),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(t){this._used.hasCheckout=t}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(t){return this._used.addEvent(t)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch{st("[Replay] Failed to load the compression worker, falling back to simple buffer");return}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){let{events:t,hasCheckout:e}=this._fallback,n=[];for(let r of t)n.push(this._compression.addEvent(r));this._compression.hasCheckout=e,this._used=this._compression;try{await Promise.all(n)}catch(r){U&&f.warn("[Replay] Failed to add events when switching buffers.",r)}}};function Bg({useCompression:t,workerUrl:e}){if(t&&window.Worker){let n=Hg(e);if(n)return n}return st("[Replay] Using simple buffer"),new Ll}function Hg(t){try{let e=t||zg();if(!e)return;st(`[Replay] Using compression worker${t?` from ${t}`:""}`);let n=new Worker(e);return new Ug(n)}catch{st("[Replay] Failed to create compression worker")}}function zg(){return typeof __SENTRY_EXCLUDE_REPLAY_WORKER__>"u"||!__SENTRY_EXCLUDE_REPLAY_WORKER__?$g():""}function qi(){try{return"sessionStorage"in H&&!!H.sessionStorage}catch{return!1}}function qg(t){Wg(),t.session=void 0}function Wg(){if(qi())try{H.sessionStorage.removeItem(Ni)}catch{}}function Pl(t){return t===void 0?!1:Math.random()<t}function $l(t){let e=Date.now(),n=t.id||J(),r=t.started||e,i=t.lastActivity||e,s=t.segmentId||0,a=t.sampled,o=t.previousSessionId;return{id:n,started:r,lastActivity:i,segmentId:s,sampled:a,previousSessionId:o}}function Wi(t){if(qi())try{H.sessionStorage.setItem(Ni,JSON.stringify(t))}catch{}}function Vg(t,e){return Pl(t)?"session":e?"buffer":!1}function sa({sessionSampleRate:t,allowBuffering:e,stickySession:n=!1},{previousSessionId:r}={}){let i=Vg(t,e),s=$l({sampled:i,previousSessionId:r});return n&&Wi(s),s}function Yg(t){if(!qi())return null;try{let e=H.sessionStorage.getItem(Ni);if(!e)return null;let n=JSON.parse(e);return Ee("[Replay] Loading existing session",t),$l(n)}catch{return null}}function oi(t,e,n=+new Date){return t===null||e===void 0||e<0?!0:e===0?!1:t+e<=n}function Fl(t,{maxReplayDuration:e,sessionIdleExpire:n,targetTime:r=Date.now()}){return oi(t.started,e,r)||oi(t.lastActivity,n,r)}function jl(t,{sessionIdleExpire:e,maxReplayDuration:n}){return!(!Fl(t,{sessionIdleExpire:e,maxReplayDuration:n})||t.sampled==="buffer"&&t.segmentId===0)}function Lr({traceInternals:t,sessionIdleExpire:e,maxReplayDuration:n,previousSessionId:r},i){let s=i.stickySession&&Yg(t);return s?jl(s,{sessionIdleExpire:e,maxReplayDuration:n})?(Ee("[Replay] Session in sessionStorage is expired, creating new one..."),sa(i,{previousSessionId:s.id})):s:(Ee("[Replay] Creating new session",t),sa(i,{previousSessionId:r}))}function Kg(t){return t.type===C.Custom}function Vi(t,e,n){return Bl(t,e)?(Ul(t,e,n),!0):!1}function Jg(t,e,n){return Bl(t,e)?Ul(t,e,n):Promise.resolve(null)}async function Ul(t,e,n){if(!t.eventBuffer)return null;try{n&&t.recordingMode==="buffer"&&t.eventBuffer.clear(),n&&(t.eventBuffer.hasCheckout=!0);let r=t.getOptions(),i=Gg(e,r.beforeAddRecordingEvent);return i?await t.eventBuffer.addEvent(i):void 0}catch(r){let i=r&&r instanceof zi?"addEventSizeExceeded":"addEvent";U&&f.error(r),await t.stop({reason:i});let s=$();s&&s.recordDroppedEvent("internal_sdk_error","replay")}}function Bl(t,e){if(!t.eventBuffer||t.isPaused()||!t.isEnabled())return!1;let n=Hi(e.timestamp);return n+t.timeouts.sessionIdlePause<Date.now()?!1:n>t.getContext().initialTimestamp+t.getOptions().maxReplayDuration?(st(`[Replay] Skipping event with timestamp ${n} because it is after maxReplayDuration`,t.getOptions()._experiments.traceInternals),!1):!0}function Gg(t,e){try{if(typeof e=="function"&&Kg(t))return e(t)}catch(n){return U&&f.error("[Replay] An error occured in the `beforeAddRecordingEvent` callback, skipping the event...",n),null}return t}function Yi(t){return!t.type}function li(t){return t.type==="transaction"}function Xg(t){return t.type==="replay_event"}function aa(t){return t.type==="feedback"}function Hl(t){let e=t_();return(n,r)=>{if(!t.isEnabled()||!Yi(n)&&!li(n))return;let i=r&&r.statusCode;if(!(e&&(!i||i<200||i>=300))){if(li(n)){Qg(t,n);return}Zg(t,n)}}}function Qg(t,e){let n=t.getContext();e.contexts&&e.contexts.trace&&e.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(e.contexts.trace.trace_id)}function Zg(t,e){let n=t.getContext();if(e.event_id&&n.errorIds.size<100&&n.errorIds.add(e.event_id),t.recordingMode!=="buffer"||!e.tags||!e.tags.replayId)return;let{beforeErrorSampling:r}=t.getOptions();typeof r=="function"&&!r(e)||setTimeout(()=>{t.sendBufferedReplayOrFlush()})}function t_(){let t=$();if(!t)return!1;let e=t.getTransport();return e&&e.send.__sentry__baseTransport__||!1}function e_(t){return e=>{!t.isEnabled()||!Yi(e)||n_(t,e)}}function n_(t,e){let n=e.exception&&e.exception.values&&e.exception.values[0].value;if(typeof n=="string"&&(n.match(/reactjs\.org\/docs\/error-decoder\.html\?invariant=(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i))){let r=Et({category:"replay.hydrate-error"});Sn(t,r)}}function r_(t,e){return t.type||!t.exception||!t.exception.values||!t.exception.values.length?!1:!!(e.originalException&&e.originalException.__rrweb__)}function i_(t,e){t.triggerUserActivity(),t.addUpdate(()=>e.timestamp?(t.throttledAddEvent({type:C.Custom,timestamp:e.timestamp*1e3,data:{tag:"breadcrumb",payload:{timestamp:e.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:e.event_id}}}}),!1):!0)}function s_(t,e){return t.recordingMode!=="buffer"||e.message===Li||!e.exception||e.type?!1:Pl(t.getOptions().errorSampleRate)}function a_(t,e=!1){let n=e?Hl(t):void 0;return Object.assign((r,i)=>t.isEnabled()?Xg(r)?(delete r.breadcrumbs,r):!Yi(r)&&!li(r)&&!aa(r)||!t.checkAndHandleExpiredSession()?r:aa(r)?(t.flush(),r.contexts.feedback.replay_id=t.getSessionId(),i_(t,r),r):r_(r,i)&&!t.getOptions()._experiments.captureExceptions?(U&&f.log("[Replay] Ignoring error from rrweb internals",r),null):((s_(t,r)||t.recordingMode==="session")&&(r.tags={...r.tags,replayId:t.getSessionId()}),n&&n(r,{statusCode:200}),r):r,{id:"Replay"})}function fr(t,e){return e.map(({type:n,start:r,end:i,name:s,data:a})=>{let o=t.throttledAddEvent({type:C.Custom,timestamp:r,data:{tag:"performanceSpan",payload:{op:n,description:s,startTimestamp:r,endTimestamp:i,data:a}}});return typeof o=="string"?Promise.resolve(null):o})}function o_(t){let{from:e,to:n}=t,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:e}}}function l_(t){return e=>{if(!t.isEnabled())return;let n=o_(e);n!==null&&(t.getContext().urls.push(n.name),t.triggerUserActivity(),t.addUpdate(()=>(fr(t,[n]),!1)))}}function c_(t,e){return U&&t.getOptions()._experiments.traceInternals?!1:Vd(e,$())}function gr(t,e){t.isEnabled()&&e!==null&&(c_(t,e.name)||t.addUpdate(()=>(fr(t,[e]),!0)))}function u_(t){let{startTimestamp:e,endTimestamp:n,fetchData:r,response:i}=t;if(!n)return null;let{method:s,url:a}=r;return{type:"resource.fetch",start:e/1e3,end:n/1e3,name:a,data:{method:s,statusCode:i?i.status:void 0}}}function d_(t){return e=>{if(!t.isEnabled())return;let n=u_(e);gr(t,n)}}function p_(t){let{startTimestamp:e,endTimestamp:n,xhr:r}=t,i=r[Ht];if(!e||!n||!i)return null;let{method:s,url:a,status_code:o}=i;return a===void 0?null:{type:"resource.xhr",name:a,start:e/1e3,end:n/1e3,data:{method:s,statusCode:o}}}function h_(t){return e=>{if(!t.isEnabled())return;let n=p_(e);gr(t,n)}}function _r(t,e){if(t)try{if(typeof t=="string")return e.encode(t).length;if(t instanceof URLSearchParams)return e.encode(t.toString()).length;if(t instanceof FormData){let n=Vl(t);return e.encode(n).length}if(t instanceof Blob)return t.size;if(t instanceof ArrayBuffer)return t.byteLength}catch{}}function zl(t){if(!t)return;let e=parseInt(t,10);return isNaN(e)?void 0:e}function ql(t){try{if(typeof t=="string")return[t];if(t instanceof URLSearchParams)return[t.toString()];if(t instanceof FormData)return[Vl(t)];if(!t)return[void 0]}catch{return U&&f.warn("[Replay] Failed to serialize body",t),[void 0,"BODY_PARSE_ERROR"]}return U&&f.info("[Replay] Skipping network body because of body type",t),[void 0,"UNPARSEABLE_BODY_TYPE"]}function tr(t,e){if(!t)return{headers:{},size:void 0,_meta:{warnings:[e]}};let n={...t._meta},r=n.warnings||[];return n.warnings=[...r,e],t._meta=n,t}function Wl(t,e){if(!e)return null;let{startTimestamp:n,endTimestamp:r,url:i,method:s,statusCode:a,request:o,response:l}=e;return{type:t,start:n/1e3,end:r/1e3,name:i,data:ot({method:s,statusCode:a,request:o,response:l})}}function un(t){return{headers:{},size:t,_meta:{warnings:["URL_SKIPPED"]}}}function qt(t,e,n){if(!e&&Object.keys(t).length===0)return;if(!e)return{headers:t};if(!n)return{headers:t,size:e};let r={headers:t,size:e},{body:i,warnings:s}=m_(n);return r.body=i,s&&s.length>0&&(r._meta={warnings:s}),r}function ci(t,e){return Object.keys(t).reduce((n,r)=>{let i=r.toLowerCase();return e.includes(i)&&t[r]&&(n[i]=t[r]),n},{})}function Vl(t){return new URLSearchParams(t).toString()}function m_(t){if(!t||typeof t!="string")return{body:t};let e=t.length>qs,n=f_(t);if(e){let r=t.slice(0,qs);return n?{body:r,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${r}…`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(t)}}catch{}return{body:t}}function f_(t){let e=t[0],n=t[t.length-1];return e==="["&&n==="]"||e==="{"&&n==="}"}function er(t,e){let n=g_(t);return Ue(n,e)}function g_(t,e=H.document.baseURI){if(t.startsWith("http://")||t.startsWith("https://")||t.startsWith(H.location.origin))return t;let n=new URL(t,e);if(n.origin!==new URL(e).origin)return t;let r=n.href;return!t.endsWith("/")&&r.endsWith("/")?r.slice(0,-1):r}async function __(t,e,n){try{let r=await v_(t,e,n),i=Wl("resource.fetch",r);gr(n.replay,i)}catch(r){U&&f.error("[Replay] Failed to capture fetch breadcrumb",r)}}function y_(t,e,n){let{input:r,response:i}=e,s=r?Yl(r):void 0,a=_r(s,n.textEncoder),o=i?zl(i.headers.get("content-length")):void 0;a!==void 0&&(t.data.request_body_size=a),o!==void 0&&(t.data.response_body_size=o)}async function v_(t,e,n){let r=Date.now(),{startTimestamp:i=r,endTimestamp:s=r}=e,{url:a,method:o,status_code:l=0,request_body_size:c,response_body_size:d}=t.data,u=er(a,n.networkDetailAllowUrls)&&!er(a,n.networkDetailDenyUrls),p=u?S_(n,e.input,c):un(c),h=await b_(u,n,e.response,d);return{startTimestamp:i,endTimestamp:s,url:a,method:o,statusCode:l,request:p,response:h}}function S_({networkCaptureBodies:t,networkRequestHeaders:e},n,r){let i=n?w_(n,e):{};if(!t)return qt(i,r,void 0);let s=Yl(n),[a,o]=ql(s),l=qt(i,r,a);return o?tr(l,o):l}async function b_(t,{networkCaptureBodies:e,textEncoder:n,networkResponseHeaders:r},i,s){if(!t&&s!==void 0)return un(s);let a=i?Kl(i.headers,r):{};if(!i||!e&&s!==void 0)return qt(a,s,void 0);let[o,l]=await T_(i),c=k_(o,{networkCaptureBodies:e,textEncoder:n,responseBodySize:s,captureDetails:t,headers:a});return l?tr(c,l):c}function k_(t,{networkCaptureBodies:e,textEncoder:n,responseBodySize:r,captureDetails:i,headers:s}){try{let a=t&&t.length&&r===void 0?_r(t,n):r;return i?e?qt(s,a,t):qt(s,a,void 0):un(a)}catch(a){return U&&f.warn("[Replay] Failed to serialize response body",a),qt(s,r,void 0)}}async function T_(t){let e=E_(t);if(!e)return[void 0,"BODY_PARSE_ERROR"];try{return[await I_(e)]}catch(n){return U&&f.warn("[Replay] Failed to get text body from response",n),[void 0,"BODY_PARSE_ERROR"]}}function Yl(t=[]){if(!(t.length!==2||typeof t[1]!="object"))return t[1].body}function Kl(t,e){let n={};return e.forEach(r=>{t.get(r)&&(n[r]=t.get(r))}),n}function w_(t,e){return t.length===1&&typeof t[0]!="string"?oa(t[0],e):t.length===2?oa(t[1],e):{}}function oa(t,e){if(!t)return{};let n=t.headers;return n?n instanceof Headers?Kl(n,e):Array.isArray(n)?{}:ci(n,e):{}}function E_(t){try{return t.clone()}catch(e){U&&f.warn("[Replay] Failed to clone response body",e)}}function I_(t){return new Promise((e,n)=>{let r=setTimeout(()=>n(new Error("Timeout while trying to read response body")),500);x_(t).then(i=>e(i),i=>n(i)).finally(()=>clearTimeout(r))})}async function x_(t){return await t.text()}async function C_(t,e,n){try{let r=M_(t,e,n),i=Wl("resource.xhr",r);gr(n.replay,i)}catch(r){U&&f.error("[Replay] Failed to capture xhr breadcrumb",r)}}function R_(t,e,n){let{xhr:r,input:i}=e;if(!r)return;let s=_r(i,n.textEncoder),a=r.getResponseHeader("content-length")?zl(r.getResponseHeader("content-length")):N_(r.response,r.responseType,n.textEncoder);s!==void 0&&(t.data.request_body_size=s),a!==void 0&&(t.data.response_body_size=a)}function M_(t,e,n){let r=Date.now(),{startTimestamp:i=r,endTimestamp:s=r,input:a,xhr:o}=e,{url:l,method:c,status_code:d=0,request_body_size:u,response_body_size:p}=t.data;if(!l)return null;if(!o||!er(l,n.networkDetailAllowUrls)||er(l,n.networkDetailDenyUrls)){let b=un(u),v=un(p);return{startTimestamp:i,endTimestamp:s,url:l,method:c,statusCode:d,request:b,response:v}}let h=o[Ht],m=h?ci(h.request_headers,n.networkRequestHeaders):{},g=ci(O_(o),n.networkResponseHeaders),[_,y]=n.networkCaptureBodies?ql(a):[void 0],[S,w]=n.networkCaptureBodies?A_(o):[void 0],D=qt(m,u,_),F=qt(g,p,S);return{startTimestamp:i,endTimestamp:s,url:l,method:c,statusCode:d,request:y?tr(D,y):D,response:w?tr(F,w):F}}function O_(t){let e=t.getAllResponseHeaders();return e?e.split(`\r
`).reduce((n,r)=>{let[i,s]=r.split(": ");return n[i.toLowerCase()]=s,n},{}):{}}function A_(t){let e=[];try{return[t.responseText]}catch(n){e.push(n)}try{return D_(t.response,t.responseType)}catch(n){e.push(n)}return U&&f.warn("[Replay] Failed to get xhr response body",...e),[void 0]}function D_(t,e){try{if(typeof t=="string")return[t];if(t instanceof Document)return[t.body.outerHTML];if(e==="json"&&t&&typeof t=="object")return[JSON.stringify(t)];if(!t)return[void 0]}catch{return U&&f.warn("[Replay] Failed to serialize body",t),[void 0,"BODY_PARSE_ERROR"]}return U&&f.info("[Replay] Skipping network body because of body type",t),[void 0,"UNPARSEABLE_BODY_TYPE"]}function N_(t,e,n){try{let r=e==="json"&&t&&typeof t=="object"?JSON.stringify(t):t;return _r(r,n)}catch{return}}function L_(t){let e=$();try{let n=new TextEncoder,{networkDetailAllowUrls:r,networkDetailDenyUrls:i,networkCaptureBodies:s,networkRequestHeaders:a,networkResponseHeaders:o}=t.getOptions(),l={replay:t,textEncoder:n,networkDetailAllowUrls:r,networkDetailDenyUrls:i,networkCaptureBodies:s,networkRequestHeaders:a,networkResponseHeaders:o};e&&e.on?e.on("beforeAddBreadcrumb",(c,d)=>P_(l,c,d)):(_i(d_(t)),yi(h_(t)))}catch{}}function P_(t,e,n){if(e.data)try{$_(e)&&j_(n)&&(R_(e,n,t),C_(e,n,t)),F_(e)&&U_(n)&&(y_(e,n,t),__(e,n,t))}catch{U&&f.warn("Error when enriching network breadcrumb")}}function $_(t){return t.category==="xhr"}function F_(t){return t.category==="fetch"}function j_(t){return t&&t.xhr}function U_(t){return t&&t.response}var la=null;function B_(t){return!!t.category}var H_=t=>e=>{if(!t.isEnabled())return;let n=z_(e);n&&Sn(t,n)};function z_(t){let e=t.getLastBreadcrumb&&t.getLastBreadcrumb();return la===e||!e||(la=e,!B_(e)||["fetch","xhr","sentry.event","sentry.transaction"].includes(e.category)||e.category.startsWith("ui."))?null:e.category==="console"?q_(e):Et(e)}function q_(t){let e=t.data&&t.data.arguments;if(!Array.isArray(e)||e.length===0)return Et(t);let n=!1,r=e.map(i=>{if(!i)return i;if(typeof i=="string")return i.length>An?(n=!0,`${i.slice(0,An)}…`):i;if(typeof i=="object")try{let s=kt(i,7);return JSON.stringify(s).length>An?(n=!0,`${JSON.stringify(s,null,2).slice(0,An)}…`):s}catch{}return i});return Et({...t,data:{...t.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}function W_(t){let e=ct(),n=$();e.addScopeListener(H_(t)),Na(kg(t)),dn(l_(t)),L_(t);let r=a_(t,!ca(n));n&&n.addEventProcessor?n.addEventProcessor(r):go(r),ca(n)&&(n.on("beforeSendEvent",e_(t)),n.on("afterSendEvent",Hl(t)),n.on("createDsc",i=>{let s=t.getSessionId();s&&t.isEnabled()&&t.recordingMode==="session"&&t.checkAndHandleExpiredSession()&&(i.replay_id=s)}),n.on("startTransaction",i=>{t.lastTransaction=i}),n.on("finishTransaction",i=>{t.lastTransaction=i}),n.on("beforeSendFeedback",(i,s)=>{let a=t.getSessionId();s&&s.includeReplay&&t.isEnabled()&&a&&i.contexts&&i.contexts.feedback&&(i.contexts.feedback.replay_id=a)}))}function ca(t){return!!(t&&t.on)}async function V_(t){try{return Promise.all(fr(t,[Y_(H.performance.memory)]))}catch{return[]}}function Y_(t){let{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}=t,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}}}}function K_(t,e,n){let r,i,s,a=n&&n.maxWait?Math.max(n.maxWait,e):0;function o(){return l(),r=t(),r}function l(){i!==void 0&&clearTimeout(i),s!==void 0&&clearTimeout(s),i=s=void 0}function c(){return i!==void 0||s!==void 0?o():r}function d(){return i&&clearTimeout(i),i=setTimeout(o,e),a&&s===void 0&&(s=setTimeout(o,a)),r}return d.cancel=l,d.flush=c,d}function J_(t){let e=!1;return(n,r)=>{if(!t.checkAndHandleExpiredSession()){U&&f.warn("[Replay] Received replay event after session expired.");return}let i=r||!e;e=!0,t.clickDetector&&_g(t.clickDetector,n),t.addUpdate(()=>{if(t.recordingMode==="buffer"&&i&&t.setInitialState(),!Vi(t,n,i))return!0;if(!i)return!1;if(X_(t,i),t.session&&t.session.previousSessionId)return!0;if(t.recordingMode==="buffer"&&t.session&&t.eventBuffer){let s=t.eventBuffer.getEarliestTimestamp();s&&(st(`[Replay] Updating session start time to earliest event in buffer to ${new Date(s)}`,t.getOptions()._experiments.traceInternals),t.session.started=s,t.getOptions().stickySession&&Wi(t.session))}return t.recordingMode==="session"&&t.flush(),!0})}}function G_(t){let e=t.getOptions();return{type:C.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:t.isRecordingCanvas(),sessionSampleRate:e.sessionSampleRate,errorSampleRate:e.errorSampleRate,useCompressionOption:e.useCompression,blockAllMedia:e.blockAllMedia,maskAllText:e.maskAllText,maskAllInputs:e.maskAllInputs,useCompression:t.eventBuffer?t.eventBuffer.type==="worker":!1,networkDetailHasUrls:e.networkDetailAllowUrls.length>0,networkCaptureBodies:e.networkCaptureBodies,networkRequestHasHeaders:e.networkRequestHeaders.length>0,networkResponseHasHeaders:e.networkResponseHeaders.length>0}}}}function X_(t,e){!e||!t.session||t.session.segmentId!==0||Vi(t,G_(t),!1)}function Q_(t,e,n,r){return Zt(za(t,bi(t),r,n),[[{type:"replay_event"},t],[{type:"replay_recording",length:typeof e=="string"?new TextEncoder().encode(e).length:e.length},e]])}function Z_({recordingData:t,headers:e}){let n,r=`${JSON.stringify(e)}
`;if(typeof t=="string")n=`${r}${t}`;else{let i=new TextEncoder().encode(r);n=new Uint8Array(i.length+t.length),n.set(i),n.set(t,i.length)}return n}async function ty({client:t,scope:e,replayId:n,event:r}){let i=typeof t._integrations=="object"&&t._integrations!==null&&!Array.isArray(t._integrations)?Object.keys(t._integrations):void 0,s={event_id:n,integrations:i};t.emit&&t.emit("preprocessEvent",r,s);let a=await Ja(t.getOptions(),r,s,e,t,te());if(!a)return null;a.platform=a.platform||"javascript";let o=t.getSdkMetadata&&t.getSdkMetadata(),{name:l,version:c}=o&&o.sdk||{};return a.sdk={...a.sdk,name:l||"sentry.javascript.unknown",version:c||"0.0.0"},a}async function ey({recordingData:t,replayId:e,segmentId:n,eventContext:r,timestamp:i,session:s}){let a=Z_({recordingData:t,headers:{segment_id:n}}),{urls:o,errorIds:l,traceIds:c,initialTimestamp:d}=r,u=$(),p=ct(),h=u&&u.getTransport(),m=u&&u.getDsn();if(!u||!h||!m||!s.sampled)return;let g={type:Fm,replay_start_timestamp:d/1e3,timestamp:i/1e3,error_ids:l,trace_ids:c,urls:o,replay_id:e,segment_id:n,replay_type:s.sampled},_=await ty({scope:p,client:u,replayId:e,event:g});if(!_){u.recordDroppedEvent("event_processor","replay",g),st("An event processor returned `null`, will not send event.");return}delete _.sdkProcessingMetadata;let y=Q_(_,a,m,u.getOptions().tunnel),S;try{S=await h.send(y)}catch(D){let F=new Error(Li);try{F.cause=D}catch{}throw F}if(!S)return S;if(typeof S.statusCode=="number"&&(S.statusCode<200||S.statusCode>=300))throw new Jl(S.statusCode);let w=Wa({},S);if(qa(w,"replay"))throw new Gl(w);return S}var Jl=class extends Error{constructor(t){super(`Transport returned status code ${t}`)}},Gl=class extends Error{constructor(t){super("Rate limit hit"),this.rateLimits=t}};async function Xl(t,e={count:0,interval:qm}){let{recordingData:n,options:r}=t;if(n.length)try{return await ey(t),!0}catch(i){if(i instanceof Jl||i instanceof Gl)throw i;if(Xu("Replays",{_retryCount:e.count}),U&&r._experiments&&r._experiments.captureExceptions&&cr(i),e.count>=Wm){let s=new Error(`${Li} - max retries exceeded`);try{s.cause=i}catch{}throw s}return e.interval*=++e.count,new Promise((s,a)=>{setTimeout(async()=>{try{await Xl(t,e),s(!0)}catch(o){a(o)}},e.interval)})}}var Ql="__THROTTLED",ny="__SKIPPED";function ry(t,e,n){let r=new Map,i=o=>{let l=o-n;r.forEach((c,d)=>{d<l&&r.delete(d)})},s=()=>[...r.values()].reduce((o,l)=>o+l,0),a=!1;return(...o)=>{let l=Math.floor(Date.now()/1e3);if(i(l),s()>=e){let d=a;return a=!0,d?ny:Ql}a=!1;let c=r.get(l)||0;return r.set(l,c+1),t(...o)}}var iy=class se{constructor({options:e,recordingOptions:n}){se.prototype.__init.call(this),se.prototype.__init2.call(this),se.prototype.__init3.call(this),se.prototype.__init4.call(this),se.prototype.__init5.call(this),se.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:jm,sessionIdleExpire:Um},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=n,this._options=e,this._debouncedFlush=K_(()=>this._flush(),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=ry((a,o)=>Jg(this,a,o),300,5);let{slowClickTimeout:r,slowClickIgnoreSelectors:i}=this.getOptions(),s=r?{threshold:Math.min(Vm,r),timeout:r,scrollTimeout:Ym,ignoreSelector:i?i.join(","):""}:void 0;s&&(this.clickDetector=new hg(this,s))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return!!this._canvas}getOptions(){return this._options}initializeSampling(e){let{errorSampleRate:n,sessionSampleRate:r}=this._options;if(!(n<=0&&r<=0)){if(this._initializeSessionForSampling(e),!this.session){this._handleException(new Error("Unable to initialize and create session"));return}this.session.sampled!==!1&&(this.recordingMode=this.session.sampled==="buffer"&&this.session.segmentId===0?"buffer":"session",Ee(`[Replay] Starting replay in ${this.recordingMode} mode`,this._options._experiments.traceInternals),this._initializeRecording())}}start(){if(this._isEnabled&&this.recordingMode==="session")throw new Error("Replay recording is already in progress");if(this._isEnabled&&this.recordingMode==="buffer")throw new Error("Replay buffering is in progress, call `flush()` to save the replay");Ee("[Replay] Starting replay in session mode",this._options._experiments.traceInternals),this._updateUserActivity();let e=Lr({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}startBuffering(){if(this._isEnabled)throw new Error("Replay recording is already in progress");Ee("[Replay] Starting replay in buffer mode",this._options._experiments.traceInternals);let e=Lr({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{let e=this._canvas;this._stopRecording=zt({...this._recordingOptions,...this.recordingMode==="buffer"&&{checkoutEveryNms:zm},emit:J_(this),onMutation:this._onMutationHandler,...e?{recordCanvas:e.recordCanvas,getCanvasManager:e.getCanvasManager,sampling:e.sampling,dataURLOptions:e.dataURLOptions}:{}})}catch(e){this._handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this._handleException(e),!1}}async stop({forceFlush:e=!1,reason:n}={}){if(this._isEnabled){this._isEnabled=!1;try{st(`[Replay] Stopping Replay${n?` triggered by ${n}`:""}`,this._options._experiments.traceInternals),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,qg(this)}catch(r){this._handleException(r)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording(),st("[Replay] Pausing replay",this._options._experiments.traceInternals))}resume(){!this._isPaused||!this._checkSession()||(this._isPaused=!1,this.startRecording(),st("[Replay] Resuming replay",this._options._experiments.traceInternals))}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if(this.recordingMode==="session")return this.flushImmediate();let n=Date.now();st("[Replay] Converting buffer to session",this._options._experiments.traceInternals),await this.flushImmediate();let r=this.stopRecording();!e||!r||this.recordingMode!=="session"&&(this.recordingMode="session",this.session&&(this._updateUserActivity(n),this._updateSessionActivity(n),this._maybeSaveSession()),this.startRecording())}addUpdate(e){let n=e();this.recordingMode!=="buffer"&&n!==!0&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return this.recordingMode==="buffer"?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(this._lastActivity&&oi(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&this.session.sampled==="session"){this.pause();return}return!!this._checkSession()}setInitialState(){let e=`${H.location.pathname}${H.location.hash}${H.location.search}`,n=`${H.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=n,this._context.initialTimestamp=Date.now(),this._context.urls.push(n)}throttledAddEvent(e,n){let r=this._throttledAddEvent(e,n);if(r===Ql){let i=Et({category:"replay.throttled"});this.addUpdate(()=>!Vi(this,{type:og,timestamp:i.timestamp||0,data:{tag:"breadcrumb",payload:i,metric:!0}}))}return r}getCurrentRoute(){let e=this.lastTransaction||ct().getTransaction(),n=(e&&X(e).data||{})[rt];if(!(!e||!n||!["route","custom"].includes(n)))return X(e).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=Bg({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_handleException(e){U&&f.error("[Replay]",e),U&&this._options._experiments&&this._options._experiments.captureExceptions&&cr(e)}_initializeSessionForSampling(e){let n=this._options.errorSampleRate>0,r=Lr({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:n});this.session=r}_checkSession(){if(!this.session)return!1;let e=this.session;return jl(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})?(this._refreshSession(e),!1):!0}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{H.document.addEventListener("visibilitychange",this._handleVisibilityChange),H.addEventListener("blur",this._handleWindowBlur),H.addEventListener("focus",this._handleWindowFocus),H.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(W_(this),this._hasInitializedCoreListeners=!0)}catch(e){this._handleException(e)}this._performanceCleanupCallback=Lg(this)}_removeListeners(){try{H.document.removeEventListener("visibilitychange",this._handleVisibilityChange),H.removeEventListener("blur",this._handleWindowBlur),H.removeEventListener("focus",this._handleWindowFocus),H.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this._handleException(e)}}__init(){this._handleVisibilityChange=()=>{H.document.visibilityState==="visible"?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{let e=Et({category:"ui.blur"});this._doChangeToBackgroundTasks(e)}}__init3(){this._handleWindowFocus=()=>{let e=Et({category:"ui.focus"});this._doChangeToForegroundTasks(e)}}__init4(){this._handleKeyboardEvent=e=>{Ig(this,e)}}_doChangeToBackgroundTasks(e){!this.session||Fl(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush())}_doChangeToForegroundTasks(e){if(this.session){if(!this.checkAndHandleExpiredSession()){st("[Replay] Document has become active, but session has expired");return}e&&this._createCustomBreadcrumb(e)}}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate(()=>{this.throttledAddEvent({type:C.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}_addPerformanceEntries(){let e=Rg(this.performanceEntries).concat(this.replayPerformanceEntries);return this.performanceEntries=[],this.replayPerformanceEntries=[],Promise.all(fr(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){let{session:e,eventBuffer:n}=this;if(!e||!n||e.segmentId)return;let r=n.getEarliestTimestamp();r&&r<this._context.initialTimestamp&&(this._context.initialTimestamp=r)}_popEventContext(){let e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){let e=this.getSessionId();if(!this.session||!this.eventBuffer||!e){U&&f.error("[Replay] No session or eventBuffer found to flush.");return}if(await this._addPerformanceEntries(),!(!this.eventBuffer||!this.eventBuffer.hasEvents)&&(await V_(this),!!this.eventBuffer&&e===this.getSessionId()))try{this._updateInitialTimestampFromEventBuffer();let n=Date.now();if(n-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");let r=this._popEventContext(),i=this.session.segmentId++;this._maybeSaveSession();let s=await this.eventBuffer.finish();await Xl({replayId:e,recordingData:s,segmentId:i,eventContext:r,session:this.session,options:this.getOptions(),timestamp:n})}catch(n){this._handleException(n),this.stop({reason:"sendReplay"});let r=$();r&&r.recordDroppedEvent("send_error","replay")}}__init5(){this._flush=async({force:e=!1}={})=>{if(!this._isEnabled&&!e)return;if(!this.checkAndHandleExpiredSession()){U&&f.error("[Replay] Attempting to finish replay event after session expired.");return}if(!this.session)return;let n=this.session.started,r=Date.now()-n;this._debouncedFlush.cancel();let i=r<this._options.minReplayDuration,s=r>this._options.maxReplayDuration+5e3;if(i||s){st(`[Replay] Session duration (${Math.floor(r/1e3)}s) is too ${i?"short":"long"}, not sending replay.`,this._options._experiments.traceInternals),i&&this._debouncedFlush();return}let a=this.eventBuffer;if(a&&this.session.segmentId===0&&!a.hasCheckout&&st("[Replay] Flushing initial segment without checkout.",this._options._experiments.traceInternals),!this._flushLock){this._flushLock=this._runFlush(),await this._flushLock,this._flushLock=void 0;return}try{await this._flushLock}catch(o){U&&f.error(o)}finally{this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&Wi(this.session)}__init6(){this._onMutationHandler=e=>{let n=e.length,r=this._options.mutationLimit,i=this._options.mutationBreadcrumbLimit,s=r&&n>r;if(n>i||s){let a=Et({category:"replay.mutations",data:{count:n,limit:s}});this._createCustomBreadcrumb(a)}return s?(this.stop({reason:"mutationLimit",forceFlush:this.recordingMode==="session"}),!1):!0}}};function Ge(t,e,n,r){let i=typeof r=="string"?r.split(","):[],s=[...t,...i,...e];return typeof n<"u"&&(typeof n=="string"&&s.push(`.${n}`),Gt(()=>{console.warn("[Replay] You are using a deprecated configuration item for privacy. Read the documentation on how to use the new privacy configuration.")})),s.join(",")}function sy({mask:t,unmask:e,block:n,unblock:r,ignore:i,blockClass:s,blockSelector:a,maskTextClass:o,maskTextSelector:l,ignoreClass:c}){let d=['base[href="/"]'],u=Ge(t,[".sentry-mask","[data-sentry-mask]"],o,l),p=Ge(e,[".sentry-unmask","[data-sentry-unmask]"]),h={maskTextSelector:u,unmaskTextSelector:p,blockSelector:Ge(n,[".sentry-block","[data-sentry-block]",...d],s,a),unblockSelector:Ge(r,[".sentry-unblock","[data-sentry-unblock]"]),ignoreSelector:Ge(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'],c)};return s instanceof RegExp&&(h.blockClass=s),o instanceof RegExp&&(h.maskTextClass=o),h}function ay({el:t,key:e,maskAttributes:n,maskAllText:r,privacyOptions:i,value:s}){return!r||i.unmaskTextSelector&&t.matches(i.unmaskTextSelector)?s:n.includes(e)||e==="value"&&t.tagName==="INPUT"&&["submit","button"].includes(t.getAttribute("type")||"")?s.replace(/[\S]/g,"*"):s}var ua='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',oy=["content-length","content-type","accept"],da=!1,ly=t=>new Zl(t),Zl=class tc{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:e=Bm,flushMaxDelay:n=Hm,minReplayDuration:r=Km,maxReplayDuration:i=Ws,stickySession:s=!0,useCompression:a=!0,workerUrl:o,_experiments:l={},sessionSampleRate:c,errorSampleRate:d,maskAllText:u=!0,maskAllInputs:p=!0,blockAllMedia:h=!0,mutationBreadcrumbLimit:m=750,mutationLimit:g=1e4,slowClickTimeout:_=7e3,slowClickIgnoreSelectors:y=[],networkDetailAllowUrls:S=[],networkDetailDenyUrls:w=[],networkCaptureBodies:D=!0,networkRequestHeaders:F=[],networkResponseHeaders:b=[],mask:v=[],maskAttributes:G=["title","placeholder"],unmask:z=[],block:E=[],unblock:x=[],ignore:W=[],maskFn:V,beforeAddRecordingEvent:dt,beforeErrorSampling:pt,blockClass:j,blockSelector:ht,maskInputOptions:Q,maskTextClass:mt,maskTextSelector:ft,ignoreClass:yr}={}){this.name=tc.id;let me=sy({mask:v,unmask:z,block:E,unblock:x,ignore:W,blockClass:j,blockSelector:ht,maskTextClass:mt,maskTextSelector:ft,ignoreClass:yr});if(this._recordingOptions={maskAllInputs:p,maskAllText:u,maskInputOptions:{...Q||{},password:!0},maskTextFn:V,maskInputFn:V,maskAttributeFn:($t,Ve,Ye)=>ay({maskAttributes:G,maskAllText:u,privacyOptions:me,key:$t,value:Ve,el:Ye}),...me,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:$t=>{try{$t.__rrweb__=!0}catch{}}},this._initialOptions={flushMinDelay:e,flushMaxDelay:n,minReplayDuration:Math.min(r,Jm),maxReplayDuration:Math.min(i,Ws),stickySession:s,sessionSampleRate:c,errorSampleRate:d,useCompression:a,workerUrl:o,blockAllMedia:h,maskAllInputs:p,maskAllText:u,mutationBreadcrumbLimit:m,mutationLimit:g,slowClickTimeout:_,slowClickIgnoreSelectors:y,networkDetailAllowUrls:S,networkDetailDenyUrls:w,networkCaptureBodies:D,networkRequestHeaders:pa(F),networkResponseHeaders:pa(b),beforeAddRecordingEvent:dt,beforeErrorSampling:pt,_experiments:l},typeof c=="number"&&(console.warn(`[Replay] You are passing \`sessionSampleRate\` to the Replay integration.
This option is deprecated and will be removed soon.
Instead, configure \`replaysSessionSampleRate\` directly in the SDK init options, e.g.:
Sentry.init({ replaysSessionSampleRate: ${c} })`),this._initialOptions.sessionSampleRate=c),typeof d=="number"&&(console.warn(`[Replay] You are passing \`errorSampleRate\` to the Replay integration.
This option is deprecated and will be removed soon.
Instead, configure \`replaysOnErrorSampleRate\` directly in the SDK init options, e.g.:
Sentry.init({ replaysOnErrorSampleRate: ${d} })`),this._initialOptions.errorSampleRate=d),this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${ua}`:ua),this._isInitialized&&us())throw new Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return da}set _isInitialized(e){da=e}setupOnce(){us()&&(this._setup(),setTimeout(()=>this._initialize()))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:this._replay.recordingMode==="session"}):Promise.resolve()}flush(e){return!this._replay||!this._replay.isEnabled()?Promise.resolve():this._replay.sendBufferedReplayOrFlush(e)}getReplayId(){if(!(!this._replay||!this._replay.isEnabled()))return this._replay.getSessionId()}_initialize(){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(),this._replay.initializeSampling())}_setup(){let e=cy(this._initialOptions);this._replay=new iy({options:e,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(){try{let e=$().getIntegrationByName("ReplayCanvas");if(!e)return;this._replay._canvas=e.getOptions()}catch{}}};Zl.__initStatic();function cy(t){let e=$(),n=e&&e.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...ot(t)};return n?(t.sessionSampleRate==null&&t.errorSampleRate==null&&n.replaysSessionSampleRate==null&&n.replaysOnErrorSampleRate==null&&Gt(()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),typeof n.replaysSessionSampleRate=="number"&&(r.sessionSampleRate=n.replaysSessionSampleRate),typeof n.replaysOnErrorSampleRate=="number"&&(r.errorSampleRate=n.replaysOnErrorSampleRate),r):(Gt(()=>{console.warn("SDK client is not available.")}),r)}function pa(t){return[...oy,...t.map(e=>e.toLowerCase())]}var ec={};I.Sentry&&I.Sentry.Integrations&&(ec=I.Sentry.Integrations);({...ec,...gp,...ul});function uy(t){let e={...t};xi(e,"svelte"),Pm(e),dy()}function dy(){let t,e=n=>(t===void 0&&(t=py()),t&&(n.modules={svelteKit:"latest",...n.modules}),n);e.id="svelteKitProcessor",go(e)}function py(){return fi("div#svelte-announcer")!==null}var nc={"routing.instrumentation":"@sentry/sveltekit"};function hy(t,e=!0,n=!0){e&&my(t),n&&fy(t)}function my(t){let e=I&&I.location&&I.location.pathname,n=t({name:e,op:"pageload",origin:"auto.pageload.sveltekit",description:e,tags:{...nc},attributes:{[rt]:"url"}});_a.subscribe(r=>{if(!r)return;let i=r.route&&r.route.id;n&&i&&(n.updateName(i),n.setAttribute(rt,"route"))})}function fy(t){let e,n;ya.subscribe(r=>{if(!r){e&&(e.end(),e=void 0);return}let i=r.from,s=r.to,a=i&&i.url.pathname||I&&I.location&&I.location.pathname,o=s&&s.url.pathname;if(a===o)return;let l=i&&i.route.id,c=s&&s.route.id;n=xt(),n||(n=t({name:c||o||"unknown",op:"navigation",origin:"auto.navigation.sveltekit",attributes:{[rt]:c?"route":"url"},tags:{...nc}})),n&&(e&&e.end(),e=n.startChild({op:"ui.sveltekit.routing",description:"SvelteKit Route Change",origin:"auto.ui.sveltekit"}),n.setTag("from",l))})}var ha=class extends hh{constructor(t){super({routingInstrumentation:hy,...t})}};function rc(t={}){let e={...yh({...t,instrumentNavigation:!1,instrumentPageLoad:!1})},n={...e.options,instrumentPageLoad:!0,instrumentNavigation:!0,...t};return{...e,options:n,afterAllSetup:r=>{e.afterAllSetup(r),n.instrumentPageLoad&&gy(r),n.instrumentNavigation&&_y(r)}}}function gy(t){let e=I&&I.location&&I.location.pathname;Ho(t,{name:e,op:"pageload",description:e,tags:{"routing.instrumentation":"@sentry/sveltekit"},attributes:{[At]:"auto.pageload.sveltekit",[rt]:"url"}});let n=De();_a.subscribe(r=>{if(!r)return;let i=r.route&&r.route.id;n&&i&&(n.updateName(i),n.setAttribute(rt,"route"))})}function _y(t){let e,n;ya.subscribe(r=>{if(!r){e&&(e.end(),e=void 0);return}let i=r.from,s=r.to,a=i&&i.url.pathname||I&&I.location&&I.location.pathname,o=s&&s.url.pathname;if(a===o)return;let l=i&&i.route.id,c=s&&s.route.id;n=De(),n||(zo(t,{name:c||o||"unknown",op:"navigation",attributes:{[At]:"auto.navigation.sveltekit",[rt]:c?"route":"url"},tags:{"routing.instrumentation":"@sentry/sveltekit"}}),n=De()),n&&(e&&e.end(),e=Ii({op:"ui.sveltekit.routing",name:"SvelteKit Route Change",attributes:{[At]:"auto.ui.sveltekit"}}),n.setAttribute("sentry.sveltekit.navigation.from",l||void 0))})}function yy(t){let e={defaultIntegrations:by(t),...t};xi(e,"sveltekit",["sveltekit","svelte"]),vy(e);let n=ky();uy(e),n&&Ty(n),ct().setTag("runtime","browser")}function vy(t){let{integrations:e}=t;e&&(Array.isArray(e)?t.integrations=ma(e):t.integrations=n=>{let r=e(n);return ma(r)})}function Sy(t){return!!t.afterAllSetup&&!!t.options}function ma(t){let e=t.find(n=>n.name==="BrowserTracing");if(!e)return t;if(Sy(e)){let{options:n}=e;t[t.indexOf(e)]=rc(n)}if(!(e instanceof ha)){let n=e.options;delete n.routingInstrumentation,t[t.indexOf(e)]=new ha(n)}return t}function by(t){if((typeof __SENTRY_TRACING__>"u"||__SENTRY_TRACING__)&&he(t))return[...cl(),rc()]}function ky(){let t=I,e=t.fetch;if(t._sentryFetchProxy&&e)return t.fetch=t._sentryFetchProxy,e}function Ty(t){let e=I;e._sentryFetchProxy=e.fetch,e.fetch=t}var wy=({error:t})=>{Gt(()=>{console.error(t)})},Ey=(t=wy)=>e=>{let n=(e==null?void 0:e.status)!==404?cr(e.error,{mechanism:{type:"sveltekit",handled:!1}}):void 0;return t(e,n)},Iy=(t,e)=>(yy({dsn:t,tracesSampleRate:1,replaysSessionSampleRate:.1,replaysOnErrorSampleRate:1,integrations:[ly()],...e==null?void 0:e.sentryOptions}),Ey);const xy=Iy(cc.PUBLIC_SENTRY_DNS),Cy=xy(({error:t,event:e})=>{console.error("An error occurred on the client side:",t,e)}),Fy={};function Ry(t){let e,n,r;var i=t[1][0];function s(a,o){return{props:{data:a[3],form:a[2]}}}return i&&(e=Re(i,s(t)),t[12](e)),{c(){e&&Ie(e.$$.fragment),n=Yt()},l(a){e&&ui(e.$$.fragment,a),n=Yt()},m(a,o){e&&xe(e,a,o),le(a,n,o),r=!0},p(a,o){if(o&2&&i!==(i=a[1][0])){if(e){rr();const l=e;Wt(l.$$.fragment,1,0,()=>{Ce(l,1)}),nr()}i?(e=Re(i,s(a)),a[12](e),Ie(e.$$.fragment),Vt(e.$$.fragment,1),xe(e,n.parentNode,n)):e=null}else if(i){const l={};o&8&&(l.data=a[3]),o&4&&(l.form=a[2]),e.$set(l)}},i(a){r||(e&&Vt(e.$$.fragment,a),r=!0)},o(a){e&&Wt(e.$$.fragment,a),r=!1},d(a){a&&Kt(n),t[12](null),e&&Ce(e,a)}}}function My(t){let e,n,r;var i=t[1][0];function s(a,o){return{props:{data:a[3],$$slots:{default:[Oy]},$$scope:{ctx:a}}}}return i&&(e=Re(i,s(t)),t[11](e)),{c(){e&&Ie(e.$$.fragment),n=Yt()},l(a){e&&ui(e.$$.fragment,a),n=Yt()},m(a,o){e&&xe(e,a,o),le(a,n,o),r=!0},p(a,o){if(o&2&&i!==(i=a[1][0])){if(e){rr();const l=e;Wt(l.$$.fragment,1,0,()=>{Ce(l,1)}),nr()}i?(e=Re(i,s(a)),a[11](e),Ie(e.$$.fragment),Vt(e.$$.fragment,1),xe(e,n.parentNode,n)):e=null}else if(i){const l={};o&8&&(l.data=a[3]),o&8215&&(l.$$scope={dirty:o,ctx:a}),e.$set(l)}},i(a){r||(e&&Vt(e.$$.fragment,a),r=!0)},o(a){e&&Wt(e.$$.fragment,a),r=!1},d(a){a&&Kt(n),t[11](null),e&&Ce(e,a)}}}function Oy(t){let e,n,r;var i=t[1][1];function s(a,o){return{props:{data:a[4],form:a[2]}}}return i&&(e=Re(i,s(t)),t[10](e)),{c(){e&&Ie(e.$$.fragment),n=Yt()},l(a){e&&ui(e.$$.fragment,a),n=Yt()},m(a,o){e&&xe(e,a,o),le(a,n,o),r=!0},p(a,o){if(o&2&&i!==(i=a[1][1])){if(e){rr();const l=e;Wt(l.$$.fragment,1,0,()=>{Ce(l,1)}),nr()}i?(e=Re(i,s(a)),a[10](e),Ie(e.$$.fragment),Vt(e.$$.fragment,1),xe(e,n.parentNode,n)):e=null}else if(i){const l={};o&16&&(l.data=a[4]),o&4&&(l.form=a[2]),e.$set(l)}},i(a){r||(e&&Vt(e.$$.fragment,a),r=!0)},o(a){e&&Wt(e.$$.fragment,a),r=!1},d(a){a&&Kt(n),t[10](null),e&&Ce(e,a)}}}function fa(t){let e,n=t[6]&&ga(t);return{c(){e=fc("div"),n&&n.c(),this.h()},l(r){e=gc(r,"DIV",{id:!0,"aria-live":!0,"aria-atomic":!0,style:!0});var i=_c(e);n&&n.l(i),i.forEach(Kt),this.h()},h(){Tr(e,"id","svelte-announcer"),Tr(e,"aria-live","assertive"),Tr(e,"aria-atomic","true"),Ct(e,"position","absolute"),Ct(e,"left","0"),Ct(e,"top","0"),Ct(e,"clip","rect(0 0 0 0)"),Ct(e,"clip-path","inset(50%)"),Ct(e,"overflow","hidden"),Ct(e,"white-space","nowrap"),Ct(e,"width","1px"),Ct(e,"height","1px")},m(r,i){le(r,e,i),n&&n.m(e,null)},p(r,i){r[6]?n?n.p(r,i):(n=ga(r),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(r){r&&Kt(e),n&&n.d()}}}function ga(t){let e;return{c(){e=yc(t[7])},l(n){e=vc(n,t[7])},m(n,r){le(n,e,r)},p(n,r){r&128&&Sc(e,n[7])},d(n){n&&Kt(e)}}}function Ay(t){let e,n,r,i,s;const a=[My,Ry],o=[];function l(d,u){return d[1][1]?0:1}e=l(t),n=o[e]=a[e](t);let c=t[5]&&fa(t);return{c(){n.c(),r=dc(),c&&c.c(),i=Yt()},l(d){n.l(d),r=pc(d),c&&c.l(d),i=Yt()},m(d,u){o[e].m(d,u),le(d,r,u),c&&c.m(d,u),le(d,i,u),s=!0},p(d,[u]){let p=e;e=l(d),e===p?o[e].p(d,u):(rr(),Wt(o[p],1,1,()=>{o[p]=null}),nr(),n=o[e],n?n.p(d,u):(n=o[e]=a[e](d),n.c()),Vt(n,1),n.m(r.parentNode,r)),d[5]?c?c.p(d,u):(c=fa(d),c.c(),c.m(i.parentNode,i)):c&&(c.d(1),c=null)},i(d){s||(Vt(n),s=!0)},o(d){Wt(n),s=!1},d(d){d&&(Kt(r),Kt(i)),o[e].d(d),c&&c.d(d)}}}function Dy(t,e,n){let{stores:r}=e,{page:i}=e,{constructors:s}=e,{components:a=[]}=e,{form:o}=e,{data_0:l=null}=e,{data_1:c=null}=e;hc(r.page.notify);let d=!1,u=!1,p=null;mc(()=>{const _=r.page.subscribe(()=>{d&&(n(6,u=!0),bc().then(()=>{n(7,p=document.title||"untitled page")}))});return n(5,d=!0),_});function h(_){wr[_?"unshift":"push"](()=>{a[1]=_,n(0,a)})}function m(_){wr[_?"unshift":"push"](()=>{a[0]=_,n(0,a)})}function g(_){wr[_?"unshift":"push"](()=>{a[0]=_,n(0,a)})}return t.$$set=_=>{"stores"in _&&n(8,r=_.stores),"page"in _&&n(9,i=_.page),"constructors"in _&&n(1,s=_.constructors),"components"in _&&n(0,a=_.components),"form"in _&&n(2,o=_.form),"data_0"in _&&n(3,l=_.data_0),"data_1"in _&&n(4,c=_.data_1)},t.$$.update=()=>{t.$$.dirty&768&&r.page.set(i)},[a,s,o,l,c,d,u,p,r,i,h,m,g]}class jy extends oc{constructor(e){super(),lc(this,e,Dy,Ay,uc,{stores:8,page:9,constructors:1,components:0,form:2,data_0:3,data_1:4})}}const Uy=[()=>Z(()=>import("../nodes/0.4TSnz47i.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22]),import.meta.url),()=>Z(()=>import("../nodes/1.0Rhp94rQ.js"),__vite__mapDeps([23,3,2,24,1]),import.meta.url),()=>Z(()=>import("../nodes/2.gDdBT66U.js"),__vite__mapDeps([25,3,2,15,11,16,1,26,7,8,4,13,27,10,17,28,29,30,31,14,9,12,6,32,33,34,35,19,24,36,21,37,20,38]),import.meta.url),()=>Z(()=>import("../nodes/3.GxTtJyUF.js"),__vite__mapDeps([39,3,2,10,11,13,17,1,35,16,19,36,7,8,9,12,14]),import.meta.url),()=>Z(()=>import("../nodes/4.oUgJkMNe.js"),__vite__mapDeps([40,3,2,13,16,1,28,37,17,19,35,20,12,7,8,14]),import.meta.url),()=>Z(()=>import("../nodes/5.mm3MYjBc.js"),__vite__mapDeps([41,3,2,29,42,26,1,7,8,4,24,36]),import.meta.url),()=>Z(()=>import("../nodes/6.Qy-GJfed.js"),__vite__mapDeps([43,3,2,29,31,15,11,1,28,14,24,36,30,7,8,4,20]),import.meta.url),()=>Z(()=>import("../nodes/7.v-tPRbq_.js"),__vite__mapDeps([44,3,2,29,31,15,11,1,28,14,24,36,20,7,8]),import.meta.url),()=>Z(()=>import("../nodes/8.r26ri4VC.js"),__vite__mapDeps([45,3,2,17,1,46,13,29,7,8,16,47,18,27,20]),import.meta.url),()=>Z(()=>import("../nodes/9.tZv71h6K.js"),__vite__mapDeps([48,3,2,5,6,7,8,4,9,10,11,12,13,1,14,27,17,46,29,18,20,47]),import.meta.url),()=>Z(()=>import("../nodes/10.vaa3FxVC.js"),__vite__mapDeps([49,3,2,29,13,17,1,20,7,8]),import.meta.url),()=>Z(()=>import("../nodes/11.sQKu8M_8.js"),__vite__mapDeps([50,3,2,29,32,33,1,28,10,11,12,13,7,8,34,17,24,36,20]),import.meta.url),()=>Z(()=>import("../nodes/12.Gi76IjEU.js"),__vite__mapDeps([51,3,2,29,10,11,1,13,27,33,17,37,16,19,36,20,12,7,8,34]),import.meta.url),()=>Z(()=>import("../nodes/13.We9DptsO.js"),__vite__mapDeps([52,3,2,27,13,12,7,8,1,17,20]),import.meta.url),()=>Z(()=>import("../nodes/14.DVtc3txu.js"),__vite__mapDeps([53,3,2,29,1,13,27,17,35,16,19,20,12,7,8,4]),import.meta.url),()=>Z(()=>import("../nodes/15.ZIjRljtv.js"),__vite__mapDeps([54,3,2,13,27,17,1,35,16,19,55,20,7,8,4]),import.meta.url),()=>Z(()=>import("../nodes/16.ddxQ5HJn.js"),__vite__mapDeps([56,3,2,29,1,42,26,7,8,4,24,55,16,36,12,13]),import.meta.url),()=>Z(()=>import("../nodes/17.cNpC49TB.js"),__vite__mapDeps([57,3,2,29,1,13,46,7,8,12]),import.meta.url),()=>Z(()=>import("../nodes/18.f7sK763g.js"),__vite__mapDeps([58,3,2,1,13,20]),import.meta.url)],By=[0],Hy={"/":[2],"/coupons":[3],"/faq":[4],"/favorites":[5],"/history":[6],"/history/completed":[7],"/login":[8],"/login/otp":[9],"/onboarding":[10],"/payment-methods":[11],"/payment-methods/create":[13],"/payment-methods/[id]":[12],"/report":[14],"/report/email":[15],"/search":[16],"/settings":[17],"/stations/scan":[18]},zy={handleError:Cy||(({error:t})=>{console.error(t)}),reroute:()=>{}};export{Hy as dictionary,zy as hooks,Fy as matchers,Uy as nodes,jy as root,By as server_loads};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["../nodes/0.4TSnz47i.js","../chunks/style.HZSn-yMG.js","../chunks/index.RK-K-o1D.js","../chunks/index.UaHqEmIZ.js","../chunks/user.oAuVK7RJ.js","../chunks/auth.ZfP6Oi2D.js","../chunks/exports.GLhM_dct.js","../chunks/supabase.xRgAeO37.js","../chunks/public.1B-Uau7n.js","../chunks/promo-codes.client.2UzDNhxK.js","../chunks/stripe.esm.worker.tkZLw4d1.js","../chunks/_commonjsHelpers.jVd2wRzr.js","../chunks/api.FojSz0ks.js","../chunks/entry.vPeIVMYj.js","../chunks/pricing.IkKnCD68.js","../chunks/dayjs.min.atm4dYLJ.js","../chunks/index.GsAsnf3x.js","../chunks/Button.dvflqaYf.js","../chunks/hiko-white.OadC_g0o.js","../chunks/useModal.kL0JL2yE.js","../chunks/useTopBar._QombAUQ.js","../chunks/posthog.rIrMRyrw.js","../assets/0.CYooNhiK.css","../nodes/1.0Rhp94rQ.js","../chunks/EmptyPlaceholder.ts_EQhA2.js","../nodes/2.gDdBT66U.js","../chunks/useGeolocation.3AiEsf14.js","../chunks/stores.sj_Ool2X.js","../chunks/Card.R0dmzyBc.js","../chunks/each.N0rfVI1r.js","../chunks/useActiveOrder.3QgQAOCf.js","../chunks/OrderItem.caWmX4u5.js","../chunks/usePaymentMethods.R4Jx6iEJ.js","../chunks/payment-methods.h9FNc7Jc.js","../assets/usePaymentMethods.67mcC058.css","../chunks/ErrorDialog.zsuK_K7c.js","../chunks/Loader.0adt-66j.js","../chunks/ConfirmDialog.Gd540cs9.js","../assets/2.D8SUBcOP.css","../nodes/3.GxTtJyUF.js","../nodes/4.oUgJkMNe.js","../nodes/5.mm3MYjBc.js","../chunks/StationItem.SmkLFR3c.js","../nodes/6.Qy-GJfed.js","../nodes/7.v-tPRbq_.js","../nodes/8.r26ri4VC.js","../chunks/LanguageSelector.yWEnsspB.js","../chunks/parsePhoneNumberWithError.jnHbw4I4.js","../nodes/9.tZv71h6K.js","../nodes/10.vaa3FxVC.js","../nodes/11.sQKu8M_8.js","../nodes/12.Gi76IjEU.js","../nodes/13.We9DptsO.js","../nodes/14.DVtc3txu.js","../nodes/15.ZIjRljtv.js","../chunks/Input.xuJbibd_.js","../nodes/16.ddxQ5HJn.js","../nodes/17.cNpC49TB.js","../nodes/18.f7sK763g.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
