import{s as Q,k as L,e as k,l as U,c as w,a as E,d as p,m as B,b as I,i as v,h as $,n as N,f as T,o as W,t as V,g as q,j as P,r as A}from"../chunks/index.UaHqEmIZ.js";import{S as X,i as Y,e as O,c as j,a as C,m as M,b as R,f as F,t as D,d as z,g as G}from"../chunks/index.RK-K-o1D.js";import{g as Z}from"../chunks/entry.vPeIVMYj.js";import{p as x}from"../chunks/stores.sj_Ool2X.js";import{B as ee}from"../chunks/Button.dvflqaYf.js";import{E as te}from"../chunks/ErrorDialog.zsuK_K7c.js";import{I as re}from"../chunks/Input.xuJbibd_.js";import{u as le}from"../chunks/useTopBar._QombAUQ.js";import{R as H,t as oe}from"../chunks/style.HZSn-yMG.js";import{s as se}from"../chunks/supabase.xRgAeO37.js";import{u as J}from"../chunks/user.oAuVK7RJ.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[e]="7e742371-ed8c-43cb-82b2-222b230d7b44",s._sentryDebugIdIdentifier="sentry-dbid-7e742371-ed8c-43cb-82b2-222b230d7b44")}catch{}})();function ie(s){let e,t=s[7]("report.verification_required")+"",l,r,i,u,c=s[7]("report.email_sent")+"",b,d,_,m=s[7]("report.check_inbox")+"",h;return{c(){e=k("h1"),l=V(t),r=U(),i=k("div"),u=k("p"),b=V(c),d=U(),_=k("p"),h=V(m),this.h()},l(a){e=w(a,"H1",{class:!0});var g=E(e);l=q(g,t),g.forEach(p),r=B(a),i=w(a,"DIV",{});var n=E(i);u=w(n,"P",{class:!0});var o=E(u);b=q(o,c),o.forEach(p),d=B(n),_=w(n,"P",{class:!0});var f=E(_);h=q(f,m),f.forEach(p),n.forEach(p),this.h()},h(){I(e,"class","font-bold text-2xl mb-6"),I(u,"class","font-medium mb-1"),I(_,"class","opacity-80 text-sm")},m(a,g){v(a,e,g),$(e,l),v(a,r,g),v(a,i,g),$(i,u),$(u,b),$(i,d),$(i,_),$(_,h)},p(a,g){g&128&&t!==(t=a[7]("report.verification_required")+"")&&P(l,t),g&128&&c!==(c=a[7]("report.email_sent")+"")&&P(b,c),g&128&&m!==(m=a[7]("report.check_inbox")+"")&&P(h,m)},i:A,o:A,d(a){a&&(p(e),p(r),p(i))}}}function ae(s){let e,t=s[7]("report.before_submitting")+"",l,r,i,u=s[7]("report.enter_email_below")+"",c,b,d,_,m,h;function a(n){s[9](n)}let g={placeholder:s[7]("report.your_email_address"),type:"email",name:"email",autocomplete:"email",rules:[s[2]||s[7]("report.enter_valid_address"),s[0].length>0||s[7]("report.field_required")]};return s[0]!==void 0&&(g.value=s[0]),_=new re({props:g}),L.push(()=>O(_,"value",a)),{c(){e=k("h1"),l=V(t),r=U(),i=k("p"),c=V(u),b=U(),d=k("div"),j(_.$$.fragment),this.h()},l(n){e=w(n,"H1",{class:!0});var o=E(e);l=q(o,t),o.forEach(p),r=B(n),i=w(n,"P",{class:!0});var f=E(i);c=q(f,u),f.forEach(p),b=B(n),d=w(n,"DIV",{});var y=E(d);C(_.$$.fragment,y),y.forEach(p),this.h()},h(){I(e,"class","font-bold text-2xl mb-6"),I(i,"class","text-sm opacity-80 mb-6")},m(n,o){v(n,e,o),$(e,l),v(n,r,o),v(n,i,o),$(i,c),v(n,b,o),v(n,d,o),M(_,d,null),h=!0},p(n,o){(!h||o&128)&&t!==(t=n[7]("report.before_submitting")+"")&&P(l,t),(!h||o&128)&&u!==(u=n[7]("report.enter_email_below")+"")&&P(c,u);const f={};o&128&&(f.placeholder=n[7]("report.your_email_address")),o&133&&(f.rules=[n[2]||n[7]("report.enter_valid_address"),n[0].length>0||n[7]("report.field_required")]),!m&&o&1&&(m=!0,f.value=n[0],N(()=>m=!1)),_.$set(f)},i(n){h||(D(_.$$.fragment,n),h=!0)},o(n){R(_.$$.fragment,n),h=!1},d(n){n&&(p(e),p(r),p(i),p(b),p(d)),z(_)}}}function K(s){let e,t,l;return t=new ee({props:{disabled:!s[6],loading:s[1],class:"w-full",$$slots:{default:[ne]},$$scope:{ctx:s}}}),t.$on("click",s[8]),{c(){e=k("div"),j(t.$$.fragment),this.h()},l(r){e=w(r,"DIV",{class:!0});var i=E(e);C(t.$$.fragment,i),i.forEach(p),this.h()},h(){I(e,"class","sticky bottom-0 bg-white border-t mt-auto px-3 py-6")},m(r,i){v(r,e,i),M(t,e,null),l=!0},p(r,i){const u={};i&64&&(u.disabled=!r[6]),i&2&&(u.loading=r[1]),i&8320&&(u.$$scope={dirty:i,ctx:r}),t.$set(u)},i(r){l||(D(t.$$.fragment,r),l=!0)},o(r){R(t.$$.fragment,r),l=!1},d(r){r&&p(e),z(t)}}}function ne(s){let e=s[7]("report.update")+"",t;return{c(){t=V(e)},l(l){t=q(l,e)},m(l,r){v(l,t,r)},p(l,r){r&128&&e!==(e=l[7]("report.update")+"")&&P(t,e)},d(l){l&&p(t)}}}function ue(s){let e=s[7]("common.something_wrong_happened")+"",t;return{c(){t=V(e)},l(l){t=q(l,e)},m(l,r){v(l,t,r)},p(l,r){r&128&&e!==(e=l[7]("common.something_wrong_happened")+"")&&P(t,e)},d(l){l&&p(t)}}}function fe(s){let e=s[7]("report.email_exists")+"",t;return{c(){t=V(e)},l(l){t=q(l,e)},m(l,r){v(l,t,r)},p(l,r){r&128&&e!==(e=l[7]("report.email_exists")+"")&&P(t,e)},d(l){l&&p(t)}}}function pe(s){let e;function t(i,u){return i[5].email_exists?fe:ue}let l=t(s),r=l(s);return{c(){e=k("p"),r.c(),this.h()},l(i){e=w(i,"P",{slot:!0});var u=E(e);r.l(u),u.forEach(p),this.h()},h(){I(e,"slot","content")},m(i,u){v(i,e,u),r.m(e,null)},p(i,u){l===(l=t(i))&&r?r.p(i,u):(r.d(1),r=l(i),r&&(r.c(),r.m(e,null)))},d(i){i&&p(e),r.d()}}}function _e(s){let e,t,l,r,i,u,c,b,d;const _=[ae,ie],m=[];function h(o,f){return o[4]?1:0}l=h(s),r=m[l]=_[l](s);let a=!s[4]&&K(s);function g(o){s[10](o)}let n={$$slots:{content:[pe]},$$scope:{ctx:s}};return s[3]!==void 0&&(n.visible=s[3]),c=new te({props:n}),L.push(()=>O(c,"visible",g)),{c(){e=k("div"),t=k("div"),r.c(),i=U(),a&&a.c(),u=U(),j(c.$$.fragment),this.h()},l(o){e=w(o,"DIV",{class:!0});var f=E(e);t=w(f,"DIV",{class:!0});var y=E(t);r.l(y),y.forEach(p),i=B(f),a&&a.l(f),f.forEach(p),u=B(o),C(c.$$.fragment,o),this.h()},h(){I(t,"class","flex-1 px-3 py-6"),I(e,"class","flex flex-col flex-1")},m(o,f){v(o,e,f),$(e,t),m[l].m(t,null),$(e,i),a&&a.m(e,null),v(o,u,f),M(c,o,f),d=!0},p(o,[f]){let y=l;l=h(o),l===y?m[l].p(o,f):(G(),R(m[y],1,1,()=>{m[y]=null}),F(),r=m[l],r?r.p(o,f):(r=m[l]=_[l](o),r.c()),D(r,1),r.m(t,null)),o[4]?a&&(G(),R(a,1,1,()=>{a=null}),F()):a?(a.p(o,f),f&16&&D(a,1)):(a=K(o),a.c(),D(a,1),a.m(e,null));const S={};f&8352&&(S.$$scope={dirty:f,ctx:o}),!b&&f&8&&(b=!0,S.visible=o[3],N(()=>b=!1)),c.$set(S)},i(o){d||(D(r),D(a),D(c.$$.fragment,o),d=!0)},o(o){R(r),R(a),R(c.$$.fragment,o),d=!1},d(o){o&&(p(e),p(u)),m[l].d(),a&&a.d(),z(c,o)}}}function ce(s,e,t){let l,r,i,u,c;T(s,J,o=>t(11,i=o)),T(s,x,o=>t(12,u=o)),T(s,oe,o=>t(7,c=o));let b="",d=!1,_=!1,m=!1,h={email_exists:!1};le({backUrl:u.state.returnTo??H.Settings});const a=async()=>{try{t(1,d=!0);const{error:o,data:f}=await se.auth.updateUser({email:b},{emailRedirectTo:window.location.origin+H.Report});if(o){console.error(o),o.status===422&&t(5,h.email_exists=!0,h),t(3,_=!0);return}f.user&&J.set(f.user),t(4,m=!0)}finally{t(1,d=!1)}};W(()=>{i!=null&&i.email&&Z(H.Report)});function g(o){b=o,t(0,b)}function n(o){_=o,t(3,_)}return s.$$.update=()=>{s.$$.dirty&1&&t(2,l=/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(b)),s.$$.dirty&6&&t(6,r=l&&!d)},[b,d,l,_,m,h,r,c,a,g,n]}class Ie extends X{constructor(e){super(),Y(this,e,ce,_e,Q,{})}}export{Ie as component};
