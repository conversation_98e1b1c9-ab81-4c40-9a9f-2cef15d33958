import{s as q,e as w,l as P,c as g,G as re,m as L,i as S,E as ne,d as m,f as C,o as oe,t as A,a as M,g as $,b,h as y,j as F,Q as ae}from"../chunks/index.UaHqEmIZ.js";import{S as z,i as G,c as U,a as W,m as V,t as J,b as X,d as Y}from"../chunks/index.RK-K-o1D.js";import{p as K}from"../chunks/stores.sj_Ool2X.js";import{b as se,a as ie}from"../chunks/api.FojSz0ks.js";import{g as le}from"../chunks/entry.vPeIVMYj.js";import{B as de}from"../chunks/Button.dvflqaYf.js";import{e as ce}from"../chunks/public.1B-Uau7n.js";import{l as fe,t as Q,R as Z}from"../chunks/style.HZSn-yMG.js";import{u as ue}from"../chunks/useTopBar._QombAUQ.js";(function(){try{var o=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(o._sentryDebugIds=o._sentryDebugIds||{},o._sentryDebugIds[e]="dbfb4ef5-954b-4313-a3f1-06806f6b75e4",o._sentryDebugIdIdentifier="sentry-dbid-dbfb4ef5-954b-4313-a3f1-06806f6b75e4")}catch{}})();const{Axios:ke,AxiosError:me,CanceledError:De,isCancel:Oe,CancelToken:He,VERSION:Be,all:Ne,Cancel:qe,isAxiosError:ze,spread:Ge,toFormData:Ue,AxiosHeaders:We,HttpStatusCode:Ve,formToJSON:Je,getAdapter:Xe,mergeConfig:Ye}=se;function I(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?I=function(e){return typeof e}:I=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},I(o)}var ee="https://js.stripe.com/v3",pe=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,O="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",he=function(){for(var e=document.querySelectorAll('script[src^="'.concat(ee,'"]')),t=0;t<e.length;t++){var n=e[t];if(pe.test(n.src))return n}return null},H=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(ee).concat(t);var r=document.head||document.body;if(!r)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},ve=function(e,t){!e||!e._registerWrapper||e._registerWrapper({name:"stripe-js",version:"3.0.7",startTime:t})},E=null,x=null,T=null,_e=function(e){return function(){e(new Error("Failed to load Stripe.js"))}},ye=function(e,t){return function(){window.Stripe?e(window.Stripe):t(new Error("Stripe.js not available"))}},be=function(e){return E!==null?E:(E=new Promise(function(t,n){if(typeof window>"u"||typeof document>"u"){t(null);return}if(window.Stripe&&e&&console.warn(O),window.Stripe){t(window.Stripe);return}try{var r=he();if(r&&e)console.warn(O);else if(!r)r=H(e);else if(r&&T!==null&&x!==null){var a;r.removeEventListener("load",T),r.removeEventListener("error",x),(a=r.parentNode)===null||a===void 0||a.removeChild(r),r=H(e)}T=ye(t,n),x=_e(n),r.addEventListener("load",T),r.addEventListener("error",x)}catch(l){n(l);return}}),E.catch(function(t){return E=null,Promise.reject(t)}))},we=function(e,t,n){if(e===null)return null;var r=e.apply(void 0,t);return ve(r,n),r},B=function(e){var t=`invalid load parameters; expected object of shape

    {advancedFraudSignals: boolean}

but received

    `.concat(JSON.stringify(e),`
`);if(e===null||I(e)!=="object")throw new Error(t);if(Object.keys(e).length===1&&typeof e.advancedFraudSignals=="boolean")return e;throw new Error(t)},j,R=!1,te=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];R=!0;var r=Date.now();return be(j).then(function(a){return we(a,t,r)})};te.setLoadParameters=function(o){if(R&&j){var e=B(o),t=Object.keys(e),n=t.reduce(function(r,a){var l;return r&&o[a]===((l=j)===null||l===void 0?void 0:l[a])},!0);if(n)return}if(R)throw new Error("You cannot change load parameters after calling loadStripe");j=B(o)};function N(o){let e,t;return{c(){e=w("p"),t=A(o[2]),this.h()},l(n){e=g(n,"P",{class:!0});var r=M(e);t=$(r,o[2]),r.forEach(m),this.h()},h(){b(e,"class","text-red-500 mt-3")},m(n,r){S(n,e,r),y(e,t)},p(n,r){r&4&&F(t,n[2])},d(n){n&&m(e)}}}function ge(o){let e,t=o[3]("payment_methods.create")+"",n;return{c(){e=w("span"),n=A(t),this.h()},l(r){e=g(r,"SPAN",{class:!0});var a=M(e);n=$(a,t),a.forEach(m),this.h()},h(){b(e,"class","font-theme uppercase text-3xl")},m(r,a){S(r,e,a),y(e,n)},p(r,a){a&8&&t!==(t=r[3]("payment_methods.create")+"")&&F(n,t)},d(r){r&&m(e)}}}function Se(o){let e,t='<div id="card-form"></div>',n,r,a,l,p,u,i=o[2]&&N(o);return a=new de({props:{loading:o[1],disabled:!o[0]||o[1],class:"w-full mt-auto",$$slots:{default:[ge]},$$scope:{ctx:o}}}),a.$on("click",o[4]),{c(){e=w("form"),e.innerHTML=t,n=P(),i&&i.c(),r=P(),U(a.$$.fragment)},l(s){e=g(s,"FORM",{"data-svelte-h":!0}),re(e)!=="svelte-1f02tlk"&&(e.innerHTML=t),n=L(s),i&&i.l(s),r=L(s),W(a.$$.fragment,s)},m(s,c){S(s,e,c),S(s,n,c),i&&i.m(s,c),S(s,r,c),V(a,s,c),l=!0,p||(u=ne(e,"submit",o[4]),p=!0)},p(s,[c]){s[2]?i?i.p(s,c):(i=N(s),i.c(),i.m(r.parentNode,r)):i&&(i.d(1),i=null);const d={};c&2&&(d.loading=s[1]),c&3&&(d.disabled=!s[0]||s[1]),c&520&&(d.$$scope={dirty:c,ctx:s}),a.$set(d)},i(s){l||(J(a.$$.fragment,s),l=!0)},o(s){X(a.$$.fragment,s),l=!1},d(s){s&&(m(e),m(n),m(r)),i&&i.d(s),Y(a,s),p=!1,u()}}}function Ee(o,e,t){let n,r,a;C(o,K,d=>t(6,n=d)),C(o,fe,d=>t(7,r=d)),C(o,Q,d=>t(3,a=d));let l=null,p=null,u=!1,i="";const s=async()=>{if(t(0,l=await te(ce.PUBLIC_STRIPE_KEY)),!l)return;p=l.elements({locale:r}).create("card",{hidePostalCode:!0,classes:{base:"block w-full rounded-2xl outline-none shadow-app border-2 border-white bg-slate-50 transition-shadow p-3",invalid:"border-red-400",focus:"shadow-md border-primary-500"},style:{base:{fontSize:"16px",lineHeight:"1.375","::placeholder":{fontSize:"16px"}},invalid:{color:"#ff3860"}}}),p.mount("#card-form")},c=async()=>{var f,h;if(!l||!p)return;t(1,u=!0);const{paymentMethod:d,error:v}=await l.createPaymentMethod({type:"card",card:p});if(v){t(2,i=v.message),t(1,u=!1);return}try{await ie.post("/payments/methods",{payment_method_id:d.id}),await le(n.state.returnTo??Z.PaymentMethods)}catch(_){_ instanceof me&&t(2,i=(h=(f=_.response)==null?void 0:f.data)==null?void 0:h.message)}t(1,u=!1)};return oe(()=>{s()}),[l,u,i,a,c]}class Pe extends z{constructor(e){super(),G(this,e,Ee,Se,q,{})}}function Le(o){let e,t,n,r,a=o[0]("payment_methods.new_payment_method")+"",l,p,u,i=o[0]("payment_methods.only_credit_cards")+"",s,c,d,v;return d=new Pe({}),{c(){e=w("meta"),t=P(),n=w("div"),r=w("h1"),l=A(a),p=P(),u=w("p"),s=A(i),c=P(),U(d.$$.fragment),this.h()},l(f){const h=ae("svelte-1ck9cr4",document.head);e=g(h,"META",{name:!0,content:!0}),h.forEach(m),t=L(f),n=g(f,"DIV",{class:!0});var _=M(n);r=g(_,"H1",{class:!0});var k=M(r);l=$(k,a),k.forEach(m),p=L(_),u=g(_,"P",{class:!0});var D=M(u);s=$(D,i),D.forEach(m),c=L(_),W(d.$$.fragment,_),_.forEach(m),this.h()},h(){b(e,"name","viewport"),b(e,"content","width=device-width, initial-scale=1, user-scalable=0, interactive-widget=resizes-content"),b(r,"class","font-bold text-2xl mb-6"),b(u,"class","text-gray-500 mb-2"),b(n,"class","flex flex-col flex-1 px-3 py-6")},m(f,h){y(document.head,e),S(f,t,h),S(f,n,h),y(n,r),y(r,l),y(n,p),y(n,u),y(u,s),y(n,c),V(d,n,null),v=!0},p(f,[h]){(!v||h&1)&&a!==(a=f[0]("payment_methods.new_payment_method")+"")&&F(l,a),(!v||h&1)&&i!==(i=f[0]("payment_methods.only_credit_cards")+"")&&F(s,i)},i(f){v||(J(d.$$.fragment,f),v=!0)},o(f){X(d.$$.fragment,f),v=!1},d(f){f&&(m(t),m(n)),m(e),Y(d)}}}function Ce(o,e,t){let n,r;return C(o,K,a=>t(1,n=a)),C(o,Q,a=>t(0,r=a)),ue({backUrl:n.state.returnTo??Z.PaymentMethods}),[r]}class Ke extends z{constructor(e){super(),G(this,e,Ce,Le,q,{})}}export{Ke as component};
