import{s as Pe,e as h,c as b,b as p,i as Z,E as fe,r as be,d as g,I as Ce,A as ve,D as He,o as Ne,B as we,C as Ae,k as Te,l as H,t as z,a as D,m as A,G as ye,g as K,h as c,j as W,n as Me,f as ne,H as Ve}from"../chunks/index.UaHqEmIZ.js";import{S as Le,i as Oe,e as Be,c as re,a as oe,m as le,t as ie,b as ue,d as ce}from"../chunks/index.RK-K-o1D.js";import{a as ke,R as Ee}from"../chunks/auth.ZfP6Oi2D.js";import{g as U}from"../chunks/entry.vPeIVMYj.js";import{p as Re}from"../chunks/stores.sj_Ool2X.js";import{B as Ue}from"../chunks/Button.dvflqaYf.js";import{L as je}from"../chunks/LanguageSelector.yWEnsspB.js";import{H as ze}from"../chunks/hiko-white.OadC_g0o.js";import{u as Ke}from"../chunks/useTopBar._QombAUQ.js";import{c as Ie,R as j,l as We,t as qe}from"../chunks/style.HZSn-yMG.js";import{s as De}from"../chunks/supabase.xRgAeO37.js";import{p as Ge}from"../chunks/parsePhoneNumberWithError.jnHbw4I4.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="274750bc-2c93-43c6-b483-c3bcf51f8800",t._sentryDebugIdIdentifier="sentry-dbid-274750bc-2c93-43c6-b483-c3bcf51f8800")}catch{}})();function Fe(t){let e,a,o;return{c(){e=h("input"),this.h()},l(s){e=b(s,"INPUT",{type:!0,name:!0,maxlength:!0,inputmode:!0,pattern:!0,autocomplete:!0,placeholder:!0,class:!0}),this.h()},h(){p(e,"type","text"),e.value=t[0],p(e,"name","token"),p(e,"maxlength",t[1]),p(e,"inputmode","numeric"),p(e,"pattern","[0-9]*"),p(e,"autocomplete","one-time-code"),p(e,"placeholder","1 2 3 4 5 6"),p(e,"class","w-full text-center text-3xl text-black tracking-[0.75rem] shadow-md font-theme outline-none bg-slate-50 rounded-2xl py-4 px-3 placeholder:text-center placeholder:tracking-[0.2rem] placeholder:text-primary-500/50 ")},m(s,l){Z(s,e,l),t[5](e),a||(o=[fe(e,"input",t[3]),fe(e,"paste",t[4])],a=!0)},p(s,[l]){l&1&&e.value!==s[0]&&(e.value=s[0]),l&2&&p(e,"maxlength",s[1])},i:be,o:be,d(s){s&&g(e),t[5](null),a=!1,Ce(o)}}}function Je(t,e,a){const o=["value","length"];let s=ve(e,o),{value:l}=e,{length:x=6}=e,i;const L=He(),w=f=>{const _=f.currentTarget;isNaN(+_.value)?_.value=l:a(0,l=_.value)},d=f=>{var O;f.preventDefault();const _=((O=f.clipboardData)==null?void 0:O.getData("text"))??"";isNaN(+_)||a(0,l=_)};Ne(()=>{s.autofocus&&(i==null||i.focus())});function k(f){Te[f?"unshift":"push"](()=>{i=f,a(2,i)})}return t.$$set=f=>{e=we(we({},e),Ae(f)),a(7,s=ve(e,o)),"value"in f&&a(0,l=f.value),"length"in f&&a(1,x=f.length)},t.$$.update=()=>{t.$$.dirty&1&&l.trim().length===6&&L("complete")},[l,x,i,w,d,k]}class Qe extends Le{constructor(e){super(),Oe(this,e,Je,Fe,Pe,{value:0,length:1})}}function xe(t){let e,a=t[4]("login.invalid_code")+"",o;return{c(){e=h("p"),o=z(a),this.h()},l(s){e=b(s,"P",{class:!0});var l=D(e);o=K(l,a),l.forEach(g),this.h()},h(){p(e,"class","text-center text-red-600 mb-3")},m(s,l){Z(s,e,l),c(e,o)},p(s,l){l&16&&a!==(a=s[4]("login.invalid_code")+"")&&W(o,a)},d(s){s&&g(e)}}}function Xe(t){let e=t[4]("login.submit")+"",a;return{c(){a=z(e)},l(o){a=K(o,e)},m(o,s){Z(o,a,s)},p(o,s){s&16&&e!==(e=o[4]("login.submit")+"")&&W(a,e)},d(o){o&&g(a)}}}function Ye(t){let e,a,o,s,l=`<img src="${ze}" alt="HIKO" width="240"/>`,x,i,L="<b>Restez branché</b> sans vous arrêter!",w,d,k,f=t[4]("login.texted_code")+"",_,O,S,n,v,M=t[4]("login.code_not_received")+"",C,$,E,R=t[4]("login.try_again")+"",q,G,F,ee,P,J,B,I,te,ae,N,T,se,pe;a=new je({props:{class:"text-3xl font-theme text-black",buttonClass:"uppercase"}});let m=t[2].invalid_otp&&xe(t);function Se(r){t[8](r)}let de={autofocus:!0,class:"text-3xl"};return t[0]!==void 0&&(de.value=t[0]),I=new Qe({props:de}),Te.push(()=>Be(I,"value",Se)),I.$on("complete",t[6]),N=new Ue({props:{loading:t[1],disabled:t[1]||!t[3],class:"w-full bg-white text-primary-500 font-theme uppercase text-3xl",$$slots:{default:[Xe]},$$scope:{ctx:t}}}),N.$on("click",t[6]),{c(){e=h("div"),re(a.$$.fragment),o=H(),s=h("div"),s.innerHTML=l,x=H(),i=h("p"),i.innerHTML=L,w=H(),d=h("div"),k=h("p"),_=z(f),O=H(),S=h("div"),n=h("p"),v=h("span"),C=z(M),$=H(),E=h("button"),q=z(R),ee=H(),P=h("div"),m&&m.c(),J=H(),B=h("div"),re(I.$$.fragment),ae=H(),re(N.$$.fragment),this.h()},l(r){e=b(r,"DIV",{class:!0});var u=D(e);oe(a.$$.fragment,u),o=A(u),s=b(u,"DIV",{class:!0,"data-svelte-h":!0}),ye(s)!=="svelte-1ykgtmx"&&(s.innerHTML=l),x=A(u),i=b(u,"P",{class:!0,"data-svelte-h":!0}),ye(i)!=="svelte-cz7a46"&&(i.innerHTML=L),w=A(u),d=b(u,"DIV",{class:!0});var y=D(d);k=b(y,"P",{class:!0});var V=D(k);_=K(V,f),V.forEach(g),O=A(y),S=b(y,"DIV",{class:!0});var me=D(S);n=b(me,"P",{class:!0});var Q=D(n);v=b(Q,"SPAN",{});var ge=D(v);C=K(ge,M),ge.forEach(g),$=A(Q),E=b(Q,"BUTTON",{class:!0});var _e=D(E);q=K(_e,R),_e.forEach(g),Q.forEach(g),me.forEach(g),ee=A(y),P=b(y,"DIV",{class:!0});var X=D(P);m&&m.l(X),J=A(X),B=b(X,"DIV",{class:!0});var he=D(B);oe(I.$$.fragment,he),he.forEach(g),X.forEach(g),ae=A(y),oe(N.$$.fragment,y),y.forEach(g),u.forEach(g),this.h()},h(){p(s,"class","flex justify-center mt-12"),p(i,"class","text-center"),p(k,"class","text-xl font-medium text-center mb-1"),E.disabled=G=t[1]||Y,p(E,"class",F=Ie("block text-lime",{"opacity-50":t[1]||Y})),p(n,"class","flex items-center gap-1"),p(S,"class","flex flex-col items-center gap-6 mb-6"),p(B,"class","flex justify-center"),p(P,"class","mb-6"),p(d,"class","mt-auto"),p(e,"class","bg-primary-500 text-white flex flex-col flex-1 px-3 py-6")},m(r,u){Z(r,e,u),le(a,e,null),c(e,o),c(e,s),c(e,x),c(e,i),c(e,w),c(e,d),c(d,k),c(k,_),c(d,O),c(d,S),c(S,n),c(n,v),c(v,C),c(n,$),c(n,E),c(E,q),c(d,ee),c(d,P),m&&m.m(P,null),c(P,J),c(P,B),le(I,B,null),c(d,ae),le(N,d,null),T=!0,se||(pe=fe(E,"click",t[7]),se=!0)},p(r,[u]){(!T||u&16)&&f!==(f=r[4]("login.texted_code")+"")&&W(_,f),(!T||u&16)&&M!==(M=r[4]("login.code_not_received")+"")&&W(C,M),(!T||u&16)&&R!==(R=r[4]("login.try_again")+"")&&W(q,R),(!T||u&2&&G!==(G=r[1]||Y))&&(E.disabled=G),(!T||u&2&&F!==(F=Ie("block text-lime",{"opacity-50":r[1]||Y})))&&p(E,"class",F),r[2].invalid_otp?m?m.p(r,u):(m=xe(r),m.c(),m.m(P,J)):m&&(m.d(1),m=null);const y={};!te&&u&1&&(te=!0,y.value=r[0],Me(()=>te=!1)),I.$set(y);const V={};u&2&&(V.loading=r[1]),u&10&&(V.disabled=r[1]||!r[3]),u&8208&&(V.$$scope={dirty:u,ctx:r}),N.$set(V)},i(r){T||(ie(a.$$.fragment,r),ie(I.$$.fragment,r),ie(N.$$.fragment,r),T=!0)},o(r){ue(a.$$.fragment,r),ue(I.$$.fragment,r),ue(N.$$.fragment,r),T=!1},d(r){r&&g(e),ce(a),m&&m.d(),ce(I),ce(N),se=!1,pe()}}}let Y=!1;function Ze(t,e,a){let o,s,l,x;ne(t,We,n=>a(10,l=n)),ne(t,qe,n=>a(4,x=n));let i="",L=!1,w={invalid_otp:!1};const d=Ve(Re,n=>n.url.searchParams.get("phone_number"));ne(t,d,n=>a(9,s=n)),Ke({backUrl:j.Login,isVisible:!1});const k=()=>{if(!("OTPCredential"in window))return()=>{};const n=new AbortController;return(async()=>{const v=await navigator.credentials.get({otp:{transport:["sms"]},signal:n.signal});v&&a(0,i=v.code)})(),()=>n.abort("SMS listener was removed as component was unmounted")},f=async()=>{const n=ke.get(Ee);return n&&(ke.remove(Ee),await U(n,{replaceState:!0})),!!n},_=async()=>{var n;if(s)try{a(1,L=!0);const v=Ge(s,"CA").number,{data:M,error:C}=await De.auth.verifyOtp({phone:v,token:i,type:"sms"});if(C){console.error(C.message),a(2,w.invalid_otp=C.name==="AuthApiError",w);return}if(await De.auth.updateUser({data:{language:l}}),await f())return;(n=M.user)!=null&&n.user_metadata.has_seen_onboarding?await U(j.Home,{replaceState:!0}):await U(j.OnBoarding,{replaceState:!0})}catch(v){console.error(v)}finally{a(1,L=!1)}};Ne(()=>{s||U(j.Login,{replaceState:!0});const n=k();return()=>{n()}});const O=()=>U(j.Login);function S(n){i=n,a(0,i)}return t.$$.update=()=>{t.$$.dirty&1&&a(3,o=i.split("").every(n=>!!n.trim()&&!isNaN(+n))&&i.trim().length===6),t.$$.dirty&1&&i&&a(2,w.invalid_otp=!1,w)},[i,L,w,o,x,d,_,O,S]}class ft extends Le{constructor(e){super(),Oe(this,e,Ze,Ye,Pe,{})}}export{ft as component};
