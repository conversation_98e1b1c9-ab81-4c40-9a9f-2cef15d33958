import{s as P,B as m,J as U,K as j,L as F,a as v,M as G,d as u,N as b,i as I,r as y,C as B,e as M,t as se,l as O,c as q,g as ne,m as R,G as pe,b as L,h as f,E as be,j as re,q as ye,f as ge,H as Be,P as de}from"../chunks/index.UaHqEmIZ.js";import{S as V,i as $,c as Y,a as ie,m as Z,t as k,f as _e,b as Q,d as ee,g as we}from"../chunks/index.RK-K-o1D.js";import{e as fe}from"../chunks/each.N0rfVI1r.js";import{g as J,R as N,l as Ee,t as Me}from"../chunks/style.HZSn-yMG.js";import{g as qe}from"../chunks/entry.vPeIVMYj.js";import{L as Le}from"../chunks/LanguageSelector.yWEnsspB.js";import"../chunks/api.FojSz0ks.js";import{s as Ae}from"../chunks/supabase.xRgAeO37.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="45b5395f-e2b0-4250-ae2e-29b954c983dc",n._sentryDebugIdIdentifier="sentry-dbid-45b5395f-e2b0-4250-ae2e-29b954c983dc")}catch{}})();function Te(n){let e,a,l='<g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"><path d="M11.986 3H12a2 2 0 0 1 2 2v6a2 2 0 0 1-1.5 1.937V7A2.5 2.5 0 0 0 10 4.5H4.063A2 2 0 0 1 6 3h.014A2.25 2.25 0 0 1 8.25 1h1.5a2.25 2.25 0 0 1 2.236 2M10.5 4v-.75a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75V4z"/><path d="M3 6a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zm1.75 2.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5zM4 11.75a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1-.75-.75"/></g>',i=[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function He(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class Ie extends V{constructor(e){super(),$(this,e,He,Te,P,{})}}function Ce(n){let e,a,l='<g fill="currentColor"><path d="M2.5 3A1.5 1.5 0 0 0 1 4.5V5h14v-.5A1.5 1.5 0 0 0 13.5 3z"/><path fill-rule="evenodd" d="M15 7H1v4.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5zM3 10.25a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75m3.75-.75a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5z" clip-rule="evenodd"/></g>',i=[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function ze(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class ke extends V{constructor(e){super(),$(this,e,ze,Ce,P,{})}}function Pe(n){let e,a,l='<path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14M8 4a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3A.75.75 0 0 1 8 4m0 8a1 1 0 1 0 0-2a1 1 0 0 0 0 2" clip-rule="evenodd"/>',i=[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function Ve(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class $e extends V{constructor(e){super(),$(this,e,Ve,Pe,P,{})}}function De(n){let e,a,l='<path fill="currentColor" d="M6.3 2.84A1.5 1.5 0 0 0 4 4.11v11.78a1.5 1.5 0 0 0 2.3 1.27l9.344-5.891a1.5 1.5 0 0 0 0-2.538z"/>',i=[{viewBox:"0 0 20 20"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 20 20"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function Se(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class Ne extends V{constructor(e){super(),$(this,e,Se,De,P,{})}}function Oe(n){let e,a,l='<path fill="currentColor" fill-rule="evenodd" d="M15 8A7 7 0 1 1 1 8a7 7 0 0 1 14 0m-6 3.5a1 1 0 1 1-2 0a1 1 0 0 1 2 0M7.293 5.293a1 1 0 1 1 .99 1.667c-.459.134-1.033.566-1.033 1.29v.25a.75.75 0 1 0 1.5 0v-.115a2.5 2.5 0 1 0-2.518-4.153a.75.75 0 1 0 1.061 1.06" clip-rule="evenodd"/>',i=[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 16 16"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function Re(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class Qe extends V{constructor(e){super(),$(this,e,Re,Oe,P,{})}}function Ue(n){let e,a,l='<path fill="currentColor" d="M5 21q-.825 0-1.412-.587T3 19V5q0-.825.588-1.412T5 3h6q.425 0 .713.288T12 4q0 .425-.288.713T11 5H5v14h6q.425 0 .713.288T12 20q0 .425-.288.713T11 21zm12.175-8H10q-.425 0-.712-.288T9 12q0-.425.288-.712T10 11h7.175L15.3 9.125q-.275-.275-.275-.675t.275-.7q.275-.3.7-.313t.725.288L20.3 11.3q.3.3.3.7t-.3.7l-3.575 3.575q-.3.3-.712.288t-.713-.313q-.275-.3-.262-.712t.287-.688z"/>',i=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},n[0]],r={};for(let t=0;t<i.length;t+=1)r=m(r,i[t]);return{c(){e=U("svg"),a=new j(!0),this.h()},l(t){e=F(t,"svg",{viewBox:!0,width:!0,height:!0});var s=v(e);a=G(s,!0),s.forEach(u),this.h()},h(){a.a=null,b(e,r)},m(t,s){I(t,e,s),a.m(l,e)},p(t,[s]){b(e,r=J(i,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},s&1&&t[0]]))},i:y,o:y,d(t){t&&u(e)}}}function je(n,e,a){return n.$$set=l=>{a(0,e=m(m({},e),B(l)))},e=B(e),[e]}class Fe extends V{constructor(e){super(),$(this,e,je,Ue,P,{})}}function me(n,e,a){const l=n.slice();return l[4]=e[a],l}function ve(n){let e,a,l,i,r,t=n[4].text+"",s,x,T;var A=n[4].icon;function H(g,w){return{props:{width:"26",height:"26",class:"text-primary-500"}}}return A&&(l=de(A,H())),{c(){e=M("li"),a=M("a"),l&&Y(l.$$.fragment),i=O(),r=M("p"),s=se(t),this.h()},l(g){e=q(g,"LI",{});var w=v(e);a=q(w,"A",{href:!0,class:!0});var _=v(a);l&&ie(l.$$.fragment,_),i=R(_),r=q(_,"P",{class:!0});var p=v(r);s=ne(p,t),p.forEach(u),_.forEach(u),w.forEach(u),this.h()},h(){L(r,"class","text-xl"),L(a,"href",x=n[4].to),L(a,"class","bg-white rounded-2xl shadow-app active:bg-slate-100 flex items-center gap-6 px-6 py-4")},m(g,w){I(g,e,w),f(e,a),l&&Z(l,a,null),f(a,i),f(a,r),f(r,s),T=!0},p(g,w){if(w&2&&A!==(A=g[4].icon)){if(l){we();const _=l;Q(_.$$.fragment,1,0,()=>{ee(_,1)}),_e()}A?(l=de(A,H()),Y(l.$$.fragment),k(l.$$.fragment,1),Z(l,a,i)):l=null}(!T||w&2)&&t!==(t=g[4].text+"")&&re(s,t),(!T||w&2&&x!==(x=g[4].to))&&L(a,"href",x)},i(g){T||(l&&k(l.$$.fragment,g),T=!0)},o(g){l&&Q(l.$$.fragment,g),T=!1},d(g){g&&u(e),l&&ee(l)}}}function Ge(n){let e,a,l=n[0]("settings.settings")+"",i,r,t,s,x,T='<hr class="rounded border-2 border-white/20 w-1/2 mx-auto"/>',A,H,g,w,_,p,C,te,D,K=n[0]("settings.logout")+"",W,S,ae,oe,z=fe(n[1]),h=[];for(let o=0;o<z.length;o+=1)h[o]=ve(me(n,z,o));const xe=o=>Q(h[o],1,1,()=>{h[o]=null});return g=new Le({}),C=new Fe({props:{width:"26",height:"26"}}),{c(){e=M("div"),a=M("h1"),i=se(l),r=O(),t=M("ul");for(let o=0;o<h.length;o+=1)h[o].c();s=O(),x=M("li"),x.innerHTML=T,A=O(),H=M("li"),Y(g.$$.fragment),w=O(),_=M("li"),p=M("button"),Y(C.$$.fragment),te=O(),D=M("span"),W=se(K),this.h()},l(o){e=q(o,"DIV",{class:!0});var d=v(e);a=q(d,"H1",{class:!0});var c=v(a);i=ne(c,l),c.forEach(u),r=R(d),t=q(d,"UL",{class:!0});var E=v(t);for(let le=0;le<h.length;le+=1)h[le].l(E);s=R(E),x=q(E,"LI",{class:!0,"data-svelte-h":!0}),pe(x)!=="svelte-yydvss"&&(x.innerHTML=T),A=R(E),H=q(E,"LI",{});var he=v(H);ie(g.$$.fragment,he),he.forEach(u),w=R(E),_=q(E,"LI",{class:!0});var ue=v(_);p=q(ue,"BUTTON",{class:!0});var X=v(p);ie(C.$$.fragment,X),te=R(X),D=q(X,"SPAN",{class:!0});var ce=v(D);W=ne(ce,K),ce.forEach(u),X.forEach(u),ue.forEach(u),E.forEach(u),d.forEach(u),this.h()},h(){L(a,"class","text-white font-bold text-2xl px-6 mb-6"),L(x,"class","my-6"),L(D,"class","text-xl"),L(p,"class","bg-white rounded-2xl shadow-app active:bg-slate-100 text-red-500 flex items-center gap-6 w-full px-6 py-4"),L(_,"class","mt-auto"),L(t,"class","flex flex-col gap-2 flex-1 px-3"),L(e,"class","bg-primary-500 flex flex-col flex-1 py-6")},m(o,d){I(o,e,d),f(e,a),f(a,i),f(e,r),f(e,t);for(let c=0;c<h.length;c+=1)h[c]&&h[c].m(t,null);f(t,s),f(t,x),f(t,A),f(t,H),Z(g,H,null),f(t,w),f(t,_),f(_,p),Z(C,p,null),f(p,te),f(p,D),f(D,W),S=!0,ae||(oe=be(p,"click",n[3]),ae=!0)},p(o,[d]){if((!S||d&1)&&l!==(l=o[0]("settings.settings")+"")&&re(i,l),d&2){z=fe(o[1]);let c;for(c=0;c<z.length;c+=1){const E=me(o,z,c);h[c]?(h[c].p(E,d),k(h[c],1)):(h[c]=ve(E),h[c].c(),k(h[c],1),h[c].m(t,s))}for(we(),c=z.length;c<h.length;c+=1)xe(c);_e()}(!S||d&1)&&K!==(K=o[0]("settings.logout")+"")&&re(W,K)},i(o){if(!S){for(let d=0;d<z.length;d+=1)k(h[d]);k(g.$$.fragment,o),k(C.$$.fragment,o),S=!0}},o(o){h=h.filter(Boolean);for(let d=0;d<h.length;d+=1)Q(h[d]);Q(g.$$.fragment,o),Q(C.$$.fragment,o),S=!1},d(o){o&&u(e),ye(h,o),ee(g),ee(C),ae=!1,oe()}}}function Je(n,e,a){let l,i;ge(n,Me,s=>a(0,l=s));const r=Be(Ee,()=>[{text:l("settings.payment_methods"),icon:ke,to:N.PaymentMethods},{text:l("settings.history"),icon:Ie,to:N.History},{text:l("settings.faq"),icon:Qe,to:N.FAQ},{text:l("settings.report"),icon:$e,to:N.Report},{text:l("settings.play_onboarding"),icon:Ne,to:N.OnBoarding}]);return ge(n,r,s=>a(1,i=s)),[l,i,r,async()=>{await Ae.auth.signOut(),await qe(N.Login)}]}class lt extends V{constructor(e){super(),$(this,e,Je,Ge,P,{})}}export{lt as component};
