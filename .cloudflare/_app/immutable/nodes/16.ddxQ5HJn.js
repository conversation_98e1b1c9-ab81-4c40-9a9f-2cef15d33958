import{s as ce,B as K,J as de,K as he,L as me,a as g,M as pe,d as f,N as Y,i as P,r as z,C as Z,k as ge,e as v,l as N,c as b,m as C,b as $,h as p,n as ve,f as F,o as be,w as ye,q as fe,p as ee,t as T,g as j,j as q}from"../chunks/index.UaHqEmIZ.js";import{S as ue,i as _e,e as we,c as x,a as R,m as G,b as L,f as O,t as E,d as U,g as Q}from"../chunks/index.RK-K-o1D.js";import{e as J}from"../chunks/each.N0rfVI1r.js";import{g as ke,t as $e,l as Ee,R as te}from"../chunks/style.HZSn-yMG.js";import{S as Ie}from"../chunks/StationItem.SmkLFR3c.js";import{E as De}from"../chunks/EmptyPlaceholder.ts_EQhA2.js";import{I as Se}from"../chunks/Input.xuJbibd_.js";import{L as Le}from"../chunks/Loader.0adt-66j.js";import{u as Pe,l as Ve,f as le}from"../chunks/useGeolocation.3AiEsf14.js";import{a as Be}from"../chunks/api.FojSz0ks.js";import{s as re}from"../chunks/supabase.xRgAeO37.js";(function(){try{var i=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(i._sentryDebugIds=i._sentryDebugIds||{},i._sentryDebugIds[t]="df12698d-fa3e-452a-a08c-9ce5df3f85d0",i._sentryDebugIdIdentifier="sentry-dbid-df12698d-fa3e-452a-a08c-9ce5df3f85d0")}catch{}})();function He(i){let t,l,r='<path fill="currentColor" d="M12 11.5A2.5 2.5 0 0 1 9.5 9A2.5 2.5 0 0 1 12 6.5A2.5 2.5 0 0 1 14.5 9a2.5 2.5 0 0 1-2.5 2.5M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7"/>',e=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},i[0]],s={};for(let a=0;a<e.length;a+=1)s=K(s,e[a]);return{c(){t=de("svg"),l=new he(!0),this.h()},l(a){t=me(a,"svg",{viewBox:!0,width:!0,height:!0});var o=g(t);l=pe(o,!0),o.forEach(f),this.h()},h(){l.a=null,Y(t,s)},m(a,o){P(a,t,o),l.m(r,t)},p(a,[o]){Y(t,s=ke(e,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},o&1&&a[0]]))},i:z,o:z,d(a){a&&f(t)}}}function Me(i,t,l){return i.$$set=r=>{l(0,t=K(K({},t),Z(r)))},t=Z(t),[t]}class Ae extends ue{constructor(t){super(),_e(this,t,Me,He,ce,{})}}function ae(i,t,l){const r=i.slice();return r[18]=t[l].station,r[19]=t[l].isFavorite,r}function se(i,t,l){const r=i.slice();return r[15]=t[l],r}function Fe(i){let t,l,r;return l=new De({props:{$$slots:{default:[xe]},$$scope:{ctx:i}}}),{c(){t=v("div"),x(l.$$.fragment),this.h()},l(e){t=b(e,"DIV",{class:!0});var s=g(t);R(l.$$.fragment,s),s.forEach(f),this.h()},h(){$(t,"class","text-center mt-16")},m(e,s){P(e,t,s),G(l,t,null),r=!0},p(e,s){const a={};s&4194328&&(a.$$scope={dirty:s,ctx:e}),l.$set(a)},i(e){r||(E(l.$$.fragment,e),r=!0)},o(e){L(l.$$.fragment,e),r=!1},d(e){e&&f(t),U(l)}}}function Ne(i){let t,l,r=J(i[2]),e=[];for(let a=0;a<r.length;a+=1)e[a]=ne(ae(i,r,a));const s=a=>L(e[a],1,1,()=>{e[a]=null});return{c(){t=v("ul");for(let a=0;a<e.length;a+=1)e[a].c()},l(a){t=b(a,"UL",{});var o=g(t);for(let n=0;n<e.length;n+=1)e[n].l(o);o.forEach(f)},m(a,o){P(a,t,o);for(let n=0;n<e.length;n+=1)e[n]&&e[n].m(t,null);l=!0},p(a,o){if(o&4){r=J(a[2]);let n;for(n=0;n<r.length;n+=1){const m=ae(a,r,n);e[n]?(e[n].p(m,o),E(e[n],1)):(e[n]=ne(m),e[n].c(),E(e[n],1),e[n].m(t,null))}for(Q(),n=r.length;n<e.length;n+=1)s(n);O()}},i(a){if(!l){for(let o=0;o<r.length;o+=1)E(e[o]);l=!0}},o(a){e=e.filter(Boolean);for(let o=0;o<e.length;o+=1)L(e[o]);l=!1},d(a){a&&f(t),fe(e,a)}}}function Ce(i){let t,l,r;return l=new Le({}),{c(){t=v("div"),x(l.$$.fragment),this.h()},l(e){t=b(e,"DIV",{class:!0});var s=g(t);R(l.$$.fragment,s),s.forEach(f),this.h()},h(){$(t,"class","flex justify-center mt-8")},m(e,s){P(e,t,s),G(l,t,null),r=!0},p:z,i(e){r||(E(l.$$.fragment,e),r=!0)},o(e){L(l.$$.fragment,e),r=!1},d(e){e&&f(t),U(l)}}}function Te(i){let t,l,r=J(i[0]),e=[];for(let a=0;a<r.length;a+=1)e[a]=ie(se(i,r,a));const s=a=>L(e[a],1,1,()=>{e[a]=null});return{c(){t=v("ul");for(let a=0;a<e.length;a+=1)e[a].c()},l(a){t=b(a,"UL",{});var o=g(t);for(let n=0;n<e.length;n+=1)e[n].l(o);o.forEach(f)},m(a,o){P(a,t,o);for(let n=0;n<e.length;n+=1)e[n]&&e[n].m(t,null);l=!0},p(a,o){if(o&33){r=J(a[0]);let n;for(n=0;n<r.length;n+=1){const m=se(a,r,n);e[n]?(e[n].p(m,o),E(e[n],1)):(e[n]=ie(m),e[n].c(),E(e[n],1),e[n].m(t,null))}for(Q(),n=r.length;n<e.length;n+=1)s(n);O()}},i(a){if(!l){for(let o=0;o<r.length;o+=1)E(e[o]);l=!0}},o(a){e=e.filter(Boolean);for(let o=0;o<e.length;o+=1)L(e[o]);l=!1},d(a){a&&f(t),fe(e,a)}}}function je(i){let t,l=i[4]("search.start_typing")+"",r;return{c(){t=v("p"),r=T(l)},l(e){t=b(e,"P",{});var s=g(t);r=j(s,l),s.forEach(f)},m(e,s){P(e,t,s),p(t,r)},p(e,s){s&16&&l!==(l=e[4]("search.start_typing")+"")&&q(r,l)},d(e){e&&f(t)}}}function qe(i){let t,l=i[4]("search.no_results")+"",r;return{c(){t=v("p"),r=T(l)},l(e){t=b(e,"P",{});var s=g(t);r=j(s,l),s.forEach(f)},m(e,s){P(e,t,s),p(t,r)},p(e,s){s&16&&l!==(l=e[4]("search.no_results")+"")&&q(r,l)},d(e){e&&f(t)}}}function xe(i){let t;function l(s,a){return s[3]?qe:je}let r=l(i),e=r(i);return{c(){e.c(),t=ee()},l(s){e.l(s),t=ee()},m(s,a){e.m(s,a),P(s,t,a)},p(s,a){r===(r=l(s))&&e?e.p(s,a):(e.d(1),e=r(s),e&&(e.c(),e.m(t.parentNode,t)))},d(s){s&&f(t),e.d(s)}}}function ne(i){let t,l;return t=new Ie({props:{station:i[18],isFavorite:i[19]}}),{c(){x(t.$$.fragment)},l(r){R(t.$$.fragment,r)},m(r,e){G(t,r,e),l=!0},p(r,e){const s={};e&4&&(s.station=r[18]),e&4&&(s.isFavorite=r[19]),t.$set(s)},i(r){l||(E(t.$$.fragment,r),l=!0)},o(r){L(t.$$.fragment,r),l=!1},d(r){U(t,r)}}}function oe(i){let t,l=le(i[15].distance_meters,{language:i[5],unitDisplay:"short"})+"",r;return{c(){t=v("p"),r=T(l),this.h()},l(e){t=b(e,"P",{class:!0});var s=g(t);r=j(s,l),s.forEach(f),this.h()},h(){$(t,"class","text-xs text-center opacity-80 truncate w-14 mt-1")},m(e,s){P(e,t,s),p(t,r)},p(e,s){s&33&&l!==(l=le(e[15].distance_meters,{language:e[5],unitDisplay:"short"})+"")&&q(r,l)},d(e){e&&f(t)}}}function ie(i){let t,l,r,e,s,a,o,n,m,I=i[15].text+"",w,y,D,c=i[15].fulltext+"",_,k,V,u;s=new Ae({props:{width:20,height:20,class:"text-white"}});let d=i[15].distance_meters&&oe(i);return{c(){t=v("li"),l=v("a"),r=v("div"),e=v("div"),x(s.$$.fragment),a=N(),d&&d.c(),o=N(),n=v("div"),m=v("p"),w=T(I),y=N(),D=v("p"),_=T(c),V=N(),this.h()},l(h){t=b(h,"LI",{class:!0});var S=g(t);l=b(S,"A",{href:!0,class:!0});var H=g(l);r=b(H,"DIV",{class:!0});var M=g(r);e=b(M,"DIV",{class:!0});var B=g(e);R(s.$$.fragment,B),B.forEach(f),a=C(M),d&&d.l(M),M.forEach(f),o=C(H),n=b(H,"DIV",{class:!0});var A=g(n);m=b(A,"P",{class:!0});var W=g(m);w=j(W,I),W.forEach(f),y=C(A),D=b(A,"P",{class:!0});var X=g(D);_=j(X,c),X.forEach(f),A.forEach(f),H.forEach(f),V=C(S),S.forEach(f),this.h()},h(){$(e,"class","bg-primary-500 w-7 aspect-square rounded-full flex items-center justify-center"),$(r,"class","flex flex-col items-center overflow-hidden w-14"),$(m,"class","font-medium"),$(D,"class","text-sm font-light line-clamp-2"),$(n,"class","flex-1"),$(l,"href",k=`${te.Home}?place_id=${i[15].place_id}`),$(l,"class","flex items-center gap-3 active:bg-slate-50 md:hover:bg-slate-50 px-3 py-2"),$(t,"class","border-t last-of-type:border-b")},m(h,S){P(h,t,S),p(t,l),p(l,r),p(r,e),G(s,e,null),p(r,a),d&&d.m(r,null),p(l,o),p(l,n),p(n,m),p(m,w),p(n,y),p(n,D),p(D,_),p(t,V),u=!0},p(h,S){h[15].distance_meters?d?d.p(h,S):(d=oe(h),d.c(),d.m(r,null)):d&&(d.d(1),d=null),(!u||S&1)&&I!==(I=h[15].text+"")&&q(w,I),(!u||S&1)&&c!==(c=h[15].fulltext+"")&&q(_,c),(!u||S&1&&k!==(k=`${te.Home}?place_id=${h[15].place_id}`))&&$(l,"href",k)},i(h){u||(E(s.$$.fragment,h),u=!0)},o(h){L(s.$$.fragment,h),u=!1},d(h){h&&f(t),U(s),d&&d.d()}}}function Re(i){let t,l,r,e,s,a,o,n;function m(c){i[9](c)}let I={type:"search",placeholder:i[4]("search.where_are_you_going"),autofocus:!0};i[3]!==void 0&&(I.value=i[3]),r=new Se({props:I}),ge.push(()=>we(r,"value",m));const w=[Te,Ce,Ne,Fe],y=[];function D(c,_){return c[0].length>0?0:c[1]?1:c[2].length>0?2:3}return a=D(i),o=y[a]=w[a](i),{c(){t=v("div"),l=v("div"),x(r.$$.fragment),s=N(),o.c(),this.h()},l(c){t=b(c,"DIV",{});var _=g(t);l=b(_,"DIV",{class:!0});var k=g(l);R(r.$$.fragment,k),k.forEach(f),s=C(_),o.l(_),_.forEach(f),this.h()},h(){$(l,"class","p-3")},m(c,_){P(c,t,_),p(t,l),G(r,l,null),p(t,s),y[a].m(t,null),n=!0},p(c,[_]){const k={};_&16&&(k.placeholder=c[4]("search.where_are_you_going")),!e&&_&8&&(e=!0,k.value=c[3],ve(()=>e=!1)),r.$set(k);let V=a;a=D(c),a===V?y[a].p(c,_):(Q(),L(y[V],1,1,()=>{y[V]=null}),O(),o=y[a],o?o.p(c,_):(o=y[a]=w[a](c),o.c()),E(o,1),o.m(t,null))},i(c){n||(E(r.$$.fragment,c),E(o),n=!0)},o(c){L(r.$$.fragment,c),L(o),n=!1},d(c){c&&f(t),U(r),y[a].d()}}}const Ge=3;function Ue(i,t,l){let r,e,s,a,o;F(i,$e,u=>l(4,a=u)),F(i,Ee,u=>l(5,o=u));const n=ye("");F(i,n,u=>l(3,s=u));let m=[],I,w=!1,y=[];const{latitude:D,longitude:c}=Pe();F(i,D,u=>l(12,e=u)),F(i,c,u=>l(11,r=u));const _=async()=>{try{l(1,w=!0);const{data:u,error:d}=await re.rpc("find_stations_within_radius",{lat:e,lng:r,radius:2e4}).limit(10);if(d){console.error(d);return}const h=u.map(B=>B.station_id),{data:S,error:H}=await re.from("station_locations").select().in("station_id",h);if(H){console.error(H);return}const M=await Ve(h);l(2,y=u.map(B=>({station:S.find(A=>A.station_id===B.station_id),isFavorite:M[B.station_id]})).filter(B=>!!B.station))}finally{l(1,w=!1)}},k=async()=>{if(!(!s||s.length<Ge))try{l(1,w=!0);const{data:u}=await Be.get("/maps/autocomplete",{params:{input:s,latitude:e,longitude:r}});l(0,m=u)}finally{l(1,w=!1)}};be(()=>{_();const u=n.subscribe(()=>{I&&clearTimeout(I),I=setTimeout(()=>{k()},500)});return()=>{u()}});function V(u){s=u,n.set(s)}return[m,w,y,s,a,o,n,D,c,V]}class lt extends ue{constructor(t){super(),_e(this,t,Ue,Re,ce,{})}}export{lt as component};
