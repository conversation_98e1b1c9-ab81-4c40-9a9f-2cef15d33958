import{s as Z,e as k,t as q,l as P,c as w,a as E,g as A,d as p,m as S,b as $,i as I,h as v,j,f as C,o as x,p as T,r as ee,q as te}from"../chunks/index.UaHqEmIZ.js";import{S as re,i as oe,b as O,f as B,t as b,g as H,c as N,a as R,m as U,d as z}from"../chunks/index.RK-K-o1D.js";import{e as F}from"../chunks/each.N0rfVI1r.js";import{O as W,a as le}from"../chunks/OrderItem.caWmX4u5.js";import{E as se}from"../chunks/EmptyPlaceholder.ts_EQhA2.js";import{L as ae}from"../chunks/Loader.0adt-66j.js";import{u as ie}from"../chunks/useActiveOrder.3QgQAOCf.js";import{u as ne}from"../chunks/useTopBar._QombAUQ.js";import{R as X,t as ce}from"../chunks/style.HZSn-yMG.js";import{s as M}from"../chunks/supabase.xRgAeO37.js";(function(){try{var c=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(c._sentryDebugIds=c._sentryDebugIds||{},c._sentryDebugIds[e]="7e43bb83-42b2-4bc5-8ca1-0a8d79cda473",c._sentryDebugIdIdentifier="sentry-dbid-7e43bb83-42b2-4bc5-8ca1-0a8d79cda473")}catch{}})();function G(c,e,t){const l=c.slice();return l[11]=e[t],l}function fe(c){let e,t,l,r=c[4]&&J(c),o=c[0].length>0&&K(c);return{c(){r&&r.c(),e=P(),o&&o.c(),t=T()},l(s){r&&r.l(s),e=S(s),o&&o.l(s),t=T()},m(s,i){r&&r.m(s,i),I(s,e,i),o&&o.m(s,i),I(s,t,i),l=!0},p(s,i){s[4]?r?(r.p(s,i),i&16&&b(r,1)):(r=J(s),r.c(),b(r,1),r.m(e.parentNode,e)):r&&(H(),O(r,1,1,()=>{r=null}),B()),s[0].length>0?o?(o.p(s,i),i&1&&b(o,1)):(o=K(s),o.c(),b(o,1),o.m(t.parentNode,t)):o&&(H(),O(o,1,1,()=>{o=null}),B())},i(s){l||(b(r),b(o),l=!0)},o(s){O(r),O(o),l=!1},d(s){s&&(p(e),p(t)),r&&r.d(s),o&&o.d(s)}}}function ue(c){let e,t;return e=new se({props:{$$slots:{default:[_e]},$$scope:{ctx:c}}}),{c(){N(e.$$.fragment)},l(l){R(e.$$.fragment,l)},m(l,r){U(e,l,r),t=!0},p(l,r){const o={};r&16388&&(o.$$scope={dirty:r,ctx:l}),e.$set(o)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){O(e.$$.fragment,l),t=!1},d(l){z(e,l)}}}function de(c){let e,t,l;return t=new ae({}),{c(){e=k("div"),N(t.$$.fragment),this.h()},l(r){e=w(r,"DIV",{class:!0});var o=E(e);R(t.$$.fragment,o),o.forEach(p),this.h()},h(){$(e,"class","flex justify-center mt-16")},m(r,o){I(r,e,o),U(t,e,null),l=!0},p:ee,i(r){l||(b(t.$$.fragment,r),l=!0)},o(r){O(t.$$.fragment,r),l=!1},d(r){r&&p(e),z(t)}}}function J(c){let e,t,l=c[2]("history.active_order")+"",r,o,s,i;return s=new W({props:{order:c[4],location:c[5]}}),{c(){e=k("div"),t=k("p"),r=q(l),o=P(),N(s.$$.fragment),this.h()},l(f){e=w(f,"DIV",{class:!0});var _=E(e);t=w(_,"P",{class:!0});var h=E(t);r=A(h,l),h.forEach(p),o=S(_),R(s.$$.fragment,_),_.forEach(p),this.h()},h(){$(t,"class","font-medium opacity-50 mb-3"),$(e,"class","mb-10")},m(f,_){I(f,e,_),v(e,t),v(t,r),v(e,o),U(s,e,null),i=!0},p(f,_){(!i||_&4)&&l!==(l=f[2]("history.active_order")+"")&&j(r,l);const h={};_&16&&(h.order=f[4]),_&32&&(h.location=f[5]),s.$set(h)},i(f){i||(b(s.$$.fragment,f),i=!0)},o(f){O(s.$$.fragment,f),i=!1},d(f){f&&p(e),z(s)}}}function K(c){let e,t,l=c[2]("history.completed_orders")+"",r,o,s,i=c[2]("history.view_all")+"",f,_,h,g,y=F(c[0]),a=[];for(let n=0;n<y.length;n+=1)a[n]=Q(G(c,y,n));const m=n=>O(a[n],1,1,()=>{a[n]=null});return{c(){e=k("div"),t=k("p"),r=q(l),o=P(),s=k("a"),f=q(i),_=P(),h=k("ul");for(let n=0;n<a.length;n+=1)a[n].c();this.h()},l(n){e=w(n,"DIV",{class:!0});var u=E(e);t=w(u,"P",{class:!0});var d=E(t);r=A(d,l),d.forEach(p),o=S(u),s=w(u,"A",{href:!0,class:!0});var L=E(s);f=A(L,i),L.forEach(p),u.forEach(p),_=S(n),h=w(n,"UL",{class:!0});var D=E(h);for(let V=0;V<a.length;V+=1)a[V].l(D);D.forEach(p),this.h()},h(){$(t,"class","opacity-50"),$(s,"href",X.HistoryCompleted),$(s,"class","text-primary-500"),$(e,"class","font-medium flex items-center justify-between mb-3"),$(h,"class","grid grid-cols-1 gap-3")},m(n,u){I(n,e,u),v(e,t),v(t,r),v(e,o),v(e,s),v(s,f),I(n,_,u),I(n,h,u);for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(h,null);g=!0},p(n,u){if((!g||u&4)&&l!==(l=n[2]("history.completed_orders")+"")&&j(r,l),(!g||u&4)&&i!==(i=n[2]("history.view_all")+"")&&j(f,i),u&1){y=F(n[0]);let d;for(d=0;d<y.length;d+=1){const L=G(n,y,d);a[d]?(a[d].p(L,u),b(a[d],1)):(a[d]=Q(L),a[d].c(),b(a[d],1),a[d].m(h,null))}for(H(),d=y.length;d<a.length;d+=1)m(d);B()}},i(n){if(!g){for(let u=0;u<y.length;u+=1)b(a[u]);g=!0}},o(n){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)O(a[u]);g=!1},d(n){n&&(p(e),p(_),p(h)),te(a,n)}}}function Q(c){let e,t,l,r;return t=new W({props:{order:c[11].order,location:c[11].location}}),{c(){e=k("li"),N(t.$$.fragment),l=P(),this.h()},l(o){e=w(o,"LI",{class:!0});var s=E(e);R(t.$$.fragment,s),l=S(s),s.forEach(p),this.h()},h(){$(e,"class","col-span-1")},m(o,s){I(o,e,s),U(t,e,null),v(e,l),r=!0},p(o,s){const i={};s&1&&(i.order=o[11].order),s&1&&(i.location=o[11].location),t.$set(i)},i(o){r||(b(t.$$.fragment,o),r=!0)},o(o){O(t.$$.fragment,o),r=!1},d(o){o&&p(e),z(t)}}}function _e(c){let e,t=c[2]("history.no_data")+"",l;return{c(){e=k("p"),l=q(t)},l(r){e=w(r,"P",{});var o=E(e);l=A(o,t),o.forEach(p)},m(r,o){I(r,e,o),v(e,l)},p(r,o){o&4&&t!==(t=r[2]("history.no_data")+"")&&j(l,t)},d(r){r&&p(e)}}}function me(c){let e,t,l,r=c[2]("history.history")+"",o,s,i,f,_;const h=[de,ue,fe],g=[];function y(a,m){return a[1]||a[3]?0:!a[4]&&a[0].length===0?1:2}return i=y(c),f=g[i]=h[i](c),{c(){e=k("div"),t=k("div"),l=k("h1"),o=q(r),s=P(),f.c(),this.h()},l(a){e=w(a,"DIV",{class:!0});var m=E(e);t=w(m,"DIV",{class:!0});var n=E(t);l=w(n,"H1",{class:!0});var u=E(l);o=A(u,r),u.forEach(p),s=S(n),f.l(n),n.forEach(p),m.forEach(p),this.h()},h(){$(l,"class","font-bold text-2xl mb-6"),$(t,"class","px-3 py-6"),$(e,"class","flex flex-col flex-1")},m(a,m){I(a,e,m),v(e,t),v(t,l),v(l,o),v(t,s),g[i].m(t,null),_=!0},p(a,[m]){(!_||m&4)&&r!==(r=a[2]("history.history")+"")&&j(o,r);let n=i;i=y(a),i===n?g[i].p(a,m):(H(),O(g[n],1,1,()=>{g[n]=null}),B(),f=g[i],f?f.p(a,m):(f=g[i]=h[i](a),f.c()),b(f,1),f.m(t,null))},i(a){_||(b(f),_=!0)},o(a){O(f),_=!1},d(a){a&&p(e),g[i].d()}}}function pe(c,e,t){let l,r,o,s;C(c,ce,m=>t(2,l=m));let i=[],f=!0;ne({backUrl:X.Settings});const{order:_,location:h,isLoading:g}=ie();C(c,_,m=>t(4,o=m)),C(c,h,m=>t(5,s=m)),C(c,g,m=>t(3,r=m));const y=async()=>{const{data:{session:m}}=await M.auth.getSession();if(!m)return;const{error:n,data:u}=await M.from("orders").select().eq("user_id",m.user.id).eq("status",le.Completed).order("started_at",{ascending:!1}).limit(3);if(n){console.error(n);return}const{error:d,data:L}=await M.from("station_locations").select().in("station_id",u.map(D=>D.station_id));if(d){console.error(d);return}t(0,i=u.map(D=>{const V=L.find(Y=>Y.station_id===D.station_id)||null;return{order:D,location:V}}))},a=async()=>{try{t(1,f=!0),await y()}catch(m){console.error(m)}finally{t(1,f=!1)}};return x(()=>{a()}),[i,f,l,r,o,s,_,h,g]}class Ie extends re{constructor(e){super(),oe(this,e,pe,me,Z,{})}}export{Ie as component};
