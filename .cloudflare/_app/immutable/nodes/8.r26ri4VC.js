import{s as rt,B as $,J as Ct,K as _t,L as Pt,a as A,M as xt,d as N,N as Z,i as j,r as tt,C as G,P as ut,e as T,l as E,c as F,m as k,b as O,V as At,h as y,E as et,I as he,A as Tt,D as me,F as ge,q as de,t as K,g as J,o as pe,u as Ft,w as ve,k as be,G as ye,j as nt,n as Ne,f as It}from"../chunks/index.UaHqEmIZ.js";import{S as at,i as it,c as L,a as Q,m as z,g as st,b as D,d as B,f as ft,t as I,h as Et,e as Ce}from"../chunks/index.RK-K-o1D.js";import{B as _e}from"../chunks/Button.dvflqaYf.js";import{L as Pe}from"../chunks/LanguageSelector.yWEnsspB.js";import{e as kt}from"../chunks/each.N0rfVI1r.js";import{g as gt,c as X,t as xe,R as we}from"../chunks/style.HZSn-yMG.js";import{s as Ot}from"../chunks/index.GsAsnf3x.js";import{i as Se,a as Ae,P as Te,n as Fe,c as Ie,f as Ee,b as ct,F as ke,d as Oe,V,e as De,g as Dt,s as $e,h as Re,j as ht,k as Me,M as $t,l as je,m as Le,o as ze,q as Be,w as He,r as Ue,p as We}from"../chunks/parsePhoneNumberWithError.jnHbw4I4.js";import{H as Ve}from"../chunks/hiko-white.OadC_g0o.js";import{g as ue}from"../chunks/entry.vPeIVMYj.js";import{p as Rt}from"../chunks/stores.sj_Ool2X.js";import{u as Ge}from"../chunks/useTopBar._QombAUQ.js";import{s as Ye}from"../chunks/supabase.xRgAeO37.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="4eb55aaa-5181-48ac-bbad-dc0dc7286679",n._sentryDebugIdIdentifier="sentry-dbid-4eb55aaa-5181-48ac-bbad-dc0dc7286679")}catch{}})();function Mt(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,r)}return t}function jt(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Mt(Object(t),!0).forEach(function(r){qe(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Mt(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function qe(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Xe(n,e,t){e&&e.defaultCountry&&!Se(e.defaultCountry,t)&&(e=jt(jt({},e),{},{defaultCountry:void 0}));try{return Ae(n,e,t)}catch(r){if(!(r instanceof Te))throw r}}function Lt(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,r)}return t}function zt(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Lt(Object(t),!0).forEach(function(r){Ke(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Lt(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function Ke(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Je(){var n=Fe(arguments),e=n.text,t=n.options,r=n.metadata;t=zt(zt({},t),{},{extract:!1});var i=Xe(e,t,r);return i&&i.isValid()||!1}function Qe(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Bt(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function Ze(n,e,t){return e&&Bt(n.prototype,e),t&&Bt(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var tn=function(){function n(e){var t=e.onCountryChange,r=e.onCallingCodeChange;Qe(this,n),this.onCountryChange=t,this.onCallingCodeChange=r}return Ze(n,[{key:"reset",value:function(t){var r=t.country,i=t.callingCode;this.international=!1,this.missingPlus=!1,this.IDDPrefix=void 0,this.callingCode=void 0,this.digits="",this.resetNationalSignificantNumber(),this.initCountryAndCallingCode(r,i)}},{key:"resetNationalSignificantNumber",value:function(){this.nationalSignificantNumber=this.getNationalDigits(),this.nationalSignificantNumberMatchesInput=!0,this.nationalPrefix=void 0,this.carrierCode=void 0,this.complexPrefixBeforeNationalSignificantNumber=void 0}},{key:"update",value:function(t){for(var r=0,i=Object.keys(t);r<i.length;r++){var a=i[r];this[a]=t[a]}}},{key:"initCountryAndCallingCode",value:function(t,r){this.setCountry(t),this.setCallingCode(r)}},{key:"setCountry",value:function(t){this.country=t,this.onCountryChange(t)}},{key:"setCallingCode",value:function(t){this.callingCode=t,this.onCallingCodeChange(t,this.country)}},{key:"startInternationalNumber",value:function(t,r){this.international=!0,this.initCountryAndCallingCode(t,r)}},{key:"appendDigits",value:function(t){this.digits+=t}},{key:"appendNationalSignificantNumberDigits",value:function(t){this.nationalSignificantNumber+=t}},{key:"getNationalDigits",value:function(){return this.international?this.digits.slice((this.IDDPrefix?this.IDDPrefix.length:0)+(this.callingCode?this.callingCode.length:0)):this.digits}},{key:"getDigitsWithoutInternationalPrefix",value:function(){return this.international&&this.IDDPrefix?this.digits.slice(this.IDDPrefix.length):this.digits}}]),n}();function en(n,e){var t=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=nn(n))||e&&n&&typeof n.length=="number"){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nn(n,e){if(n){if(typeof n=="string")return Ht(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ht(n,e)}}function Ht(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}var R="x",vt=new RegExp(R);function ot(n,e){if(e<1)return"";for(var t="";e>1;)e&1&&(t+=n),e>>=1,n+=n;return t+n}function Ut(n,e){return n[e]===")"&&e++,rn(n.slice(0,e))}function rn(n){for(var e=[],t=0;t<n.length;)n[t]==="("?e.push(t):n[t]===")"&&e.pop(),t++;var r=0,i="";e.push(n.length);for(var a=0,l=e;a<l.length;a++){var o=l[a];i+=n.slice(r,o),r=o+1}return i}function an(n,e,t){for(var r=en(t.split("")),i;!(i=r()).done;){var a=i.value;if(n.slice(e+1).search(vt)<0)return;e=n.search(vt),n=n.replace(vt,a)}return[n,e]}function ln(n,e,t){var r=t.metadata,i=t.shouldTryNationalPrefixFormattingRule,a=t.getSeparatorAfterNationalPrefix,l=new RegExp("^(?:".concat(e.pattern(),")$"));if(l.test(n.nationalSignificantNumber))return un(n,e,{metadata:r,shouldTryNationalPrefixFormattingRule:i,getSeparatorAfterNationalPrefix:a})}function on(n,e){return Ie(n,e)==="IS_POSSIBLE"}function un(n,e,t){var r=t.metadata,i=t.shouldTryNationalPrefixFormattingRule,a=t.getSeparatorAfterNationalPrefix;if(n.nationalSignificantNumber,n.international,n.nationalPrefix,n.carrierCode,i(e)){var l=Wt(n,e,{useNationalPrefixFormattingRule:!0,getSeparatorAfterNationalPrefix:a,metadata:r});if(l)return l}return Wt(n,e,{useNationalPrefixFormattingRule:!1,getSeparatorAfterNationalPrefix:a,metadata:r})}function Wt(n,e,t){var r=t.metadata,i=t.useNationalPrefixFormattingRule,a=t.getSeparatorAfterNationalPrefix,l=Ee(n.nationalSignificantNumber,e,{carrierCode:n.carrierCode,useInternationalFormat:n.international,withNationalPrefix:i,metadata:r});if(i||(n.nationalPrefix?l=n.nationalPrefix+a(e)+l:n.complexPrefixBeforeNationalSignificantNumber&&(l=n.complexPrefixBeforeNationalSignificantNumber+" "+l)),sn(l,n))return l}function sn(n,e){return ct(n)===e.getNationalDigits()}function fn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Vt(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function cn(n,e,t){return e&&Vt(n.prototype,e),t&&Vt(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var hn=function(){function n(){fn(this,n)}return cn(n,[{key:"parse",value:function(t){if(this.context=[{or:!0,instructions:[]}],this.parsePattern(t),this.context.length!==1)throw new Error("Non-finalized contexts left when pattern parse ended");var r=this.context[0],i=r.branches,a=r.instructions;if(i)return{op:"|",args:i.concat([bt(a)])};if(a.length===0)throw new Error("Pattern is required");return a.length===1?a[0]:a}},{key:"startContext",value:function(t){this.context.push(t)}},{key:"endContext",value:function(){this.context.pop()}},{key:"getContext",value:function(){return this.context[this.context.length-1]}},{key:"parsePattern",value:function(t){if(!t)throw new Error("Pattern is required");var r=t.match(dn);if(!r){if(gn.test(t))throw new Error("Illegal characters found in a pattern: ".concat(t));this.getContext().instructions=this.getContext().instructions.concat(t.split(""));return}var i=r[1],a=t.slice(0,r.index),l=t.slice(r.index+i.length);switch(i){case"(?:":a&&this.parsePattern(a),this.startContext({or:!0,instructions:[],branches:[]});break;case")":if(!this.getContext().or)throw new Error('")" operator must be preceded by "(?:" operator');if(a&&this.parsePattern(a),this.getContext().instructions.length===0)throw new Error('No instructions found after "|" operator in an "or" group');var o=this.getContext(),u=o.branches;u.push(bt(this.getContext().instructions)),this.endContext(),this.getContext().instructions.push({op:"|",args:u});break;case"|":if(!this.getContext().or)throw new Error('"|" operator can only be used inside "or" groups');if(a&&this.parsePattern(a),!this.getContext().branches)if(this.context.length===1)this.getContext().branches=[];else throw new Error('"branches" not found in an "or" group context');this.getContext().branches.push(bt(this.getContext().instructions)),this.getContext().instructions=[];break;case"[":a&&this.parsePattern(a),this.startContext({oneOfSet:!0});break;case"]":if(!this.getContext().oneOfSet)throw new Error('"]" operator must be preceded by "[" operator');this.endContext(),this.getContext().instructions.push({op:"[]",args:mn(a)});break;default:throw new Error("Unknown operator: ".concat(i))}l&&this.parsePattern(l)}}]),n}();function mn(n){for(var e=[],t=0;t<n.length;){if(n[t]==="-"){if(t===0||t===n.length-1)throw new Error("Couldn't parse a one-of set pattern: ".concat(n));for(var r=n[t-1].charCodeAt(0)+1,i=n[t+1].charCodeAt(0)-1,a=r;a<=i;)e.push(String.fromCharCode(a)),a++}else e.push(n[t]);t++}return e}var gn=/[\(\)\[\]\?\:\|]/,dn=new RegExp("(\\||\\(\\?\\:|\\)|\\[|\\])");function bt(n){return n.length===1?n[0]:n}function Gt(n,e){var t=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=pn(n))||e&&n&&typeof n.length=="number"){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pn(n,e){if(n){if(typeof n=="string")return Yt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Yt(n,e)}}function Yt(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function vn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function qt(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function bn(n,e,t){return e&&qt(n.prototype,e),t&&qt(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var yn=function(){function n(e){vn(this,n),this.matchTree=new hn().parse(e)}return bn(n,[{key:"match",value:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=r.allowOverflow;if(!t)throw new Error("String is required");var a=yt(t.split(""),this.matchTree,!0);if(a&&a.match&&delete a.matchedChars,!(a&&a.overflow&&!i))return a}}]),n}();function yt(n,e,t){if(typeof e=="string"){var r=n.join("");return e.indexOf(r)===0?n.length===e.length?{match:!0,matchedChars:n}:{partialMatch:!0}:r.indexOf(e)===0?t&&n.length>e.length?{overflow:!0}:{match:!0,matchedChars:n.slice(0,e.length)}:void 0}if(Array.isArray(e)){for(var i=n.slice(),a=0;a<e.length;){var l=e[a],o=yt(i,l,t&&a===e.length-1);if(o){if(o.overflow)return o;if(o.match){if(i=i.slice(o.matchedChars.length),i.length===0)return a===e.length-1?{match:!0,matchedChars:n}:{partialMatch:!0}}else{if(o.partialMatch)return{partialMatch:!0};throw new Error(`Unsupported match result:
`.concat(JSON.stringify(o,null,2)))}}else return;a++}return t?{overflow:!0}:{match:!0,matchedChars:n.slice(0,n.length-i.length)}}switch(e.op){case"|":for(var u,s=Gt(e.args),h;!(h=s()).done;){var p=h.value,f=yt(n,p,t);if(f){if(f.overflow)return f;if(f.match)return{match:!0,matchedChars:f.matchedChars};if(f.partialMatch)u=!0;else throw new Error(`Unsupported match result:
`.concat(JSON.stringify(f,null,2)))}}return u?{partialMatch:!0}:void 0;case"[]":for(var C=Gt(e.args),x;!(x=C()).done;){var m=x.value;if(n[0]===m)return n.length===1?{match:!0,matchedChars:n}:t?{overflow:!0}:{match:!0,matchedChars:[m]}}return;default:throw new Error("Unsupported instruction tree: ".concat(e))}}function Xt(n,e){var t=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=Nn(n))||e&&n&&typeof n.length=="number"){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Nn(n,e){if(n){if(typeof n=="string")return Kt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Kt(n,e)}}function Kt(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Cn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Jt(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function _n(n,e,t){return e&&Jt(n.prototype,e),t&&Jt(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var Nt="9",Pn=15,xn=ot(Nt,Pn),wn=/[- ]/,Sn=function(){return/\[([^\[\]])*\]/g},An=function(){return/\d(?=[^,}][^,}])/g},Tn=new RegExp("["+V+"]*\\$1["+V+"]*(\\$\\d["+V+"]*)*$"),Qt=3,Fn=function(){function n(e){e.state;var t=e.metadata;Cn(this,n),this.metadata=t,this.resetFormat()}return _n(n,[{key:"resetFormat",value:function(){this.chosenFormat=void 0,this.template=void 0,this.nationalNumberTemplate=void 0,this.populatedNationalNumberTemplate=void 0,this.populatedNationalNumberTemplatePosition=-1}},{key:"reset",value:function(t,r){this.resetFormat(),t?(this.isNANP=t.callingCode()==="1",this.matchingFormats=t.formats(),r.nationalSignificantNumber&&this.narrowDownMatchingFormats(r)):(this.isNANP=void 0,this.matchingFormats=[])}},{key:"format",value:function(t,r){var i=this;if(on(r.nationalSignificantNumber,this.metadata))for(var a=Xt(this.matchingFormats),l;!(l=a()).done;){var o=l.value,u=ln(r,o,{metadata:this.metadata,shouldTryNationalPrefixFormattingRule:function(h){return i.shouldTryNationalPrefixFormattingRule(h,{international:r.international,nationalPrefix:r.nationalPrefix})},getSeparatorAfterNationalPrefix:function(h){return i.getSeparatorAfterNationalPrefix(h)}});if(u)return this.resetFormat(),this.chosenFormat=o,this.setNationalNumberTemplate(u.replace(/\d/g,R),r),this.populatedNationalNumberTemplate=u,this.populatedNationalNumberTemplatePosition=this.template.lastIndexOf(R),u}return this.formatNationalNumberWithNextDigits(t,r)}},{key:"formatNationalNumberWithNextDigits",value:function(t,r){var i=this.chosenFormat,a=this.chooseFormat(r);if(a)return a===i?this.formatNextNationalNumberDigits(t):this.formatNextNationalNumberDigits(r.getNationalDigits())}},{key:"narrowDownMatchingFormats",value:function(t){var r=this,i=t.nationalSignificantNumber,a=t.nationalPrefix,l=t.international,o=i,u=o.length-Qt;u<0&&(u=0),this.matchingFormats=this.matchingFormats.filter(function(s){return r.formatSuits(s,l,a)&&r.formatMatches(s,o,u)}),this.chosenFormat&&this.matchingFormats.indexOf(this.chosenFormat)===-1&&this.resetFormat()}},{key:"formatSuits",value:function(t,r,i){return!(i&&!t.usesNationalPrefix()&&!t.nationalPrefixIsOptionalWhenFormattingInNationalFormat()||!r&&!i&&t.nationalPrefixIsMandatoryWhenFormattingInNationalFormat())}},{key:"formatMatches",value:function(t,r,i){var a=t.leadingDigitsPatterns().length;if(a===0)return!0;i=Math.min(i,a-1);var l=t.leadingDigitsPatterns()[i];if(r.length<Qt)try{return new yn(l).match(r,{allowOverflow:!0})!==void 0}catch(o){return console.error(o),!0}return new RegExp("^(".concat(l,")")).test(r)}},{key:"getFormatFormat",value:function(t,r){return r?t.internationalFormat():t.format()}},{key:"chooseFormat",value:function(t){for(var r=this,i=function(){var s=l.value;return r.chosenFormat===s?"break":Tn.test(r.getFormatFormat(s,t.international))?r.createTemplateForFormat(s,t)?(r.chosenFormat=s,"break"):(r.matchingFormats=r.matchingFormats.filter(function(h){return h!==s}),"continue"):"continue"},a=Xt(this.matchingFormats.slice()),l;!(l=a()).done;){var o=i();if(o==="break")break}return this.chosenFormat||this.resetFormat(),this.chosenFormat}},{key:"createTemplateForFormat",value:function(t,r){if(!(t.pattern().indexOf("|")>=0)){var i=this.getTemplateForFormat(t,r);if(i)return this.setNationalNumberTemplate(i,r),!0}}},{key:"getSeparatorAfterNationalPrefix",value:function(t){return this.isNANP||t&&t.nationalPrefixFormattingRule()&&wn.test(t.nationalPrefixFormattingRule())?" ":""}},{key:"getInternationalPrefixBeforeCountryCallingCode",value:function(t,r){var i=t.IDDPrefix,a=t.missingPlus;return i?r&&r.spacing===!1?i:i+" ":a?"":"+"}},{key:"getTemplate",value:function(t){if(this.template){for(var r=-1,i=0,a=t.international?this.getInternationalPrefixBeforeCountryCallingCode(t,{spacing:!1}):"";i<a.length+t.getDigitsWithoutInternationalPrefix().length;)r=this.template.indexOf(R,r+1),i++;return Ut(this.template,r+1)}}},{key:"setNationalNumberTemplate",value:function(t,r){this.nationalNumberTemplate=t,this.populatedNationalNumberTemplate=t,this.populatedNationalNumberTemplatePosition=-1,r.international?this.template=this.getInternationalPrefixBeforeCountryCallingCode(r).replace(/[\d\+]/g,R)+ot(R,r.callingCode.length)+" "+t:this.template=t}},{key:"getTemplateForFormat",value:function(t,r){var i=r.nationalSignificantNumber,a=r.international,l=r.nationalPrefix,o=r.complexPrefixBeforeNationalSignificantNumber,u=t.pattern();u=u.replace(Sn(),"\\d").replace(An(),"\\d");var s=xn.match(u)[0];if(!(i.length>s.length)){var h=new RegExp("^"+u+"$"),p=i.replace(/\d/g,Nt);h.test(p)&&(s=p);var f=this.getFormatFormat(t,a),C;if(this.shouldTryNationalPrefixFormattingRule(t,{international:a,nationalPrefix:l})){var x=f.replace(ke,t.nationalPrefixFormattingRule());if(ct(t.nationalPrefixFormattingRule())===(l||"")+ct("$1")&&(f=x,C=!0,l))for(var m=l.length;m>0;)f=f.replace(/\d/,R),m--}var v=s.replace(new RegExp(u),f).replace(new RegExp(Nt,"g"),R);return C||(o?v=ot(R,o.length)+" "+v:l&&(v=ot(R,l.length)+this.getSeparatorAfterNationalPrefix(t)+v)),a&&(v=Oe(v)),v}}},{key:"formatNextNationalNumberDigits",value:function(t){var r=an(this.populatedNationalNumberTemplate,this.populatedNationalNumberTemplatePosition,t);if(!r){this.resetFormat();return}return this.populatedNationalNumberTemplate=r[0],this.populatedNationalNumberTemplatePosition=r[1],Ut(this.populatedNationalNumberTemplate,this.populatedNationalNumberTemplatePosition+1)}},{key:"shouldTryNationalPrefixFormattingRule",value:function(t,r){var i=r.international,a=r.nationalPrefix;if(t.nationalPrefixFormattingRule()){var l=t.usesNationalPrefix();if(l&&a||!l&&!i)return!0}}}]),n}();function se(n,e){return On(n)||kn(n,e)||En(n,e)||In()}function In(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function En(n,e){if(n){if(typeof n=="string")return Zt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Zt(n,e)}}function Zt(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function kn(n,e){var t=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t!=null){var r=[],i=!0,a=!1,l,o;try{for(t=t.call(n);!(i=(l=t.next()).done)&&(r.push(l.value),!(e&&r.length===e));i=!0);}catch(u){a=!0,o=u}finally{try{!i&&t.return!=null&&t.return()}finally{if(a)throw o}}return r}}function On(n){if(Array.isArray(n))return n}function Dn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function te(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function $n(n,e,t){return e&&te(n.prototype,e),t&&te(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var Rn="["+V+ht+"]+",Mn=new RegExp("^"+Rn+"$","i"),jn="(?:["+Me+"]["+V+ht+"]*|["+V+ht+"]+)",Ln=new RegExp("[^"+V+ht+"]+.*$"),zn=/[^\d\[\]]/,Bn=function(){function n(e){var t=e.defaultCountry,r=e.defaultCallingCode,i=e.metadata,a=e.onNationalSignificantNumberChange;Dn(this,n),this.defaultCountry=t,this.defaultCallingCode=r,this.metadata=i,this.onNationalSignificantNumberChange=a}return $n(n,[{key:"input",value:function(t,r){var i=Wn(t),a=se(i,2),l=a[0],o=a[1],u=ct(l),s;return o&&(r.digits||(r.startInternationalNumber(),u||(s=!0))),u&&this.inputDigits(u,r),{digits:u,justLeadingPlus:s}}},{key:"inputDigits",value:function(t,r){var i=r.digits,a=i.length<3&&i.length+t.length>=3;if(r.appendDigits(t),a&&this.extractIddPrefix(r),this.isWaitingForCountryCallingCode(r)){if(!this.extractCountryCallingCode(r))return}else r.appendNationalSignificantNumberDigits(t);r.international||this.hasExtractedNationalSignificantNumber||this.extractNationalSignificantNumber(r.getNationalDigits(),function(l){return r.update(l)})}},{key:"isWaitingForCountryCallingCode",value:function(t){var r=t.international,i=t.callingCode;return r&&!i}},{key:"extractCountryCallingCode",value:function(t){var r=De("+"+t.getDigitsWithoutInternationalPrefix(),this.defaultCountry,this.defaultCallingCode,this.metadata.metadata),i=r.countryCallingCode,a=r.number;if(i)return t.setCallingCode(i),t.update({nationalSignificantNumber:a}),!0}},{key:"reset",value:function(t){if(t){this.hasSelectedNumberingPlan=!0;var r=t._nationalPrefixForParsing();this.couldPossiblyExtractAnotherNationalSignificantNumber=r&&zn.test(r)}else this.hasSelectedNumberingPlan=void 0,this.couldPossiblyExtractAnotherNationalSignificantNumber=void 0}},{key:"extractNationalSignificantNumber",value:function(t,r){if(this.hasSelectedNumberingPlan){var i=Dt(t,this.metadata),a=i.nationalPrefix,l=i.nationalNumber,o=i.carrierCode;if(l!==t)return this.onExtractedNationalNumber(a,o,l,t,r),!0}}},{key:"extractAnotherNationalSignificantNumber",value:function(t,r,i){if(!this.hasExtractedNationalSignificantNumber)return this.extractNationalSignificantNumber(t,i);if(this.couldPossiblyExtractAnotherNationalSignificantNumber){var a=Dt(t,this.metadata),l=a.nationalPrefix,o=a.nationalNumber,u=a.carrierCode;if(o!==r)return this.onExtractedNationalNumber(l,u,o,t,i),!0}}},{key:"onExtractedNationalNumber",value:function(t,r,i,a,l){var o,u,s=a.lastIndexOf(i);if(s>=0&&s===a.length-i.length){u=!0;var h=a.slice(0,s);h!==t&&(o=h)}l({nationalPrefix:t,carrierCode:r,nationalSignificantNumber:i,nationalSignificantNumberMatchesInput:u,complexPrefixBeforeNationalSignificantNumber:o}),this.hasExtractedNationalSignificantNumber=!0,this.onNationalSignificantNumberChange()}},{key:"reExtractNationalSignificantNumber",value:function(t){if(this.extractAnotherNationalSignificantNumber(t.getNationalDigits(),t.nationalSignificantNumber,function(r){return t.update(r)}))return!0;if(this.extractIddPrefix(t))return this.extractCallingCodeAndNationalSignificantNumber(t),!0;if(this.fixMissingPlus(t))return this.extractCallingCodeAndNationalSignificantNumber(t),!0}},{key:"extractIddPrefix",value:function(t){var r=t.international,i=t.IDDPrefix,a=t.digits;if(t.nationalSignificantNumber,!(r||i)){var l=$e(a,this.defaultCountry,this.defaultCallingCode,this.metadata.metadata);if(l!==void 0&&l!==a)return t.update({IDDPrefix:a.slice(0,a.length-l.length)}),this.startInternationalNumber(t,{country:void 0,callingCode:void 0}),!0}}},{key:"fixMissingPlus",value:function(t){if(!t.international){var r=Re(t.digits,this.defaultCountry,this.defaultCallingCode,this.metadata.metadata),i=r.countryCallingCode;if(r.number,i)return t.update({missingPlus:!0}),this.startInternationalNumber(t,{country:t.country,callingCode:i}),!0}}},{key:"startInternationalNumber",value:function(t,r){var i=r.country,a=r.callingCode;t.startInternationalNumber(i,a),t.nationalSignificantNumber&&(t.resetNationalSignificantNumber(),this.onNationalSignificantNumberChange(),this.hasExtractedNationalSignificantNumber=void 0)}},{key:"extractCallingCodeAndNationalSignificantNumber",value:function(t){this.extractCountryCallingCode(t)&&this.extractNationalSignificantNumber(t.getNationalDigits(),function(r){return t.update(r)})}}]),n}();function Hn(n){var e=n.search(jn);if(!(e<0)){n=n.slice(e);var t;return n[0]==="+"&&(t=!0,n=n.slice(1)),n=n.replace(Ln,""),t&&(n="+"+n),n}}function Un(n){var e=Hn(n)||"";return e[0]==="+"?[e.slice(1),!0]:[e]}function Wn(n){var e=Un(n),t=se(e,2),r=t[0],i=t[1];return Mn.test(r)||(r=""),[r,i]}function Vn(n,e){return Xn(n)||qn(n,e)||Yn(n,e)||Gn()}function Gn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yn(n,e){if(n){if(typeof n=="string")return ee(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ee(n,e)}}function ee(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function qn(n,e){var t=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t!=null){var r=[],i=!0,a=!1,l,o;try{for(t=t.call(n);!(i=(l=t.next()).done)&&(r.push(l.value),!(e&&r.length===e));i=!0);}catch(u){a=!0,o=u}finally{try{!i&&t.return!=null&&t.return()}finally{if(a)throw o}}return r}}function Xn(n){if(Array.isArray(n))return n}function Kn(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function ne(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}function Jn(n,e,t){return e&&ne(n.prototype,e),t&&ne(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}var fe=function(){function n(e,t){Kn(this,n),this.metadata=new $t(t);var r=this.getCountryAndCallingCode(e),i=Vn(r,2),a=i[0],l=i[1];this.defaultCountry=a,this.defaultCallingCode=l,this.reset()}return Jn(n,[{key:"getCountryAndCallingCode",value:function(t){var r,i;return t&&(je(t)?(r=t.defaultCountry,i=t.defaultCallingCode):r=t),r&&!this.metadata.hasCountry(r)&&(r=void 0),[r,i]}},{key:"input",value:function(t){var r=this.parser.input(t,this.state),i=r.digits,a=r.justLeadingPlus;if(a)this.formattedOutput="+";else if(i){this.determineTheCountryIfNeeded(),this.state.nationalSignificantNumber&&this.formatter.narrowDownMatchingFormats(this.state);var l;if(this.metadata.hasSelectedNumberingPlan()&&(l=this.formatter.format(i,this.state)),l===void 0&&this.parser.reExtractNationalSignificantNumber(this.state)){this.determineTheCountryIfNeeded();var o=this.state.getNationalDigits();o&&(l=this.formatter.format(o,this.state))}this.formattedOutput=l?this.getFullNumber(l):this.getNonFormattedNumber()}return this.formattedOutput}},{key:"reset",value:function(){var t=this;return this.state=new tn({onCountryChange:function(i){t.country=i},onCallingCodeChange:function(i,a){t.metadata.selectNumberingPlan(a,i),t.formatter.reset(t.metadata.numberingPlan,t.state),t.parser.reset(t.metadata.numberingPlan)}}),this.formatter=new Fn({state:this.state,metadata:this.metadata}),this.parser=new Bn({defaultCountry:this.defaultCountry,defaultCallingCode:this.defaultCallingCode,metadata:this.metadata,state:this.state,onNationalSignificantNumberChange:function(){t.determineTheCountryIfNeeded(),t.formatter.reset(t.metadata.numberingPlan,t.state)}}),this.state.reset({country:this.defaultCountry,callingCode:this.defaultCallingCode}),this.formattedOutput="",this}},{key:"isInternational",value:function(){return this.state.international}},{key:"getCallingCode",value:function(){if(this.isInternational())return this.state.callingCode}},{key:"getCountryCallingCode",value:function(){return this.getCallingCode()}},{key:"getCountry",value:function(){var t=this.state.digits;if(t)return this._getCountry()}},{key:"_getCountry",value:function(){var t=this.state.country;return t}},{key:"determineTheCountryIfNeeded",value:function(){(!this.state.country||this.isCountryCallingCodeAmbiguous())&&this.determineTheCountry()}},{key:"getFullNumber",value:function(t){var r=this;if(this.isInternational()){var i=function(o){return r.formatter.getInternationalPrefixBeforeCountryCallingCode(r.state,{spacing:!!o})+o},a=this.state.callingCode;return i(a?t?"".concat(a," ").concat(t):a:"".concat(this.state.getDigitsWithoutInternationalPrefix()))}return t}},{key:"getNonFormattedNationalNumberWithPrefix",value:function(){var t=this.state,r=t.nationalSignificantNumber,i=t.complexPrefixBeforeNationalSignificantNumber,a=t.nationalPrefix,l=r,o=i||a;return o&&(l=o+l),l}},{key:"getNonFormattedNumber",value:function(){var t=this.state.nationalSignificantNumberMatchesInput;return this.getFullNumber(t?this.getNonFormattedNationalNumberWithPrefix():this.state.getNationalDigits())}},{key:"getNonFormattedTemplate",value:function(){var t=this.getNonFormattedNumber();if(t)return t.replace(/[\+\d]/g,R)}},{key:"isCountryCallingCodeAmbiguous",value:function(){var t=this.state.callingCode,r=this.metadata.getCountryCodesForCallingCode(t);return r&&r.length>1}},{key:"determineTheCountry",value:function(){this.state.setCountry(Le(this.isInternational()?this.state.callingCode:this.defaultCallingCode,{nationalNumber:this.state.nationalSignificantNumber,defaultCountry:this.defaultCountry,metadata:this.metadata}))}},{key:"getNumberValue",value:function(){var t=this.state,r=t.digits,i=t.callingCode,a=t.country,l=t.nationalSignificantNumber;if(r){if(this.isInternational())return i?"+"+i+l:"+"+r;if(a||i){var o=a?this.metadata.countryCallingCode():i;return"+"+o+l}}}},{key:"getNumber",value:function(){var t=this.state,r=t.nationalSignificantNumber,i=t.carrierCode,a=t.callingCode,l=this._getCountry();if(r&&!(!l&&!a)){if(l&&l===this.defaultCountry){var o=new $t(this.metadata.metadata);o.selectNumberingPlan(l);var u=o.numberingPlan.callingCode(),s=this.metadata.getCountryCodesForCallingCode(u);if(s.length>1){var h=ze(r,{countries:s,defaultCountry:this.defaultCountry,metadata:this.metadata.metadata});h&&(l=h)}}var p=new Be(l||a,r,this.metadata.metadata);return i&&(p.carrierCode=i),p}}},{key:"isPossible",value:function(){var t=this.getNumber();return t?t.isPossible():!1}},{key:"isValid",value:function(){var t=this.getNumber();return t?t.isValid():!1}},{key:"getNationalNumber",value:function(){return this.state.nationalSignificantNumber}},{key:"getChars",value:function(){return(this.state.international?"+":"")+this.state.digits}},{key:"getTemplate",value:function(){return this.formatter.getTemplate(this.state)||this.getNonFormattedTemplate()||""}}]),n}();function re(){return He(Je,arguments)}function mt(n){return fe.call(this,n,Ue)}mt.prototype=Object.create(fe.prototype,{});mt.prototype.constructor=mt;function Qn(n){let e,t,r='<g fill="none" fill-rule="evenodd" clip-rule="evenodd"><path fill="#F7FCFF" d="M8 0h16v24H8z"/><path fill="#E31D1C" d="M15.976 7L16 19h-.6l.368-2.97c-2.69.484-3.924.604-3.701.36c.222-.244.397-.59.525-1.038L9 12.955c.378-.004.72-.098 1.028-.281c.307-.183.153-.816-.461-1.9l1.813.264l.687-.746l1.368 1.432h.615l-.615-3.28l1.103.615zm0 0l1.486 2.06l1.103-.617l-.615 3.281h.615l1.368-1.432l.686.746l1.814-.264c-.614 1.084-.768 1.717-.46 1.9c.307.183.65.277 1.027.281l-3.593 2.397c.129.448.304.794.526 1.038c.223.244-1.011.124-3.701-.36l.367 2.97H16zM24 0h8v24h-8zM0 0h8v24H0z"/></g>',i=[{viewBox:"0 0 32 24"},{width:"1.2em"},{height:"1.2em"},n[0]],a={};for(let l=0;l<i.length;l+=1)a=$(a,i[l]);return{c(){e=Ct("svg"),t=new _t(!0),this.h()},l(l){e=Pt(l,"svg",{viewBox:!0,width:!0,height:!0});var o=A(e);t=xt(o,!0),o.forEach(N),this.h()},h(){t.a=null,Z(e,a)},m(l,o){j(l,e,o),t.m(r,e)},p(l,[o]){Z(e,a=gt(i,[{viewBox:"0 0 32 24"},{width:"1.2em"},{height:"1.2em"},o&1&&l[0]]))},i:tt,o:tt,d(l){l&&N(e)}}}function Zn(n,e,t){return n.$$set=r=>{t(0,e=$($({},e),G(r)))},e=G(e),[e]}class tr extends at{constructor(e){super(),it(this,e,Zn,Qn,rt,{})}}function er(n){let e,t,r='<g fill="none"><path fill="#F7FCFF" fill-rule="evenodd" d="M0 0h32v24H0z" clip-rule="evenodd"/><path fill="#E31D1C" fill-rule="evenodd" d="M0 14.667v2h32v-2zm0 3.666v2h32v-2zm0-11v2h32v-2zM0 22v2h32v-2zm0-11v2h32v-2zM0 0v2h32V0zm0 3.667v2h32v-2z" clip-rule="evenodd"/><path fill="#2E42A5" d="M0 0h20v13H0z"/><path fill="#F7FCFF" fill-rule="evenodd" d="m1.722 2.939l-.726.509l.245-.906l-.645-.574h.843l.282-.74l.331.74h.718l-.564.574l.218.906zm4 0l-.726.509l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274.509l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.842l.644.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zM.996 7.449l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74H.596l.645.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.842l.644.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zM.996 11.449l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74H.596l.645.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.842l.644.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274-7.49l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.843l.645.574zm.726 3.49l-.726.51l.245-.906l-.645-.574h.843l.282-.74l.331.74h.718l-.564.574l.218.906zm-.726 4.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.843l.645.574zM3.722 4.938l-.726.51l.245-.906l-.645-.574h.843l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.843l.645.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm-8.726 4.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.843l.645.574zm4.726-.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm3.274.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.842l.644.574zm4.726-4.51l-.726.51l.245-.906l-.644-.574h.842l.282-.74l.331.74h.718l-.564.574l.218.906zm-.726 4.51l.726-.51l.702.51l-.218-.906l.564-.574h-.718l-.331-.74l-.282.74h-.842l.644.574z" clip-rule="evenodd"/></g>',i=[{viewBox:"0 0 32 24"},{width:"1.2em"},{height:"1.2em"},n[0]],a={};for(let l=0;l<i.length;l+=1)a=$(a,i[l]);return{c(){e=Ct("svg"),t=new _t(!0),this.h()},l(l){e=Pt(l,"svg",{viewBox:!0,width:!0,height:!0});var o=A(e);t=xt(o,!0),o.forEach(N),this.h()},h(){t.a=null,Z(e,a)},m(l,o){j(l,e,o),t.m(r,e)},p(l,[o]){Z(e,a=gt(i,[{viewBox:"0 0 32 24"},{width:"1.2em"},{height:"1.2em"},o&1&&l[0]]))},i:tt,o:tt,d(l){l&&N(e)}}}function nr(n,e,t){return n.$$set=r=>{t(0,e=$($({},e),G(r)))},e=G(e),[e]}class rr extends at{constructor(e){super(),it(this,e,nr,er,rt,{})}}function ar(n){let e,t,r='<path fill="currentColor" d="m12 15l-5-5h10z"/>',i=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},n[0]],a={};for(let l=0;l<i.length;l+=1)a=$(a,i[l]);return{c(){e=Ct("svg"),t=new _t(!0),this.h()},l(l){e=Pt(l,"svg",{viewBox:!0,width:!0,height:!0});var o=A(e);t=xt(o,!0),o.forEach(N),this.h()},h(){t.a=null,Z(e,a)},m(l,o){j(l,e,o),t.m(r,e)},p(l,[o]){Z(e,a=gt(i,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},o&1&&l[0]]))},i:tt,o:tt,d(l){l&&N(e)}}}function ir(n,e,t){return n.$$set=r=>{t(0,e=$($({},e),G(r)))},e=G(e),[e]}class lr extends at{constructor(e){super(),it(this,e,ir,ar,rt,{})}}function ae(n,e,t){const r=n.slice();return r[15]=e[t],r}function ie(n){let e,t,r,i=kt(n[5]),a=[];for(let o=0;o<i.length;o+=1)a[o]=le(ae(n,i,o));const l=o=>D(a[o],1,1,()=>{a[o]=null});return{c(){e=T("ul");for(let o=0;o<a.length;o+=1)a[o].c();this.h()},l(o){e=F(o,"UL",{class:!0});var u=A(e);for(let s=0;s<a.length;s+=1)a[s].l(u);u.forEach(N),this.h()},h(){O(e,"class","absolute bottom-[120%] left-0 z-[1] w-full bg-slate-50 shadow-app rounded-2xl max-h-56 overflow-auto")},m(o,u){j(o,e,u);for(let s=0;s<a.length;s+=1)a[s]&&a[s].m(e,null);r=!0},p(o,u){if(u&544){i=kt(o[5]);let s;for(s=0;s<i.length;s+=1){const h=ae(o,i,s);a[s]?(a[s].p(h,u),I(a[s],1)):(a[s]=le(h),a[s].c(),I(a[s],1),a[s].m(e,null))}for(st(),s=i.length;s<a.length;s+=1)l(s);ft()}},i(o){if(!r){for(let u=0;u<i.length;u+=1)I(a[u]);o&&ge(()=>{r&&(t||(t=Et(e,Ot,{axis:"y",duration:150},!0)),t.run(1))}),r=!0}},o(o){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)D(a[u]);o&&(t||(t=Et(e,Ot,{axis:"y",duration:150},!1)),t.run(0)),r=!1},d(o){o&&N(e),de(a,o),o&&t&&t.end()}}}function le(n){let e,t,r,i,a,l=n[15].title+"",o,u,s,h,p;var f=n[15].icon;function C(m,v){return{}}f&&(r=ut(f,C()));function x(){return n[13](n[15])}return{c(){e=T("li"),t=T("button"),r&&L(r.$$.fragment),i=E(),a=T("span"),o=K(l),u=E(),this.h()},l(m){e=F(m,"LI",{class:!0});var v=A(e);t=F(v,"BUTTON",{class:!0});var _=A(t);r&&Q(r.$$.fragment,_),i=k(_),a=F(_,"SPAN",{});var d=A(a);o=J(d,l),d.forEach(N),_.forEach(N),u=k(v),v.forEach(N),this.h()},h(){O(t,"class","flex items-center w-full gap-3 p-3"),O(e,"class","border-b last-of-type:border-b-0 active:bg-white first-of-type:rounded-t-2xl last-of-type:rounded-b-2xl")},m(m,v){j(m,e,v),y(e,t),r&&z(r,t,null),y(t,i),y(t,a),y(a,o),y(e,u),s=!0,h||(p=et(t,"click",x),h=!0)},p(m,v){if(n=m,f!==(f=n[15].icon)){if(r){st();const _=r;D(_.$$.fragment,1,0,()=>{B(_,1)}),ft()}f?(r=ut(f,C()),L(r.$$.fragment),I(r.$$.fragment,1),z(r,t,i)):r=null}},i(m){s||(r&&I(r.$$.fragment,m),s=!0)},o(m){r&&D(r.$$.fragment,m),s=!1},d(m){m&&N(e),r&&B(r),h=!1,p()}}}function or(n){let e,t,r,i,a,l,o,u,s,h,p,f,C;var x=n[4];function m(c,g){return{}}x&&(r=ut(x,m())),a=new lr({props:{class:X("opacity-50 transition-all",{"-rotate-180":n[3]})}});let v=[n[10],{value:n[0]},{class:u=X("flex-1 w-full font-theme outline-none bg-slate-50 rounded-r-2xl placeholder:text-primary-500/50 py-4 px-3",{"placeholder:text-red-500/80":!n[2]})},{type:"tel"}],_={};for(let c=0;c<v.length;c+=1)_=$(_,v[c]);let d=n[3]&&ie(n);return{c(){e=T("div"),t=T("button"),r&&L(r.$$.fragment),i=E(),L(a.$$.fragment),l=E(),o=T("input"),s=E(),d&&d.c(),this.h()},l(c){e=F(c,"DIV",{class:!0});var g=A(e);t=F(g,"BUTTON",{class:!0});var w=A(t);r&&Q(r.$$.fragment,w),i=k(w),Q(a.$$.fragment,w),w.forEach(N),l=k(g),o=F(g,"INPUT",{class:!0,type:!0}),s=k(g),d&&d.l(g),g.forEach(N),this.h()},h(){O(t,"class","shrink-0 self-stretch flex items-center pl-3 pr-1"),At(o,_),O(e,"class",h=X("relative bg-white text-black text-3xl flex items-center rounded-2xl shadow-md",{"outline outline-2 outline-red-500/80":!n[2],"outline outline-2 outline-primary-200":n[1]}))},m(c,g){j(c,e,g),y(e,t),r&&z(r,t,null),y(t,i),z(a,t,null),y(e,l),y(e,o),o.autofocus&&o.focus(),y(e,s),d&&d.m(e,null),p=!0,f||(C=[et(t,"click",n[12]),et(o,"input",n[6]),et(o,"focus",n[7]),et(o,"blur",n[8])],f=!0)},p(c,[g]){if(g&16&&x!==(x=c[4])){if(r){st();const Y=r;D(Y.$$.fragment,1,0,()=>{B(Y,1)}),ft()}x?(r=ut(x,m()),L(r.$$.fragment),I(r.$$.fragment,1),z(r,t,i)):r=null}const w={};g&8&&(w.class=X("opacity-50 transition-all",{"-rotate-180":c[3]})),a.$set(w),At(o,_=gt(v,[g&1024&&c[10],(!p||g&1&&o.value!==c[0])&&{value:c[0]},(!p||g&4&&u!==(u=X("flex-1 w-full font-theme outline-none bg-slate-50 rounded-r-2xl placeholder:text-primary-500/50 py-4 px-3",{"placeholder:text-red-500/80":!c[2]})))&&{class:u},{type:"tel"}])),c[3]?d?(d.p(c,g),g&8&&I(d,1)):(d=ie(c),d.c(),I(d,1),d.m(e,null)):d&&(st(),D(d,1,1,()=>{d=null}),ft()),(!p||g&6&&h!==(h=X("relative bg-white text-black text-3xl flex items-center rounded-2xl shadow-md",{"outline outline-2 outline-red-500/80":!c[2],"outline outline-2 outline-primary-200":c[1]})))&&O(e,"class",h)},i(c){p||(r&&I(r.$$.fragment,c),I(a.$$.fragment,c),I(d),p=!0)},o(c){r&&D(r.$$.fragment,c),D(a.$$.fragment,c),D(d),p=!1},d(c){c&&N(e),r&&B(r),B(a),d&&d.d(),f=!1,he(C)}}}function ur(n,e,t){let r;const i=["value"];let a=Tt(e,i);const l=[{title:"United States (+1)",value:"US",icon:rr},{title:"Canada (+1)",value:"CA",icon:tr}];let{value:o}=e;const u=me();let s=!1,h=!0,p=!1,f="CA";const C=c=>{t(0,o=c.target.value)},x=()=>{t(1,s=!0),t(2,h=!0),u("focus")},m=()=>{t(1,s=!1),t(2,h=!o||re(o,f)),u("blur")},v=c=>{t(11,f=c),t(3,p=!1)},_=()=>t(3,p=!p),d=c=>v(c.value);return n.$$set=c=>{e=$($({},e),G(c)),t(10,a=Tt(e,i)),"value"in c&&t(0,o=c.value)},n.$$.update=()=>{var c;if(n.$$.dirty&2048&&t(4,r=((c=l.find(g=>g.value===f))==null?void 0:c.icon)??null),n.$$.dirty&2049){const g=new mt(f).input(o);g.charAt(g.length-1)===o.charAt(o.length-1)&&t(0,o=g)}n.$$.dirty&2049&&u("validation",re(o,f))},[o,s,h,p,r,l,C,x,m,v,a,f,_,d]}class sr extends at{constructor(e){super(),it(this,e,ur,or,rt,{value:0})}}const fr=(n,e)=>{const t=ve(e),r=i=>{const a=Ft(Rt).url;i?a.searchParams.set(n,String(i)):a.searchParams.delete(n);const l=a.searchParams.toString().length>0?`${a.pathname}?${a.searchParams.toString()}`:a.pathname;ue(l,{keepFocus:!0,noScroll:!0})};return pe(()=>{const i=Ft(Rt).url.searchParams.get(n);t.set(i||e);const a=t.subscribe(l=>{r(l)});return()=>{a()}}),t},cr=async(n,e={})=>{const t=e.query?Object.fromEntries(Object.entries(e.query).map(([a,l])=>[a,String(l)])):{},r=new URLSearchParams(t).toString(),i=r?`${n}?${r}`:n;await ue(i,e)};function oe(n){let e,t=n[4]("login.invalid_phone_number")+"",r;return{c(){e=T("p"),r=K(t),this.h()},l(i){e=F(i,"P",{class:!0});var a=A(e);r=J(a,t),a.forEach(N),this.h()},h(){O(e,"class","text-xs text-red-500 mt-3")},m(i,a){j(i,e,a),y(e,r)},p(i,a){a&16&&t!==(t=i[4]("login.invalid_phone_number")+"")&&nt(r,t)},d(i){i&&N(e)}}}function hr(n){let e=n[4]("login.send_code")+"",t;return{c(){t=K(e)},l(r){t=J(r,e)},m(r,i){j(r,t,i)},p(r,i){i&16&&e!==(e=r[4]("login.send_code")+"")&&nt(t,e)},d(r){r&&N(t)}}}function mr(n){let e,t,r,i,a=`<img src="${Ve}" alt="HIKO" width="240"/>`,l,o,u,s=n[4]("login.keep_charged")+"",h,p,f=n[4]("login.without_pausing")+"",C,x,m,v,_=n[4]("login.enter_phone_number")+"",d,c,g,w,Y,dt,pt,M,H;t=new Pe({props:{class:"text-3xl font-theme text-black",buttonClass:"uppercase"}});function ce(b){n[8](b)}let wt={placeholder:"(*************"};n[3]!==void 0&&(wt.value=n[3]),w=new sr({props:wt}),be.push(()=>Ce(w,"value",ce)),w.$on("validation",n[6]);let S=n[2].invalid_phone_number&&oe(n);return M=new _e({props:{loading:n[1],disabled:!n[3]||!n[0]||n[1],class:"w-full bg-white text-primary-500 font-theme uppercase text-3xl mt-6",$$slots:{default:[hr]},$$scope:{ctx:n}}}),M.$on("click",n[7]),{c(){e=T("div"),L(t.$$.fragment),r=E(),i=T("div"),i.innerHTML=a,l=E(),o=T("p"),u=T("b"),h=K(s),p=E(),C=K(f),x=E(),m=T("div"),v=T("p"),d=K(_),c=E(),g=T("div"),L(w.$$.fragment),dt=E(),S&&S.c(),pt=E(),L(M.$$.fragment),this.h()},l(b){e=F(b,"DIV",{class:!0});var P=A(e);Q(t.$$.fragment,P),r=k(P),i=F(P,"DIV",{class:!0,"data-svelte-h":!0}),ye(i)!=="svelte-1ykgtmx"&&(i.innerHTML=a),l=k(P),o=F(P,"P",{class:!0});var U=A(o);u=F(U,"B",{});var W=A(u);h=J(W,s),W.forEach(N),p=k(U),C=J(U,f),U.forEach(N),x=k(P),m=F(P,"DIV",{class:!0});var q=A(m);v=F(q,"P",{class:!0});var St=A(v);d=J(St,_),St.forEach(N),c=k(q),g=F(q,"DIV",{});var lt=A(g);Q(w.$$.fragment,lt),dt=k(lt),S&&S.l(lt),lt.forEach(N),pt=k(q),Q(M.$$.fragment,q),q.forEach(N),P.forEach(N),this.h()},h(){O(i,"class","flex justify-center mt-12"),O(o,"class","text-center"),O(v,"class","text-xl font-medium text-center mb-3"),O(m,"class","mt-auto"),O(e,"class","flex flex-col flex-1 bg-primary-500 text-white px-3 py-6")},m(b,P){j(b,e,P),z(t,e,null),y(e,r),y(e,i),y(e,l),y(e,o),y(o,u),y(u,h),y(o,p),y(o,C),y(e,x),y(e,m),y(m,v),y(v,d),y(m,c),y(m,g),z(w,g,null),y(g,dt),S&&S.m(g,null),y(m,pt),z(M,m,null),H=!0},p(b,[P]){(!H||P&16)&&s!==(s=b[4]("login.keep_charged")+"")&&nt(h,s),(!H||P&16)&&f!==(f=b[4]("login.without_pausing")+"")&&nt(C,f),(!H||P&16)&&_!==(_=b[4]("login.enter_phone_number")+"")&&nt(d,_);const U={};!Y&&P&8&&(Y=!0,U.value=b[3],Ne(()=>Y=!1)),w.$set(U),b[2].invalid_phone_number?S?S.p(b,P):(S=oe(b),S.c(),S.m(g,null)):S&&(S.d(1),S=null);const W={};P&2&&(W.loading=b[1]),P&11&&(W.disabled=!b[3]||!b[0]||b[1]),P&528&&(W.$$scope={dirty:P,ctx:b}),M.$set(W)},i(b){H||(I(t.$$.fragment,b),I(w.$$.fragment,b),I(M.$$.fragment,b),H=!0)},o(b){D(t.$$.fragment,b),D(w.$$.fragment,b),D(M.$$.fragment,b),H=!1},d(b){b&&N(e),B(t),B(w),S&&S.d(),B(M)}}}function gr(n,e,t){let r,i;It(n,xe,f=>t(4,i=f));const a=fr("phone","");It(n,a,f=>t(3,r=f));let l=!0,o=!1,u={invalid_phone_number:!1};Ge({isVisible:!1});const s=f=>{t(0,l=f.detail),t(2,u.invalid_phone_number=!1,u)},h=async()=>{try{t(1,o=!0);const f=We(r,"CA").number,{error:C}=await Ye.auth.signInWithOtp({phone:f});if(C){t(2,u.invalid_phone_number=C.name==="AuthApiError",u);return}await cr(we.LoginOTP,{query:{phone_number:f}})}catch(f){console.error(f)}finally{t(1,o=!1)}};function p(f){r=f,a.set(r)}return[l,o,u,r,i,a,s,h,p]}class Tr extends at{constructor(e){super(),it(this,e,gr,mr,rt,{})}}export{Tr as component};
