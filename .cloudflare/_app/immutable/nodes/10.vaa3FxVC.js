import{s as z,e as x,c as y,a as w,d,b as p,i as I,r as L,q as F,A as P,B as q,C as Z,l as k,m as E,h as _,f as ee,t as V,g as $,U as te,p as A,j as O}from"../chunks/index.UaHqEmIZ.js";import{S as W,i as Y,c as C,a as M,m as H,t as T,b as G,d as J}from"../chunks/index.RK-K-o1D.js";import{e as S}from"../chunks/each.N0rfVI1r.js";import{g as se}from"../chunks/entry.vPeIVMYj.js";import{B as ae}from"../chunks/Button.dvflqaYf.js";import{c as R,t as le,R as ne}from"../chunks/style.HZSn-yMG.js";import{u as re}from"../chunks/useTopBar._QombAUQ.js";import{s as ie}from"../chunks/supabase.xRgAeO37.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="b668aa44-3333-4d5e-b6cb-229dbb282a33",n._sentryDebugIdIdentifier="sentry-dbid-b668aa44-3333-4d5e-b6cb-229dbb282a33")}catch{}})();function K(n,e,l){const s=n.slice();return s[4]=e[l],s}function N(n){let e,l;return{c(){e=x("div"),this.h()},l(s){e=y(s,"DIV",{class:!0}),w(e).forEach(d),this.h()},h(){p(e,"class",l=R("rounded-full w-3 h-3",{"bg-white/30":n[4]!==n[0],"bg-white":n[4]===n[0]}))},m(s,t){I(s,e,t)},p(s,t){t&3&&l!==(l=R("rounded-full w-3 h-3",{"bg-white/30":s[4]!==s[0],"bg-white":s[4]===s[0]}))&&p(e,"class",l)},d(s){s&&d(e)}}}function oe(n){let e,l,s=S(n[1]),t=[];for(let a=0;a<s.length;a+=1)t[a]=N(K(n,s,a));return{c(){e=x("div");for(let a=0;a<t.length;a+=1)t[a].c();this.h()},l(a){e=y(a,"DIV",{class:!0});var i=w(e);for(let r=0;r<t.length;r+=1)t[r].l(i);i.forEach(d),this.h()},h(){p(e,"class",l=R("flex items-center justify-center gap-3",n[2].class))},m(a,i){I(a,e,i);for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(e,null)},p(a,[i]){if(i&3){s=S(a[1]);let r;for(r=0;r<s.length;r+=1){const u=K(a,s,r);t[r]?t[r].p(u,i):(t[r]=N(u),t[r].c(),t[r].m(e,null))}for(;r<t.length;r+=1)t[r].d(1);t.length=s.length}i&4&&l!==(l=R("flex items-center justify-center gap-3",a[2].class))&&p(e,"class",l)},i:L,o:L,d(a){a&&d(e),F(t,a)}}}function ce(n,e,l){let s;const t=["step","max"];let a=P(e,t),{step:i=0}=e,{max:r=0}=e;return n.$$set=u=>{e=q(q({},e),Z(u)),l(2,a=P(e,t)),"step"in u&&l(0,i=u.step),"max"in u&&l(3,r=u.max)},n.$$.update=()=>{n.$$.dirty&8&&l(1,s=new Array(r).fill(0).map((u,g)=>g))},[i,s,a,r]}class fe extends W{constructor(e){super(),Y(this,e,ce,oe,z,{step:0,max:3})}}const ue=""+new URL("../assets/step-1.b4KhVovJ.svg",import.meta.url).href,pe=""+new URL("../assets/step-2.pdayVylf.svg",import.meta.url).href,de=""+new URL("../assets/step-3.MUQr3kPM.svg",import.meta.url).href;function Q(n,e,l){const s=n.slice();return s[5]=e[l],s[7]=l,s}function X(n){let e,l,s,t,a,i,r,u=n[5].title+"",g,f,o,m=n[5].description+"",h,v;return{c(){e=x("article"),l=x("div"),s=x("img"),a=k(),i=x("div"),r=x("h2"),g=V(u),f=k(),o=x("p"),h=V(m),v=k(),this.h()},l(c){e=y(c,"ARTICLE",{class:!0,style:!0});var b=w(e);l=y(b,"DIV",{class:!0});var U=w(l);s=y(U,"IMG",{src:!0,alt:!0,class:!0}),U.forEach(d),a=E(b),i=y(b,"DIV",{class:!0});var D=w(i);r=y(D,"H2",{class:!0});var j=w(r);g=$(j,u),j.forEach(d),f=E(D),o=y(D,"P",{class:!0});var B=w(o);h=$(B,m),B.forEach(d),D.forEach(d),v=E(b),b.forEach(d),this.h()},h(){te(s.src,t=n[5].icon)||p(s,"src",t),p(s,"alt",`step-${n[7]+1}`),p(s,"class","w-full rounded-b-3xl bg-primary-100"),p(l,"class","flex justify-center max-w-[450px] mx-auto px-12"),p(r,"class","font-semibold text-2xl xxs:text-3xl mt-6 mb-3 px-6"),p(o,"class","text-sm xxs:text-lg px-6"),p(i,"class","max-w-[450px] mx-auto"),p(e,"class","flex flex-col min-w-full max-w-app text-white text-center transition-transform"),p(e,"style",n[1])},m(c,b){I(c,e,b),_(e,l),_(l,s),_(e,a),_(e,i),_(i,r),_(r,g),_(i,f),_(i,o),_(o,h),_(e,v)},p(c,b){b&2&&p(e,"style",c[1])},d(c){c&&d(e)}}}function me(n){let e=n[2]("onboarding.discover")+"",l;return{c(){l=V(e)},l(s){l=$(s,e)},m(s,t){I(s,l,t)},p(s,t){t&4&&e!==(e=s[2]("onboarding.discover")+"")&&O(l,e)},d(s){s&&d(l)}}}function he(n){let e=n[2]("onboarding.next")+"",l;return{c(){l=V(e)},l(s){l=$(s,e)},m(s,t){I(s,l,t)},p(s,t){t&4&&e!==(e=s[2]("onboarding.next")+"")&&O(l,e)},d(s){s&&d(l)}}}function _e(n){let e;function l(a,i){return a[0]<a[3].length-1?he:me}let s=l(n),t=s(n);return{c(){t.c(),e=A()},l(a){t.l(a),e=A()},m(a,i){t.m(a,i),I(a,e,i)},p(a,i){s===(s=l(a))&&t?t.p(a,i):(t.d(1),t=s(a),t&&(t.c(),t.m(e.parentNode,e)))},d(a){a&&d(e),t.d(a)}}}function ge(n){let e,l,s,t,a,i,r,u,g=S(n[3]),f=[];for(let o=0;o<g.length;o+=1)f[o]=X(Q(n,g,o));return a=new fe({props:{step:n[0],max:n[3].length,class:"mb-6"}}),r=new ae({props:{class:"w-full bg-white active:bg-slate-100 text-primary-500 font-theme uppercase text-3xl",$$slots:{default:[_e]},$$scope:{ctx:n}}}),r.$on("click",n[4]),{c(){e=x("div"),l=x("div");for(let o=0;o<f.length;o+=1)f[o].c();s=k(),t=x("div"),C(a.$$.fragment),i=k(),C(r.$$.fragment),this.h()},l(o){e=y(o,"DIV",{class:!0});var m=w(e);l=y(m,"DIV",{class:!0});var h=w(l);for(let c=0;c<f.length;c+=1)f[c].l(h);h.forEach(d),s=E(m),t=y(m,"DIV",{class:!0});var v=w(t);M(a.$$.fragment,v),i=E(v),M(r.$$.fragment,v),v.forEach(d),m.forEach(d),this.h()},h(){p(l,"class","flex flex-1 overflow-hidden pb-6"),p(t,"class","pb-6 px-6"),p(e,"class","flex flex-col flex-1 bg-primary-500")},m(o,m){I(o,e,m),_(e,l);for(let h=0;h<f.length;h+=1)f[h]&&f[h].m(l,null);_(e,s),_(e,t),H(a,t,null),_(t,i),H(r,t,null),u=!0},p(o,[m]){if(m&10){g=S(o[3]);let c;for(c=0;c<g.length;c+=1){const b=Q(o,g,c);f[c]?f[c].p(b,m):(f[c]=X(b),f[c].c(),f[c].m(l,null))}for(;c<f.length;c+=1)f[c].d(1);f.length=g.length}const h={};m&1&&(h.step=o[0]),a.$set(h);const v={};m&261&&(v.$$scope={dirty:m,ctx:o}),r.$set(v)},i(o){u||(T(a.$$.fragment,o),T(r.$$.fragment,o),u=!0)},o(o){G(a.$$.fragment,o),G(r.$$.fragment,o),u=!1},d(o){o&&d(e),F(f,o),J(a),J(r)}}}function be(n,e,l){let s,t;ee(n,le,u=>l(2,t=u));let a=0;const i=[{icon:pe,title:t("onboarding.step1"),description:t("onboarding.step1_description")},{icon:ue,title:t("onboarding.step2"),description:t("onboarding.step2_description")},{icon:de,title:t("onboarding.step3"),description:t("onboarding.step3_description")}];re({isVisible:!1});const r=async()=>{if(a>=i.length-1){await ie.auth.updateUser({data:{has_seen_onboarding:!0}}),await se(ne.Home);return}l(0,a++,a)};return n.$$.update=()=>{n.$$.dirty&1&&l(1,s=`transform: translateX(-${a*100}%)`)},[a,s,t,i,r]}class Ve extends W{constructor(e){super(),Y(this,e,be,ge,z,{})}}export{Ve as component};
