import{s as m,e as i,c as d,a as l,d as r,b as p,i as f,f as u,t as _,g as h,h as $,j as b}from"../chunks/index.UaHqEmIZ.js";import{S as y,i as g,c as v,a as w,m as I,t as D,b as E,d as x}from"../chunks/index.RK-K-o1D.js";import{E as P}from"../chunks/EmptyPlaceholder.ts_EQhA2.js";import{t as S}from"../chunks/style.HZSn-yMG.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new C().stack;t&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[t]="ede60f83-47b3-4874-b169-cb104a7e676d",s._sentryDebugIdIdentifier="sentry-dbid-ede60f83-47b3-4874-b169-cb104a7e676d")}catch{}})();function j(s){let t,n=s[0]("common.not_found")+"",o;return{c(){t=i("p"),o=_(n)},l(e){t=d(e,"P",{});var a=l(t);o=h(a,n),a.forEach(r)},m(e,a){f(e,t,a),$(t,o)},p(e,a){a&1&&n!==(n=e[0]("common.not_found")+"")&&b(o,n)},d(e){e&&r(t)}}}function k(s){let t,n,o;return n=new P({props:{$$slots:{default:[j]},$$scope:{ctx:s}}}),{c(){t=i("div"),v(n.$$.fragment),this.h()},l(e){t=d(e,"DIV",{class:!0});var a=l(t);w(n.$$.fragment,a),a.forEach(r),this.h()},h(){p(t,"class","mt-16")},m(e,a){f(e,t,a),I(n,t,null),o=!0},p(e,[a]){const c={};a&3&&(c.$$scope={dirty:a,ctx:e}),n.$set(c)},i(e){o||(D(n.$$.fragment,e),o=!0)},o(e){E(n.$$.fragment,e),o=!1},d(e){e&&r(t),x(n)}}}function q(s,t,n){let o;return u(s,S,e=>n(0,o=e)),[o]}class C extends y{constructor(t){super(),g(this,t,q,k,m,{})}}export{C as component};
