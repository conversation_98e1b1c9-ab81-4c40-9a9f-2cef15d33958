import{s as fe,B as G,J as qe,K as Ce,L as Me,a as $,M as Ne,d as p,N as de,i as B,r as se,C as ae,e as k,l as S,c as E,m as I,b as v,h as m,E as Ie,A as he,f as De,o as tt,v as lt,x as rt,y as st,z as it,t as V,g as j,j as R,q as at,U as We,k as me,G as nt,ac as ut,ad as Pe,n as be}from"../chunks/index.UaHqEmIZ.js";import{S as ce,i as _e,c as Q,a as Y,m as x,t as L,g as ve,b as C,f as we,d as ee,e as ke}from"../chunks/index.RK-K-o1D.js";import{e as Se}from"../chunks/each.N0rfVI1r.js";import{g as Te,c as pe,t as ot,R as Le}from"../chunks/style.HZSn-yMG.js";import{g as ze}from"../chunks/entry.vPeIVMYj.js";import{p as ft}from"../chunks/stores.sj_Ool2X.js";import{B as Ae}from"../chunks/Button.dvflqaYf.js";import{E as ct,D as _t}from"../chunks/ErrorDialog.zsuK_K7c.js";import{v as pt}from"../chunks/useModal.kL0JL2yE.js";import{u as dt}from"../chunks/useTopBar._QombAUQ.js";import{a as ht}from"../chunks/api.FojSz0ks.js";import{u as mt}from"../chunks/user.oAuVK7RJ.js";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[e]="dd5a37bc-61e1-41e6-9e0f-43d22971cd98",r._sentryDebugIdIdentifier="sentry-dbid-dd5a37bc-61e1-41e6-9e0f-43d22971cd98")}catch{}})();function gt(r){let e,t,s='<path fill="currentColor" d="M208 32H80a16 16 0 0 0-16 16v16H48a16 16 0 0 0-16 16v128a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-16h16a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16M80 48h128v69.38l-16.7-16.7a16 16 0 0 0-22.62 0L93.37 176H80Zm96 160H48V80h16v96a16 16 0 0 0 16 16h96ZM104 88a16 16 0 1 1 16 16a16 16 0 0 1-16-16"/>',i=[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},r[0]],a={};for(let l=0;l<i.length;l+=1)a=G(a,i[l]);return{c(){e=qe("svg"),t=new Ce(!0),this.h()},l(l){e=Me(l,"svg",{viewBox:!0,width:!0,height:!0});var n=$(e);t=Ne(n,!0),n.forEach(p),this.h()},h(){t.a=null,de(e,a)},m(l,n){B(l,e,n),t.m(s,e)},p(l,[n]){de(e,a=Te(i,[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},n&1&&l[0]]))},i:se,o:se,d(l){l&&p(e)}}}function bt(r,e,t){return r.$$set=s=>{t(0,e=G(G({},e),ae(s)))},e=ae(e),[e]}class vt extends ce{constructor(e){super(),_e(this,e,bt,gt,fe,{})}}function wt(r){let e,t,s='<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 7L7 17M7 7l10 10"/>',i=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},r[0]],a={};for(let l=0;l<i.length;l+=1)a=G(a,i[l]);return{c(){e=qe("svg"),t=new Ce(!0),this.h()},l(l){e=Me(l,"svg",{viewBox:!0,width:!0,height:!0});var n=$(e);t=Ne(n,!0),n.forEach(p),this.h()},h(){t.a=null,de(e,a)},m(l,n){B(l,e,n),t.m(s,e)},p(l,[n]){de(e,a=Te(i,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},n&1&&l[0]]))},i:se,o:se,d(l){l&&p(e)}}}function kt(r,e,t){return r.$$set=s=>{t(0,e=G(G({},e),ae(s)))},e=ae(e),[e]}class Et extends ce{constructor(e){super(),_e(this,e,kt,wt,fe,{})}}function $t(r){let e,t,s='<path fill="currentColor" d="M18 15.75q0 2.6-1.825 4.425T11.75 22q-2.6 0-4.425-1.825T5.5 15.75V6.5q0-1.875 1.313-3.187T10 2q1.875 0 3.188 1.313T14.5 6.5v8.75q0 1.15-.8 1.95t-1.95.8q-1.15 0-1.95-.8T9 15.25V6h2v9.25q0 .325.213.538t.537.212q.325 0 .538-.213t.212-.537V6.5q-.025-1.05-.737-1.775T10 4q-1.05 0-1.775.725T7.5 6.5v9.25q-.025 1.775 1.225 3.013T11.75 20q1.75 0 2.975-1.237T16 15.75V6h2z"/>',i=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},r[0]],a={};for(let l=0;l<i.length;l+=1)a=G(a,i[l]);return{c(){e=qe("svg"),t=new Ce(!0),this.h()},l(l){e=Me(l,"svg",{viewBox:!0,width:!0,height:!0});var n=$(e);t=Ne(n,!0),n.forEach(p),this.h()},h(){t.a=null,de(e,a)},m(l,n){B(l,e,n),t.m(s,e)},p(l,[n]){de(e,a=Te(i,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},n&1&&l[0]]))},i:se,o:se,d(l){l&&p(e)}}}function yt(r,e,t){return r.$$set=s=>{t(0,e=G(G({},e),ae(s)))},e=ae(e),[e]}class Bt extends ce{constructor(e){super(),_e(this,e,yt,$t,fe,{})}}function Je(r,e,t){const s=r.slice();return s[14]=e[t],s[16]=t,s}function Dt(r){let e,t,s,i="Attach files",a;return e=new Bt({}),{c(){Q(e.$$.fragment),t=S(),s=k("span"),s.textContent=i},l(l){Y(e.$$.fragment,l),t=I(l),s=E(l,"SPAN",{"data-svelte-h":!0}),nt(s)!=="svelte-186f7fg"&&(s.textContent=i)},m(l,n){x(e,l,n),B(l,t,n),B(l,s,n),a=!0},p:se,i(l){a||(L(e.$$.fragment,l),a=!0)},o(l){C(e.$$.fragment,l),a=!1},d(l){l&&(p(t),p(s)),ee(e,l)}}}function Pt(r){let e;const t=r[9].default,s=lt(t,r,r[13],null),i=s||Dt();return{c(){i&&i.c()},l(a){i&&i.l(a)},m(a,l){i&&i.m(a,l),e=!0},p(a,l){s&&s.p&&(!e||l&8192)&&rt(s,t,a,a[13],e?it(t,a[13],l,null):st(a[13]),null)},i(a){e||(L(i,a),e=!0)},o(a){C(i,a),e=!1},d(a){i&&i.d(a)}}}function Xe(r){let e,t,s,i=r[5]("common.files_maximum")+"",a;return{c(){e=k("p"),t=V(r[2]),s=S(),a=V(i),this.h()},l(l){e=E(l,"P",{class:!0});var n=$(e);t=j(n,r[2]),s=I(n),a=j(n,i),n.forEach(p),this.h()},h(){v(e,"class","text-sm text-center opacity-50 mt-3")},m(l,n){B(l,e,n),m(e,t),m(e,s),m(e,a)},p(l,n){n&4&&R(t,l[2]),n&32&&i!==(i=l[5]("common.files_maximum")+"")&&R(a,i)},d(l){l&&p(e)}}}function Ze(r){let e,t,s=Se(r[4]),i=[];for(let l=0;l<s.length;l+=1)i[l]=Ke(Je(r,s,l));const a=l=>C(i[l],1,1,()=>{i[l]=null});return{c(){e=k("ul");for(let l=0;l<i.length;l+=1)i[l].c();this.h()},l(l){e=E(l,"UL",{class:!0});var n=$(e);for(let o=0;o<i.length;o+=1)i[o].l(n);n.forEach(p),this.h()},h(){v(e,"class","grid grid-cols-3 gap-3 mt-3")},m(l,n){B(l,e,n);for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(e,null);t=!0},p(l,n){if(n&400){s=Se(l[4]);let o;for(o=0;o<s.length;o+=1){const _=Je(l,s,o);i[o]?(i[o].p(_,n),L(i[o],1)):(i[o]=Ke(_),i[o].c(),L(i[o],1),i[o].m(e,null))}for(ve(),o=s.length;o<i.length;o+=1)a(o);we()}},i(l){if(!t){for(let n=0;n<s.length;n+=1)L(i[n]);t=!0}},o(l){i=i.filter(Boolean);for(let n=0;n<i.length;n+=1)C(i[n]);t=!1},d(l){l&&p(e),at(i,l)}}}function Ke(r){let e,t,s,i,a,l,n,o,_,f,c;l=new Et({props:{width:"22",height:"22"}});function u(){return r[11](r[16])}return{c(){e=k("li"),t=k("img"),i=S(),a=k("button"),Q(l.$$.fragment),o=S(),this.h()},l(d){e=E(d,"LI",{class:!0});var h=$(e);t=E(h,"IMG",{src:!0,alt:!0,class:!0}),i=I(h),a=E(h,"BUTTON",{class:!0});var b=$(a);Y(l.$$.fragment,b),b.forEach(p),o=I(h),h.forEach(p),this.h()},h(){We(t.src,s=r[14])||v(t,"src",s),v(t,"alt","file"),v(t,"class","w-full h-full object-contain"),v(a,"class","absolute -top-1 -right-1 bg-white rounded-full border border-slate-200 disabled:opacity-50 p-0.5"),a.disabled=n=r[8].disabled,v(e,"class","relative col-span-1 border border-dashed border-slate-200 rounded-2xl aspect-square flex items-center justify-center p-3")},m(d,h){B(d,e,h),m(e,t),m(e,i),m(e,a),x(l,a,null),m(e,o),_=!0,f||(c=Ie(a,"click",u),f=!0)},p(d,h){r=d,(!_||h&16&&!We(t.src,s=r[14]))&&v(t,"src",s),(!_||h&256&&n!==(n=r[8].disabled))&&(a.disabled=n)},i(d){_||(L(l.$$.fragment,d),_=!0)},o(d){C(l.$$.fragment,d),_=!1},d(d){d&&p(e),ee(l),f=!1,c()}}}function St(r){let e,t,s,i,a,l,n,o,_,f,c;t=new Ae({props:{disabled:r[0].length>=r[2]||r[8].disabled,class:pe(r[8].class),$$slots:{default:[Pt]},$$scope:{ctx:r}}}),t.$on("click",r[10]);let u=r[8].multiple&&Xe(r),d=r[1]&&Ze(r);return{c(){e=k("div"),Q(t.$$.fragment),s=S(),u&&u.c(),i=S(),d&&d.c(),a=S(),l=k("input"),this.h()},l(h){e=E(h,"DIV",{});var b=$(e);Y(t.$$.fragment,b),s=I(b),u&&u.l(b),i=I(b),d&&d.l(b),a=I(b),l=E(b,"INPUT",{type:!0,accept:!0,class:!0}),b.forEach(p),this.h()},h(){v(l,"type","file"),v(l,"accept",n=r[8].accept),l.multiple=o=r[8].multiple,v(l,"class","hidden")},m(h,b){B(h,e,b),x(t,e,null),m(e,s),u&&u.m(e,null),m(e,i),d&&d.m(e,null),m(e,a),m(e,l),r[12](l),_=!0,f||(c=Ie(l,"input",r[6]),f=!0)},p(h,[b]){const M={};b&261&&(M.disabled=h[0].length>=h[2]||h[8].disabled),b&256&&(M.class=pe(h[8].class)),b&8192&&(M.$$scope={dirty:b,ctx:h}),t.$set(M),h[8].multiple?u?u.p(h,b):(u=Xe(h),u.c(),u.m(e,i)):u&&(u.d(1),u=null),h[1]?d?(d.p(h,b),b&2&&L(d,1)):(d=Ze(h),d.c(),L(d,1),d.m(e,a)):d&&(ve(),C(d,1,1,()=>{d=null}),we()),(!_||b&256&&n!==(n=h[8].accept))&&v(l,"accept",n),(!_||b&256&&o!==(o=h[8].multiple))&&(l.multiple=o)},i(h){_||(L(t.$$.fragment,h),L(d),_=!0)},o(h){C(t.$$.fragment,h),C(d),_=!1},d(h){h&&p(e),ee(t),u&&u.d(),d&&d.d(),r[12](null),f=!1,c()}}}function It(r,e,t){const s=["value","preview","limit"];let i=he(e,s),a;De(r,ot,g=>t(5,a=g));let{$$slots:l={},$$scope:n}=e,{value:o=[]}=e,{preview:_=!1}=e,{limit:f=3}=e,c,u=[];const d=g=>{const y=g.target.files;if(!y)return;const T=Array.from(y).filter(U=>!o.some(W=>W.name===U.name));t(0,o=[...o,...T].slice(0,f)),_&&t(4,u=[...u,...Array.from(T).map(U=>URL.createObjectURL(U))].slice(0,f)),t(3,c.value="",c)},h=g=>{t(0,o=o.filter((y,T)=>T!==g)),t(4,u=u.filter((y,T)=>T!==g))};tt(()=>()=>{u.forEach(g=>URL.revokeObjectURL(g))});const b=()=>c.click(),M=g=>h(g);function D(g){me[g?"unshift":"push"](()=>{c=g,t(3,c)})}return r.$$set=g=>{e=G(G({},e),ae(g)),t(8,i=he(e,s)),"value"in g&&t(0,o=g.value),"preview"in g&&t(1,_=g.preview),"limit"in g&&t(2,f=g.limit),"$$scope"in g&&t(13,n=g.$$scope)},[o,_,f,c,u,a,d,h,i,l,b,M,D,n]}class At extends ce{constructor(e){super(),_e(this,e,It,St,fe,{value:0,preview:1,limit:2})}}function Qe(r){let e;return{c(){e=k("span"),this.h()},l(t){e=E(t,"SPAN",{class:!0}),$(e).forEach(p),this.h()},h(){v(e,"class","w-2.5 h-2.5 rounded-full bg-primary-500")},m(t,s){B(t,e,s)},d(t){t&&p(e)}}}function Lt(r){let e,t,s=!1,i,a,l,n,o,_,f,c,u,d,h,b=r[0]===r[2]&&Qe();const M=r[5].default,D=lt(M,r,r[4],null);return u=ut(r[7][0]),{c(){e=k("article"),t=k("input"),i=S(),a=k("label"),l=k("span"),b&&b.c(),o=S(),_=k("span"),D&&D.c(),this.h()},l(g){e=E(g,"ARTICLE",{class:!0});var y=$(e);t=E(y,"INPUT",{type:!0,name:!0,id:!0,class:!0}),i=I(y),a=E(y,"LABEL",{class:!0,for:!0});var T=$(a);l=E(T,"SPAN",{class:!0});var U=$(l);b&&b.l(U),U.forEach(p),o=I(T),_=E(T,"SPAN",{});var W=$(_);D&&D.l(W),W.forEach(p),T.forEach(p),y.forEach(p),this.h()},h(){v(t,"type","radio"),t.__value=r[2],Pe(t,t.__value),v(t,"name",r[1]),v(t,"id",r[2]),v(t,"class","pointer-events-none hidden"),v(l,"class",n=pe("border-2 border-black/20 rounded-full w-5 h-5 shrink-0 flex items-center justify-center mt-1",{"border-primary-500":r[0]===r[2]})),v(a,"class","cursor-pointer flex gap-4 text-left py-4 px-3"),v(a,"for",r[2]),v(e,"class",f=pe("border-2 border-white rounded-2xl shadow-app",{"border-primary-500":r[0]===r[2]},r[3].class)),u.p(t)},m(g,y){B(g,e,y),m(e,t),t.checked=t.__value===r[0],m(e,i),m(e,a),m(a,l),b&&b.m(l,null),m(a,o),m(a,_),D&&D.m(_,null),c=!0,d||(h=Ie(t,"change",r[6]),d=!0)},p(g,[y]){(!c||y&4)&&(t.__value=g[2],Pe(t,t.__value),s=!0),(!c||y&2)&&v(t,"name",g[1]),(!c||y&4)&&v(t,"id",g[2]),(s||y&1)&&(t.checked=t.__value===g[0]),g[0]===g[2]?b||(b=Qe(),b.c(),b.m(l,null)):b&&(b.d(1),b=null),(!c||y&5&&n!==(n=pe("border-2 border-black/20 rounded-full w-5 h-5 shrink-0 flex items-center justify-center mt-1",{"border-primary-500":g[0]===g[2]})))&&v(l,"class",n),D&&D.p&&(!c||y&16)&&rt(D,M,g,g[4],c?it(M,g[4],y,null):st(g[4]),null),(!c||y&4)&&v(a,"for",g[2]),(!c||y&13&&f!==(f=pe("border-2 border-white rounded-2xl shadow-app",{"border-primary-500":g[0]===g[2]},g[3].class)))&&v(e,"class",f)},i(g){c||(L(D,g),c=!0)},o(g){C(D,g),c=!1},d(g){g&&p(e),b&&b.d(),D&&D.d(g),u.r(),d=!1,h()}}}function qt(r,e,t){const s=["value","name","id"];let i=he(e,s),{$$slots:a={},$$scope:l}=e,{value:n}=e,{name:o=""}=e,{id:_=pt()}=e;const f=[[]];function c(){n=this.__value,t(0,n)}return r.$$set=u=>{e=G(G({},e),ae(u)),t(3,i=he(e,s)),"value"in u&&t(0,n=u.value),"name"in u&&t(1,o=u.name),"id"in u&&t(2,_=u.id),"$$scope"in u&&t(4,l=u.$$scope)},[n,o,_,i,l,a,c,f]}class Ct extends ce{constructor(e){super(),_e(this,e,qt,Lt,fe,{value:0,name:1,id:2})}}function Ye(r){let e,t,s=r[0].length+"",i,a,l=r[1].maxlength+"",n;return{c(){e=k("div"),t=k("p"),i=V(s),a=V(" / "),n=V(l),this.h()},l(o){e=E(o,"DIV",{class:!0});var _=$(e);t=E(_,"P",{class:!0});var f=$(t);i=j(f,s),a=j(f," / "),n=j(f,l),f.forEach(p),_.forEach(p),this.h()},h(){v(t,"class","text-xs font-medium text-black/50 leading-none mt-1 mr-1"),v(e,"class","absolute bottom-2 right-1")},m(o,_){B(o,e,_),m(e,t),m(t,i),m(t,a),m(t,n)},p(o,_){_&1&&s!==(s=o[0].length+"")&&R(i,s),_&2&&l!==(l=o[1].maxlength+"")&&R(n,l)},d(o){o&&p(e)}}}function Mt(r){let e,t,s,i,a,l,n,o,_=r[1].maxlength&&Ye(r);return{c(){e=k("div"),t=k("textarea"),l=S(),_&&_.c(),this.h()},l(f){e=E(f,"DIV",{class:!0});var c=$(e);t=E(c,"TEXTAREA",{placeholder:!0,maxlength:!0,rows:!0,class:!0}),$(t).forEach(p),l=I(c),_&&_.l(c),c.forEach(p),this.h()},h(){v(t,"placeholder",s=r[1].placeholder),v(t,"maxlength",i=r[1].maxlength),v(t,"rows",a=r[1].rows),v(t,"class","block w-full rounded-2xl outline-none shadow-app focus:shadow-md border-2 border-white bg-slate-50 transition-shadow focus:border-primary-500 resize-none p-3"),v(e,"class","relative")},m(f,c){B(f,e,c),m(e,t),Pe(t,r[0]),m(e,l),_&&_.m(e,null),n||(o=Ie(t,"input",r[2]),n=!0)},p(f,[c]){c&2&&s!==(s=f[1].placeholder)&&v(t,"placeholder",s),c&2&&i!==(i=f[1].maxlength)&&v(t,"maxlength",i),c&2&&a!==(a=f[1].rows)&&v(t,"rows",a),c&1&&Pe(t,f[0]),f[1].maxlength?_?_.p(f,c):(_=Ye(f),_.c(),_.m(e,null)):_&&(_.d(1),_=null)},i:se,o:se,d(f){f&&p(e),_&&_.d(),n=!1,o()}}}function Nt(r,e,t){const s=["value"];let i=he(e,s),{value:a}=e;function l(){a=this.value,t(0,a)}return r.$$set=n=>{e=G(G({},e),ae(n)),t(1,i=he(e,s)),"value"in n&&t(0,a=n.value)},[a,i,l]}class Tt extends ce{constructor(e){super(),_e(this,e,Nt,Mt,fe,{value:0})}}function Ut(r){return r.length===0?"":r.map(e=>`importScripts("${e}");`).join(`
`)+`
`}function Vt(r,e=[]){const t=`
        ${Ut(e)}
        const workerFunction = ${r.toString()};
        self.onmessage = async (event) => {
            const result = await workerFunction(event.data);
            self.postMessage(result);
        };
    `;return s=>new Promise((i,a)=>{const l=new Blob([t],{type:"text/javascript"}),n=new Worker(URL.createObjectURL(l));n.onmessage=o=>i(o.data),n.onerror=a,n.postMessage(s)})}const jt=300;var K=(r=>(r.RentalTimerNotStopping="rental_timer_not_stopping",r.BatteryNotCharging="battery_not_charging",r.BatteryNotHoldingCharge="battery_not_holding_charge",r.StationUnavailable="station_unavailable",r.BatteryDamage="battery_damage",r.StationMalfunction="station_malfunction",r.BillingDiscrepancy="billing_discrepancy",r.AppTechnicalIssues="app_technical_issues",r.LostOrStolenBattery="lost_or_stolen_battery",r.FeedbackOrSuggestions="feedback_or_suggestions",r.Other="other",r))(K||{});function xe(r,e,t){const s=r.slice();return s[20]=e[t],s}function Rt(r){let e,t,s,i,a,l,n;function o(u){r[13](u)}let _={maxlength:jt,rows:3,placeholder:r[8]("report.describe_your_issue")};r[1]!==void 0&&(_.value=r[1]),t=new Tt({props:_}),me.push(()=>ke(t,"value",o));function f(u){r[14](u)}let c={class:"w-full bg-white active:bg-slate-100 text-black border",accept:"image/jpeg,image/png",preview:!0,multiple:!0,limit:3,disabled:r[3],$$slots:{default:[Ot]},$$scope:{ctx:r}};return r[6]!==void 0&&(c.value=r[6]),a=new At({props:c}),me.push(()=>ke(a,"value",f)),{c(){e=k("div"),Q(t.$$.fragment),i=S(),Q(a.$$.fragment),this.h()},l(u){e=E(u,"DIV",{class:!0});var d=$(e);Y(t.$$.fragment,d),d.forEach(p),i=I(u),Y(a.$$.fragment,u),this.h()},h(){v(e,"class","mb-6")},m(u,d){B(u,e,d),x(t,e,null),B(u,i,d),x(a,u,d),n=!0},p(u,d){const h={};d&256&&(h.placeholder=u[8]("report.describe_your_issue")),!s&&d&2&&(s=!0,h.value=u[1],be(()=>s=!1)),t.$set(h);const b={};d&8&&(b.disabled=u[3]),d&8388864&&(b.$$scope={dirty:d,ctx:u}),!l&&d&64&&(l=!0,b.value=u[6],be(()=>l=!1)),a.$set(b)},i(u){n||(L(t.$$.fragment,u),L(a.$$.fragment,u),n=!0)},o(u){C(t.$$.fragment,u),C(a.$$.fragment,u),n=!1},d(u){u&&(p(e),p(i)),ee(t),ee(a,u)}}}function Ht(r){let e,t=r[8]("report.what_kind_of_issue")+"",s,i,a,l,n=Se(r[9]),o=[];for(let f=0;f<n.length;f+=1)o[f]=et(xe(r,n,f));const _=f=>C(o[f],1,1,()=>{o[f]=null});return{c(){e=k("p"),s=V(t),i=S(),a=k("ul");for(let f=0;f<o.length;f+=1)o[f].c();this.h()},l(f){e=E(f,"P",{class:!0});var c=$(e);s=j(c,t),c.forEach(p),i=I(f),a=E(f,"UL",{class:!0});var u=$(a);for(let d=0;d<o.length;d+=1)o[d].l(u);u.forEach(p),this.h()},h(){v(e,"class","opacity-50 mb-3"),v(a,"class","grid grid-cols-1 gap-3")},m(f,c){B(f,e,c),m(e,s),B(f,i,c),B(f,a,c);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(a,null);l=!0},p(f,c){if((!l||c&256)&&t!==(t=f[8]("report.what_kind_of_issue")+"")&&R(s,t),c&513){n=Se(f[9]);let u;for(u=0;u<n.length;u+=1){const d=xe(f,n,u);o[u]?(o[u].p(d,c),L(o[u],1)):(o[u]=et(d),o[u].c(),L(o[u],1),o[u].m(a,null))}for(ve(),u=n.length;u<o.length;u+=1)_(u);we()}},i(f){if(!l){for(let c=0;c<n.length;c+=1)L(o[c]);l=!0}},o(f){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)C(o[c]);l=!1},d(f){f&&(p(e),p(i),p(a)),at(o,f)}}}function Ot(r){let e,t,s,i=r[8]("report.attach_images")+"",a,l;return e=new vt({}),{c(){Q(e.$$.fragment),t=S(),s=k("span"),a=V(i)},l(n){Y(e.$$.fragment,n),t=I(n),s=E(n,"SPAN",{});var o=$(s);a=j(o,i),o.forEach(p)},m(n,o){x(e,n,o),B(n,t,o),B(n,s,o),m(s,a),l=!0},p(n,o){(!l||o&256)&&i!==(i=n[8]("report.attach_images")+"")&&R(a,i)},i(n){l||(L(e.$$.fragment,n),l=!0)},o(n){C(e.$$.fragment,n),l=!1},d(n){n&&(p(t),p(s)),ee(e,n)}}}function Ft(r){let e,t=r[20].title+"",s,i,a,l=r[20].description+"",n;return{c(){e=k("p"),s=V(t),i=S(),a=k("p"),n=V(l),this.h()},l(o){e=E(o,"P",{class:!0});var _=$(e);s=j(_,t),_.forEach(p),i=I(o),a=E(o,"P",{class:!0});var f=$(a);n=j(f,l),f.forEach(p),this.h()},h(){v(e,"class","font-medium mb-1"),v(a,"class","text-sm opacity-80 leading-snug")},m(o,_){B(o,e,_),m(e,s),B(o,i,_),B(o,a,_),m(a,n)},p:se,d(o){o&&(p(e),p(i),p(a))}}}function et(r){let e,t,s,i,a;function l(o){r[12](o)}let n={id:r[20].type,name:"report",$$slots:{default:[Ft]},$$scope:{ctx:r}};return r[0]!==void 0&&(n.value=r[0]),t=new Ct({props:n}),me.push(()=>ke(t,"value",l)),{c(){e=k("li"),Q(t.$$.fragment),i=S()},l(o){e=E(o,"LI",{});var _=$(e);Y(t.$$.fragment,_),i=I(_),_.forEach(p)},m(o,_){B(o,e,_),x(t,e,null),m(e,i),a=!0},p(o,_){const f={};_&8388608&&(f.$$scope={dirty:_,ctx:o}),!s&&_&1&&(s=!0,f.value=o[0],be(()=>s=!1)),t.$set(f)},i(o){a||(L(t.$$.fragment,o),a=!0)},o(o){C(t.$$.fragment,o),a=!1},d(o){o&&p(e),ee(t)}}}function Gt(r){let e,t;return e=new Ae({props:{disabled:!r[7]||r[3],loading:r[3],class:"w-full",$$slots:{default:[zt]},$$scope:{ctx:r}}}),e.$on("click",r[10]),{c(){Q(e.$$.fragment)},l(s){Y(e.$$.fragment,s)},m(s,i){x(e,s,i),t=!0},p(s,i){const a={};i&136&&(a.disabled=!s[7]||s[3]),i&8&&(a.loading=s[3]),i&8388864&&(a.$$scope={dirty:i,ctx:s}),e.$set(a)},i(s){t||(L(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){ee(e,s)}}}function Wt(r){let e,t;return e=new Ae({props:{disabled:!r[0],class:"w-full",$$slots:{default:[Jt]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){Q(e.$$.fragment)},l(s){Y(e.$$.fragment,s)},m(s,i){x(e,s,i),t=!0},p(s,i){const a={};i&1&&(a.disabled=!s[0]),i&8388864&&(a.$$scope={dirty:i,ctx:s}),e.$set(a)},i(s){t||(L(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){ee(e,s)}}}function zt(r){let e,t=r[8]("report.submit")+"",s;return{c(){e=k("span"),s=V(t),this.h()},l(i){e=E(i,"SPAN",{class:!0});var a=$(e);s=j(a,t),a.forEach(p),this.h()},h(){v(e,"class","font-theme uppercase text-3xl")},m(i,a){B(i,e,a),m(e,s)},p(i,a){a&256&&t!==(t=i[8]("report.submit")+"")&&R(s,t)},d(i){i&&p(e)}}}function Jt(r){let e,t=r[8]("report.next")+"",s;return{c(){e=k("span"),s=V(t),this.h()},l(i){e=E(i,"SPAN",{class:!0});var a=$(e);s=j(a,t),a.forEach(p),this.h()},h(){v(e,"class","font-theme uppercase text-3xl")},m(i,a){B(i,e,a),m(e,s)},p(i,a){a&256&&t!==(t=i[8]("report.next")+"")&&R(s,t)},d(i){i&&p(e)}}}function Xt(r){let e=r[8]("report.dismiss")+"",t;return{c(){t=V(e)},l(s){t=j(s,e)},m(s,i){B(s,t,i)},p(s,i){i&256&&e!==(e=s[8]("report.dismiss")+"")&&R(t,e)},d(s){s&&p(t)}}}function Zt(r){let e,t,s=r[8]("report.report_submitted")+"",i,a,l,n=r[8]("report.thank_you_for_reporting")+"",o,_,f,c=r[8]("report.what_next")+"",u,d,h,b,M=r[8]("report.issue_resolution_1")+"",D,g,y,T=r[8]("report.issue_resolution_2")+"",U,W,P,H=r[8]("report.need_assistance")+"",z,J,O,te,le=r[8]("report.contact_support")+"",w,q,F,X=r[8]("report.support_email")+"",re,oe,ne,Ue="<EMAIL>",Ee,ue,ie,Z;return ie=new Ae({props:{class:"bg-white active:bg-slate-100 text-black w-full",$$slots:{default:[Xt]},$$scope:{ctx:r}}}),ie.$on("click",r[11]),{c(){e=k("div"),t=k("h2"),i=V(s),a=S(),l=k("p"),o=V(n),_=S(),f=k("p"),u=V(c),d=S(),h=k("ul"),b=k("li"),D=V(M),g=S(),y=k("li"),U=V(T),W=S(),P=k("p"),z=V(H),J=S(),O=k("div"),te=k("p"),w=V(le),q=S(),F=k("p"),re=V(X),oe=S(),ne=k("a"),ne.textContent=Ue,Ee=S(),ue=k("div"),Q(ie.$$.fragment),this.h()},l(N){e=E(N,"DIV",{class:!0});var A=$(e);t=E(A,"H2",{class:!0});var ge=$(t);i=j(ge,s),ge.forEach(p),a=I(A),l=E(A,"P",{});var Ve=$(l);o=j(Ve,n),Ve.forEach(p),_=I(A),f=E(A,"P",{class:!0});var je=$(f);u=j(je,c),je.forEach(p),d=I(A),h=E(A,"UL",{class:!0});var $e=$(h);b=E($e,"LI",{});var Re=$(b);D=j(Re,M),Re.forEach(p),g=I($e),y=E($e,"LI",{});var He=$(y);U=j(He,T),He.forEach(p),$e.forEach(p),W=I(A),P=E(A,"P",{class:!0});var Oe=$(P);z=j(Oe,H),Oe.forEach(p),J=I(A),O=E(A,"DIV",{});var ye=$(O);te=E(ye,"P",{class:!0});var Fe=$(te);w=j(Fe,le),Fe.forEach(p),q=I(ye),F=E(ye,"P",{});var Be=$(F);re=j(Be,X),oe=I(Be),ne=E(Be,"A",{href:!0,class:!0,"data-svelte-h":!0}),nt(ne)!=="svelte-1xl314l"&&(ne.textContent=Ue),Be.forEach(p),ye.forEach(p),A.forEach(p),Ee=I(N),ue=E(N,"DIV",{class:!0});var Ge=$(ue);Y(ie.$$.fragment,Ge),Ge.forEach(p),this.h()},h(){v(t,"class","font-bold text-center text-3xl leading-snug mb-6"),v(f,"class","text-2xl leading-snug font-bold mb-1 mt-10"),v(h,"class","grid grid-cols-1 gap-1 list-disc ml-6"),v(P,"class","text-2xl leading-snug font-bold mb-1 mt-10"),v(te,"class","mb-1"),v(ne,"href","mailto:<EMAIL>"),v(ne,"class","underline"),v(e,"class","text-lg text-white px-3 py-6"),v(ue,"class","sticky bottom-0 bg-primary-500 mt-auto px-3 py-6")},m(N,A){B(N,e,A),m(e,t),m(t,i),m(e,a),m(e,l),m(l,o),m(e,_),m(e,f),m(f,u),m(e,d),m(e,h),m(h,b),m(b,D),m(h,g),m(h,y),m(y,U),m(e,W),m(e,P),m(P,z),m(e,J),m(e,O),m(O,te),m(te,w),m(O,q),m(O,F),m(F,re),m(F,oe),m(F,ne),B(N,Ee,A),B(N,ue,A),x(ie,ue,null),Z=!0},p(N,A){(!Z||A&256)&&s!==(s=N[8]("report.report_submitted")+"")&&R(i,s),(!Z||A&256)&&n!==(n=N[8]("report.thank_you_for_reporting")+"")&&R(o,n),(!Z||A&256)&&c!==(c=N[8]("report.what_next")+"")&&R(u,c),(!Z||A&256)&&M!==(M=N[8]("report.issue_resolution_1")+"")&&R(D,M),(!Z||A&256)&&T!==(T=N[8]("report.issue_resolution_2")+"")&&R(U,T),(!Z||A&256)&&H!==(H=N[8]("report.need_assistance")+"")&&R(z,H),(!Z||A&256)&&le!==(le=N[8]("report.contact_support")+"")&&R(w,le),(!Z||A&256)&&X!==(X=N[8]("report.support_email")+"")&&R(re,X);const ge={};A&8388864&&(ge.$$scope={dirty:A,ctx:N}),ie.$set(ge)},i(N){Z||(L(ie.$$.fragment,N),Z=!0)},o(N){C(ie.$$.fragment,N),Z=!1},d(N){N&&(p(e),p(Ee),p(ue)),ee(ie)}}}function Kt(r){let e,t,s,i=r[8]("report.report_issue")+"",a,l,n,o,_,f,c,u,d,h,b,M,D,g,y;const T=[Ht,Rt],U=[];function W(w,q){return w[2]==="type"?0:w[2]==="description"?1:-1}~(n=W(r))&&(o=U[n]=T[n](r));const P=[Wt,Gt],H=[];function z(w,q){return w[2]==="type"?0:w[2]==="description"?1:-1}~(c=z(r))&&(u=H[c]=P[c](r));function J(w){r[16](w)}let O={};r[5]!==void 0&&(O.visible=r[5]),h=new ct({props:O}),me.push(()=>ke(h,"visible",J));function te(w){r[17](w)}let le={contentClass:"p-0",$$slots:{default:[Zt]},$$scope:{ctx:r}};return r[4]!==void 0&&(le.visible=r[4]),D=new _t({props:le}),me.push(()=>ke(D,"visible",te)),{c(){e=k("div"),t=k("div"),s=k("h1"),a=V(i),l=S(),o&&o.c(),_=S(),f=k("div"),u&&u.c(),d=S(),Q(h.$$.fragment),M=S(),Q(D.$$.fragment),this.h()},l(w){e=E(w,"DIV",{class:!0});var q=$(e);t=E(q,"DIV",{class:!0});var F=$(t);s=E(F,"H1",{class:!0});var X=$(s);a=j(X,i),X.forEach(p),l=I(F),o&&o.l(F),F.forEach(p),_=I(q),f=E(q,"DIV",{class:!0});var re=$(f);u&&u.l(re),re.forEach(p),q.forEach(p),d=I(w),Y(h.$$.fragment,w),M=I(w),Y(D.$$.fragment,w),this.h()},h(){v(s,"class","font-bold text-2xl mb-6"),v(t,"class","flex-1 px-3 py-6"),v(f,"class","sticky bottom-0 bg-white border-t mt-auto px-3 py-6"),v(e,"class","flex flex-col flex-1")},m(w,q){B(w,e,q),m(e,t),m(t,s),m(s,a),m(t,l),~n&&U[n].m(t,null),m(e,_),m(e,f),~c&&H[c].m(f,null),B(w,d,q),x(h,w,q),B(w,M,q),x(D,w,q),y=!0},p(w,[q]){(!y||q&256)&&i!==(i=w[8]("report.report_issue")+"")&&R(a,i);let F=n;n=W(w),n===F?~n&&U[n].p(w,q):(o&&(ve(),C(U[F],1,1,()=>{U[F]=null}),we()),~n?(o=U[n],o?o.p(w,q):(o=U[n]=T[n](w),o.c()),L(o,1),o.m(t,null)):o=null);let X=c;c=z(w),c===X?~c&&H[c].p(w,q):(u&&(ve(),C(H[X],1,1,()=>{H[X]=null}),we()),~c?(u=H[c],u?u.p(w,q):(u=H[c]=P[c](w),u.c()),L(u,1),u.m(f,null)):u=null);const re={};!b&&q&32&&(b=!0,re.visible=w[5],be(()=>b=!1)),h.$set(re);const oe={};q&8388864&&(oe.$$scope={dirty:q,ctx:w}),!g&&q&16&&(g=!0,oe.visible=w[4],be(()=>g=!1)),D.$set(oe)},i(w){y||(L(o),L(u),L(h.$$.fragment,w),L(D.$$.fragment,w),y=!0)},o(w){C(o),C(u),C(h.$$.fragment,w),C(D.$$.fragment,w),y=!1},d(w){w&&(p(e),p(d),p(M)),~n&&U[n].d(),~c&&H[c].d(),ee(h,w),ee(D,w)}}}function Qt(r,e,t){let s,i,a,l;De(r,mt,P=>t(18,i=P)),De(r,ft,P=>t(19,a=P)),De(r,ot,P=>t(8,l=P));let n="type",o="",_=!1,f=!1,c=!1;const u=[{type:K.BatteryNotCharging,title:l("report.battery_not_charging.title"),description:l("report.battery_not_charging.description")},{type:K.BatteryNotHoldingCharge,title:l("report.battery_not_holding_charge.title"),description:l("report.battery_not_holding_charge.description")},{type:K.RentalTimerNotStopping,title:l("report.rental_timer_not_stopping.title"),description:l("report.rental_timer_not_stopping.description")},{type:K.StationUnavailable,title:l("report.station_unavailable.title"),description:l("report.station_unavailable.description")},{type:K.BatteryDamage,title:l("report.battery_damage.title"),description:l("report.battery_damage.description")},{type:K.StationMalfunction,title:l("report.station_malfunction.title"),description:l("report.station_malfunction.description")},{type:K.BillingDiscrepancy,title:l("report.billing_discrepancy.title"),description:l("report.billing_discrepancy.description")},{type:K.AppTechnicalIssues,title:l("report.app_technical_issues.title"),description:l("report.app_technical_issues.description")},{type:K.LostOrStolenBattery,title:l("report.lost_or_stolen_battery.title"),description:l("report.lost_or_stolen_battery.description")},{type:K.FeedbackOrSuggestions,title:l("report.feedback_or_suggestions.title"),description:l("report.feedback_or_suggestions.description")},{type:K.Other,title:l("report.other.title"),description:l("report.other.description")}];let d="",h=[];dt({backUrl:a.state.returnTo??Le.Settings});const b=async()=>{if(!(!s||_))try{t(3,_=!0);const P=new FormData;P.append("type",o),P.append("description",d);const H=Vt(async z=>{const{Jimp:J}=self,O=await J.read(Buffer.from(await z.arrayBuffer())),te=1280,le=O.getWidth()>te?te:O.getWidth();O.resize(le,J.AUTO),O.quality(50);const w=await O.getBufferAsync(J.MIME_JPEG);return new File([w],z.name,{type:J.MIME_JPEG})},["https://cdn.jsdelivr.net/npm/jimp@0.22.12/browser/lib/jimp.min.js"]);for(const z of h){const J=await H(z);P.append("file",J,z.name)}await ht.post("/issues",P,{headers:{"Content-Type":"multipart/form-data"}}),t(4,f=!0)}catch(P){console.error(P),t(5,c=!0)}finally{t(3,_=!1)}},M=async()=>{t(4,f=!1),await ze(Le.Home)};tt(()=>{i!=null&&i.email||ze(Le.ReportEmail)});function D(P){o=P,t(0,o)}function g(P){d=P,t(1,d)}function y(P){h=P,t(6,h)}const T=()=>t(2,n="description");function U(P){c=P,t(5,c)}function W(P){f=P,t(4,f)}return r.$$.update=()=>{r.$$.dirty&3&&t(7,s=o.length>0&&(o!=="other"||d.trim().length>0))},[o,d,n,_,f,c,h,s,l,u,b,M,D,g,y,T,U,W]}class fl extends ce{constructor(e){super(),_e(this,e,Qt,Kt,fe,{})}}export{fl as component};
