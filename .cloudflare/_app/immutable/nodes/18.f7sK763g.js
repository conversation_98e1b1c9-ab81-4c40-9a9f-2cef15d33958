import{s as K,B as H,J as ee,K as te,L as ae,a as D,M as ie,d as E,N as j,i as x,r as B,C as A,o as Z,w as se,e as R,l as F,t as U,c as b,G as ne,m as P,g as N,b as C,h as w,E as X,j as Q,f as q}from"../chunks/index.UaHqEmIZ.js";import{S as Y,i as J,_ as re,t as L,b as T,f as oe,c as le,a as he,m as ce,d as de,g as ue}from"../chunks/index.RK-K-o1D.js";import{g as ge,c as z,t as me,R as ve}from"../chunks/style.HZSn-yMG.js";import{g as fe}from"../chunks/entry.vPeIVMYj.js";import{u as _e}from"../chunks/useTopBar._QombAUQ.js";(function(){try{var g=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(g._sentryDebugIds=g._sentryDebugIds||{},g._sentryDebugIds[e]="9f13f6ba-9fc3-44bd-ac65-e389c2fd8821",g._sentryDebugIdIdentifier="sentry-dbid-9f13f6ba-9fc3-44bd-ac65-e389c2fd8821")}catch{}})();function pe(g){let e,t,a='<path fill="currentColor" d="M184 16H72a16 16 0 0 0-16 16v45.33a16.12 16.12 0 0 0 3.2 9.6L80 114.67V224a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16V114.67l20.8-27.74a16.12 16.12 0 0 0 3.2-9.6V32a16 16 0 0 0-16-16M72 32h112v24H72zm91.2 73.07a16.12 16.12 0 0 0-3.2 9.6V224H96V114.67a16.12 16.12 0 0 0-3.2-9.6L72 77.33V72h112v5.33ZM136 120v32a8 8 0 0 1-16 0v-32a8 8 0 0 1 16 0"/>',i=[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},g[0]],s={};for(let n=0;n<i.length;n+=1)s=H(s,i[n]);return{c(){e=ee("svg"),t=new te(!0),this.h()},l(n){e=ae(n,"svg",{viewBox:!0,width:!0,height:!0});var h=D(e);t=ie(h,!0),h.forEach(E),this.h()},h(){t.a=null,j(e,s)},m(n,h){x(n,e,h),t.m(a,e)},p(n,[h]){j(e,s=ge(i,[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},h&1&&n[0]]))},i:B,o:B,d(n){n&&E(e)}}}function ye(g,e,t){return g.$$set=a=>{t(0,e=H(H({},e),A(a)))},e=A(e),[e]}class we extends Y{constructor(e){super(),J(this,e,ye,pe,K,{})}}class c{constructor(e,t,a,i,s){this._legacyCanvasSize=c.DEFAULT_CANVAS_SIZE,this._preferredCamera="environment",this._maxScansPerSecond=25,this._lastScanTimestamp=-1,this._destroyed=this._flashOn=this._paused=this._active=!1,this.$video=e,this.$canvas=document.createElement("canvas"),a&&typeof a=="object"?this._onDecode=t:(console.warn(a||i||s?"You're using a deprecated version of the QrScanner constructor which will be removed in the future":"Note that the type of the scan result passed to onDecode will change in the future. To already switch to the new api today, you can pass returnDetailedScanResult: true."),this._legacyOnDecode=t),t=typeof a=="object"?a:{},this._onDecodeError=t.onDecodeError||(typeof a=="function"?a:this._onDecodeError),this._calculateScanRegion=t.calculateScanRegion||(typeof i=="function"?i:this._calculateScanRegion),this._preferredCamera=t.preferredCamera||s||this._preferredCamera,this._legacyCanvasSize=typeof a=="number"?a:typeof i=="number"?i:this._legacyCanvasSize,this._maxScansPerSecond=t.maxScansPerSecond||this._maxScansPerSecond,this._onPlay=this._onPlay.bind(this),this._onLoadedMetaData=this._onLoadedMetaData.bind(this),this._onVisibilityChange=this._onVisibilityChange.bind(this),this._updateOverlay=this._updateOverlay.bind(this),e.disablePictureInPicture=!0,e.playsInline=!0,e.muted=!0;let n=!1;if(e.hidden&&(e.hidden=!1,n=!0),document.body.contains(e)||(document.body.appendChild(e),n=!0),a=e.parentElement,t.highlightScanRegion||t.highlightCodeOutline){if(i=!!t.overlay,this.$overlay=t.overlay||document.createElement("div"),s=this.$overlay.style,s.position="absolute",s.display="none",s.pointerEvents="none",this.$overlay.classList.add("scan-region-highlight"),!i&&t.highlightScanRegion){this.$overlay.innerHTML='<svg class="scan-region-highlight-svg" viewBox="0 0 238 238" preserveAspectRatio="none" style="position:absolute;width:100%;height:100%;left:0;top:0;fill:none;stroke:#e9b213;stroke-width:4;stroke-linecap:round;stroke-linejoin:round"><path d="M31 2H10a8 8 0 0 0-8 8v21M207 2h21a8 8 0 0 1 8 8v21m0 176v21a8 8 0 0 1-8 8h-21m-176 0H10a8 8 0 0 1-8-8v-21"/></svg>';try{this.$overlay.firstElementChild.animate({transform:["scale(.98)","scale(1.01)"]},{duration:400,iterations:1/0,direction:"alternate",easing:"ease-in-out"})}catch{}a.insertBefore(this.$overlay,this.$video.nextSibling)}t.highlightCodeOutline&&(this.$overlay.insertAdjacentHTML("beforeend",'<svg class="code-outline-highlight" preserveAspectRatio="none" style="display:none;width:100%;height:100%;fill:none;stroke:#e9b213;stroke-width:5;stroke-dasharray:25;stroke-linecap:round;stroke-linejoin:round"><polygon/></svg>'),this.$codeOutlineHighlight=this.$overlay.lastElementChild)}this._scanRegion=this._calculateScanRegion(e),requestAnimationFrame(()=>{let h=window.getComputedStyle(e);h.display==="none"&&(e.style.setProperty("display","block","important"),n=!0),h.visibility!=="visible"&&(e.style.setProperty("visibility","visible","important"),n=!0),n&&(console.warn("QrScanner has overwritten the video hiding style to avoid Safari stopping the playback."),e.style.opacity="0",e.style.width="0",e.style.height="0",this.$overlay&&this.$overlay.parentElement&&this.$overlay.parentElement.removeChild(this.$overlay),delete this.$overlay,delete this.$codeOutlineHighlight),this.$overlay&&this._updateOverlay()}),e.addEventListener("play",this._onPlay),e.addEventListener("loadedmetadata",this._onLoadedMetaData),document.addEventListener("visibilitychange",this._onVisibilityChange),window.addEventListener("resize",this._updateOverlay),this._qrEnginePromise=c.createQrEngine()}static set WORKER_PATH(e){console.warn("Setting QrScanner.WORKER_PATH is not required and not supported anymore. Have a look at the README for new setup instructions.")}static async hasCamera(){try{return!!(await c.listCameras(!1)).length}catch{return!1}}static async listCameras(e=!1){if(!navigator.mediaDevices)return[];let t=async()=>(await navigator.mediaDevices.enumerateDevices()).filter(i=>i.kind==="videoinput"),a;try{e&&(await t()).every(i=>!i.label)&&(a=await navigator.mediaDevices.getUserMedia({audio:!1,video:!0}))}catch{}try{return(await t()).map((i,s)=>({id:i.deviceId,label:i.label||(s===0?"Default Camera":`Camera ${s+1}`)}))}finally{a&&(console.warn("Call listCameras after successfully starting a QR scanner to avoid creating a temporary video stream"),c._stopVideoStream(a))}}async hasFlash(){let e;try{if(this.$video.srcObject){if(!(this.$video.srcObject instanceof MediaStream))return!1;e=this.$video.srcObject}else e=(await this._getCameraStream()).stream;return"torch"in e.getVideoTracks()[0].getSettings()}catch{return!1}finally{e&&e!==this.$video.srcObject&&(console.warn("Call hasFlash after successfully starting the scanner to avoid creating a temporary video stream"),c._stopVideoStream(e))}}isFlashOn(){return this._flashOn}async toggleFlash(){this._flashOn?await this.turnFlashOff():await this.turnFlashOn()}async turnFlashOn(){if(!this._flashOn&&!this._destroyed&&(this._flashOn=!0,this._active&&!this._paused))try{if(!await this.hasFlash())throw"No flash available";await this.$video.srcObject.getVideoTracks()[0].applyConstraints({advanced:[{torch:!0}]})}catch(e){throw this._flashOn=!1,e}}async turnFlashOff(){this._flashOn&&(this._flashOn=!1,await this._restartVideoStream())}destroy(){this.$video.removeEventListener("loadedmetadata",this._onLoadedMetaData),this.$video.removeEventListener("play",this._onPlay),document.removeEventListener("visibilitychange",this._onVisibilityChange),window.removeEventListener("resize",this._updateOverlay),this._destroyed=!0,this._flashOn=!1,this.stop(),c._postWorkerMessage(this._qrEnginePromise,"close")}async start(){if(this._destroyed)throw Error("The QR scanner can not be started as it had been destroyed.");if((!this._active||this._paused)&&(window.location.protocol!=="https:"&&console.warn("The camera stream is only accessible if the page is transferred via https."),this._active=!0,!document.hidden))if(this._paused=!1,this.$video.srcObject)await this.$video.play();else try{let{stream:e,facingMode:t}=await this._getCameraStream();!this._active||this._paused?c._stopVideoStream(e):(this._setVideoMirror(t),this.$video.srcObject=e,await this.$video.play(),this._flashOn&&(this._flashOn=!1,this.turnFlashOn().catch(()=>{})))}catch(e){if(!this._paused)throw this._active=!1,e}}stop(){this.pause(),this._active=!1}async pause(e=!1){if(this._paused=!0,!this._active)return!0;this.$video.pause(),this.$overlay&&(this.$overlay.style.display="none");let t=()=>{this.$video.srcObject instanceof MediaStream&&(c._stopVideoStream(this.$video.srcObject),this.$video.srcObject=null)};return e?(t(),!0):(await new Promise(a=>setTimeout(a,300)),this._paused?(t(),!0):!1)}async setCamera(e){e!==this._preferredCamera&&(this._preferredCamera=e,await this._restartVideoStream())}static async scanImage(e,t,a,i,s=!1,n=!1){let h,r=!1;t&&("scanRegion"in t||"qrEngine"in t||"canvas"in t||"disallowCanvasResizing"in t||"alsoTryWithoutScanRegion"in t||"returnDetailedScanResult"in t)?(h=t.scanRegion,a=t.qrEngine,i=t.canvas,s=t.disallowCanvasResizing||!1,n=t.alsoTryWithoutScanRegion||!1,r=!0):console.warn(t||a||i||s||n?"You're using a deprecated api for scanImage which will be removed in the future.":"Note that the return type of scanImage will change in the future. To already switch to the new api today, you can pass returnDetailedScanResult: true."),t=!!a;try{let o,d;[a,o]=await Promise.all([a||c.createQrEngine(),c._loadImage(e)]),[i,d]=c._drawToCanvas(o,h,i,s);let v;if(a instanceof Worker){let l=a;t||c._postWorkerMessageSync(l,"inversionMode","both"),v=await new Promise((m,S)=>{let O,f,y,M=-1;f=u=>{u.data.id===M&&(l.removeEventListener("message",f),l.removeEventListener("error",y),clearTimeout(O),u.data.data!==null?m({data:u.data.data,cornerPoints:c._convertPoints(u.data.cornerPoints,h)}):S(c.NO_QR_CODE_FOUND))},y=u=>{l.removeEventListener("message",f),l.removeEventListener("error",y),clearTimeout(O),S("Scanner error: "+(u?u.message||u:"Unknown Error"))},l.addEventListener("message",f),l.addEventListener("error",y),O=setTimeout(()=>y("timeout"),1e4);let $=d.getImageData(0,0,i.width,i.height);M=c._postWorkerMessageSync(l,"decode",$,[$.data.buffer])})}else v=await Promise.race([new Promise((l,m)=>window.setTimeout(()=>m("Scanner error: timeout"),1e4)),(async()=>{try{var[l]=await a.detect(i);if(!l)throw c.NO_QR_CODE_FOUND;return{data:l.rawValue,cornerPoints:c._convertPoints(l.cornerPoints,h)}}catch(m){if(l=m.message||m,/not implemented|service unavailable/.test(l))return c._disableBarcodeDetector=!0,c.scanImage(e,{scanRegion:h,canvas:i,disallowCanvasResizing:s,alsoTryWithoutScanRegion:n});throw`Scanner error: ${l}`}})()]);return r?v:v.data}catch(o){if(!h||!n)throw o;let d=await c.scanImage(e,{qrEngine:a,canvas:i,disallowCanvasResizing:s});return r?d:d.data}finally{t||c._postWorkerMessage(a,"close")}}setGrayscaleWeights(e,t,a,i=!0){c._postWorkerMessage(this._qrEnginePromise,"grayscaleWeights",{red:e,green:t,blue:a,useIntegerApproximation:i})}setInversionMode(e){c._postWorkerMessage(this._qrEnginePromise,"inversionMode",e)}static async createQrEngine(e){if(e&&console.warn("Specifying a worker path is not required and not supported anymore."),e=()=>re(()=>import("../chunks/qr-scanner-worker.min.-ZCqdKJ_.js"),__vite__mapDeps([]),import.meta.url).then(a=>a.createWorker()),!(!c._disableBarcodeDetector&&"BarcodeDetector"in window&&BarcodeDetector.getSupportedFormats&&(await BarcodeDetector.getSupportedFormats()).includes("qr_code")))return e();let t=navigator.userAgentData;return t&&t.brands.some(({brand:a})=>/Chromium/i.test(a))&&/mac ?OS/i.test(t.platform)&&await t.getHighEntropyValues(["architecture","platformVersion"]).then(({architecture:a,platformVersion:i})=>/arm/i.test(a||"arm")&&13<=parseInt(i||"13")).catch(()=>!0)?e():new BarcodeDetector({formats:["qr_code"]})}_onPlay(){this._scanRegion=this._calculateScanRegion(this.$video),this._updateOverlay(),this.$overlay&&(this.$overlay.style.display=""),this._scanFrame()}_onLoadedMetaData(){this._scanRegion=this._calculateScanRegion(this.$video),this._updateOverlay()}_onVisibilityChange(){document.hidden?this.pause():this._active&&this.start()}_calculateScanRegion(e){let t=Math.round(.6666666666666666*Math.min(e.videoWidth,e.videoHeight));return{x:Math.round((e.videoWidth-t)/2),y:Math.round((e.videoHeight-t)/2),width:t,height:t,downScaledWidth:this._legacyCanvasSize,downScaledHeight:this._legacyCanvasSize}}_updateOverlay(){requestAnimationFrame(()=>{if(this.$overlay){var e=this.$video,t=e.videoWidth,a=e.videoHeight,i=e.offsetWidth,s=e.offsetHeight,n=e.offsetLeft,h=e.offsetTop,r=window.getComputedStyle(e),o=r.objectFit,d=t/a,v=i/s;switch(o){case"none":var l=t,m=a;break;case"fill":l=i,m=s;break;default:(o==="cover"?d>v:d<v)?(m=s,l=m*d):(l=i,m=l/d),o==="scale-down"&&(l=Math.min(l,t),m=Math.min(m,a))}var[S,O]=r.objectPosition.split(" ").map((y,M)=>{const $=parseFloat(y);return y.endsWith("%")?(M?s-m:i-l)*$/100:$});r=this._scanRegion.width||t,v=this._scanRegion.height||a,o=this._scanRegion.x||0;var f=this._scanRegion.y||0;d=this.$overlay.style,d.width=`${r/t*l}px`,d.height=`${v/a*m}px`,d.top=`${h+O+f/a*m}px`,a=/scaleX\(-1\)/.test(e.style.transform),d.left=`${n+(a?i-S-l:S)+(a?t-o-r:o)/t*l}px`,d.transform=e.style.transform}})}static _convertPoints(e,t){if(!t)return e;let a=t.x||0,i=t.y||0,s=t.width&&t.downScaledWidth?t.width/t.downScaledWidth:1;t=t.height&&t.downScaledHeight?t.height/t.downScaledHeight:1;for(let n of e)n.x=n.x*s+a,n.y=n.y*t+i;return e}_scanFrame(){!this._active||this.$video.paused||this.$video.ended||("requestVideoFrameCallback"in this.$video?this.$video.requestVideoFrameCallback.bind(this.$video):requestAnimationFrame)(async()=>{if(!(1>=this.$video.readyState)){var e=Date.now()-this._lastScanTimestamp,t=1e3/this._maxScansPerSecond;e<t&&await new Promise(i=>setTimeout(i,t-e)),this._lastScanTimestamp=Date.now();try{var a=await c.scanImage(this.$video,{scanRegion:this._scanRegion,qrEngine:this._qrEnginePromise,canvas:this.$canvas})}catch(i){if(!this._active)return;this._onDecodeError(i)}!c._disableBarcodeDetector||await this._qrEnginePromise instanceof Worker||(this._qrEnginePromise=c.createQrEngine()),a?(this._onDecode?this._onDecode(a):this._legacyOnDecode&&this._legacyOnDecode(a.data),this.$codeOutlineHighlight&&(clearTimeout(this._codeOutlineHighlightRemovalTimeout),this._codeOutlineHighlightRemovalTimeout=void 0,this.$codeOutlineHighlight.setAttribute("viewBox",`${this._scanRegion.x||0} ${this._scanRegion.y||0} ${this._scanRegion.width||this.$video.videoWidth} ${this._scanRegion.height||this.$video.videoHeight}`),this.$codeOutlineHighlight.firstElementChild.setAttribute("points",a.cornerPoints.map(({x:i,y:s})=>`${i},${s}`).join(" ")),this.$codeOutlineHighlight.style.display="")):this.$codeOutlineHighlight&&!this._codeOutlineHighlightRemovalTimeout&&(this._codeOutlineHighlightRemovalTimeout=setTimeout(()=>this.$codeOutlineHighlight.style.display="none",100))}this._scanFrame()})}_onDecodeError(e){e!==c.NO_QR_CODE_FOUND&&console.log(e)}async _getCameraStream(){if(!navigator.mediaDevices)throw"Camera not found.";let e=/^(environment|user)$/.test(this._preferredCamera)?"facingMode":"deviceId",t=[{width:{min:1024}},{width:{min:768}},{}],a=t.map(i=>Object.assign({},i,{[e]:{exact:this._preferredCamera}}));for(let i of[...a,...t])try{let s=await navigator.mediaDevices.getUserMedia({video:i,audio:!1}),n=this._getFacingMode(s)||(i.facingMode?this._preferredCamera:this._preferredCamera==="environment"?"user":"environment");return{stream:s,facingMode:n}}catch{}throw"Camera not found."}async _restartVideoStream(){let e=this._paused;await this.pause(!0)&&!e&&this._active&&await this.start()}static _stopVideoStream(e){for(let t of e.getTracks())t.stop(),e.removeTrack(t)}_setVideoMirror(e){this.$video.style.transform="scaleX("+(e==="user"?-1:1)+")"}_getFacingMode(e){return(e=e.getVideoTracks()[0])?/rear|back|environment/i.test(e.label)?"environment":/front|user|face/i.test(e.label)?"user":null:null}static _drawToCanvas(e,t,a,i=!1){a=a||document.createElement("canvas");let s=t&&t.x?t.x:0,n=t&&t.y?t.y:0,h=t&&t.width?t.width:e.videoWidth||e.width,r=t&&t.height?t.height:e.videoHeight||e.height;return i||(i=t&&t.downScaledWidth?t.downScaledWidth:h,t=t&&t.downScaledHeight?t.downScaledHeight:r,a.width!==i&&(a.width=i),a.height!==t&&(a.height=t)),t=a.getContext("2d",{alpha:!1}),t.imageSmoothingEnabled=!1,t.drawImage(e,s,n,h,r,0,0,a.width,a.height),[a,t]}static async _loadImage(e){if(e instanceof Image)return await c._awaitImageLoad(e),e;if(e instanceof HTMLVideoElement||e instanceof HTMLCanvasElement||e instanceof SVGImageElement||"OffscreenCanvas"in window&&e instanceof OffscreenCanvas||"ImageBitmap"in window&&e instanceof ImageBitmap)return e;if(e instanceof File||e instanceof Blob||e instanceof URL||typeof e=="string"){let t=new Image;t.src=e instanceof File||e instanceof Blob?URL.createObjectURL(e):e.toString();try{return await c._awaitImageLoad(t),t}finally{(e instanceof File||e instanceof Blob)&&URL.revokeObjectURL(t.src)}}else throw"Unsupported image type."}static async _awaitImageLoad(e){e.complete&&e.naturalWidth!==0||await new Promise((t,a)=>{let i=s=>{e.removeEventListener("load",i),e.removeEventListener("error",i),s instanceof ErrorEvent?a("Image load error"):t()};e.addEventListener("load",i),e.addEventListener("error",i)})}static async _postWorkerMessage(e,t,a,i){return c._postWorkerMessageSync(await e,t,a,i)}static _postWorkerMessageSync(e,t,a,i){if(!(e instanceof Worker))return-1;let s=c._workerMessageId++;return e.postMessage({id:s,type:t,data:a},i),s}}c.DEFAULT_CANVAS_SIZE=400;c.NO_QR_CODE_FOUND="No QR code found";c._disableBarcodeDetector=!1;c._workerMessageId=0;const Ee=(g,e)=>{let t=null;const a=se(!1),i=r=>{const o=new URL(r);return o.searchParams.get("flag")||o.searchParams.get("station_id")},s=r=>{const o=i(r);o&&e(o)},n=async()=>{await(t==null?void 0:t.start()),a.set(await(t==null?void 0:t.hasFlash())??!1)},h=async()=>{const r=document.getElementById(g);if(!r)return;const o=document.createElement("canvas"),d=o.getContext("2d");if(!d)throw new Error("Canvas context not found");const v=r.videoWidth/r.videoHeight;o.width=1e3,o.height=Math.round(o.width/v),d.drawImage(r,0,0,o.width,o.height);const l=await c.scanImage(o.toDataURL("image/jpeg"),{returnDetailedScanResult:!0});s(l.data)};return Z(()=>{const r=document.getElementById(g);if(!r)throw new Error(`Element with id ${g} not found`);t=new c(r,()=>null,{preferredCamera:"environment",highlightScanRegion:!0,highlightCodeOutline:!0}),n();const o=setInterval(()=>{h().catch(d=>console.error(d))},1e3);return()=>{t==null||t.destroy(),t=null,clearInterval(o)}}),{hasFlash:a,toggleFlash:()=>t==null?void 0:t.toggleFlash(),isFlashOn:()=>t==null?void 0:t.isFlashOn(),manualScan:h}};function G(g){let e,t,a,i,s,n,h;return a=new we({props:{width:"42",height:"42"}}),{c(){e=R("div"),t=R("button"),le(a.$$.fragment),this.h()},l(r){e=b(r,"DIV",{class:!0});var o=D(e);t=b(o,"BUTTON",{class:!0});var d=D(t);he(a.$$.fragment,d),d.forEach(E),o.forEach(E),this.h()},h(){t.disabled=g[1],C(t,"class",i=z("disabled:pointer-events-none disabled:opacity-50 rounded-full shadow-app p-3",{"bg-white active:bg-slate-100 text-black":g[0],"bg-transparent active:bg-white/10 text-white outline":!g[0]})),C(e,"class","flex justify-center mt-auto")},m(r,o){x(r,e,o),w(e,t),ce(a,t,null),s=!0,n||(h=X(t,"click",g[6]),n=!0)},p(r,o){(!s||o&2)&&(t.disabled=r[1]),(!s||o&1&&i!==(i=z("disabled:pointer-events-none disabled:opacity-50 rounded-full shadow-app p-3",{"bg-white active:bg-slate-100 text-black":r[0],"bg-transparent active:bg-white/10 text-white outline":!r[0]})))&&C(t,"class",i)},i(r){s||(L(a.$$.fragment,r),s=!0)},o(r){T(a.$$.fragment,r),s=!1},d(r){r&&E(e),de(a),n=!1,h()}}}function Se(g){let e,t,a,i='<track default="" kind="captions" srclang="en" src="null"/>',s,n,h,r,o=g[2]("station_scan.scan_qr_code")+"",d,v,l,m=g[2]("station_scan.place_in_the_center")+"",S,O,f,y,M,$=Oe,u=g[3]&&G(g);return{c(){e=R("div"),t=R("div"),a=R("video"),a.innerHTML=i,s=F(),n=F(),h=R("div"),r=R("p"),d=U(o),v=F(),l=R("p"),S=U(m),O=F(),u&&u.c(),this.h()},l(_){e=b(_,"DIV",{class:!0});var p=D(e);t=b(p,"DIV",{class:!0});var k=D(t);a=b(k,"VIDEO",{id:!0,class:!0,"data-svelte-h":!0}),ne(a)!=="svelte-1mu1ast"&&(a.innerHTML=i),s=P(k),k.forEach(E),n=P(p),h=b(p,"DIV",{class:!0});var I=D(h);r=b(I,"P",{class:!0});var V=D(r);d=N(V,o),V.forEach(E),v=P(I),l=b(I,"P",{});var W=D(l);S=N(W,m),W.forEach(E),I.forEach(E),O=P(p),u&&u.l(p),p.forEach(E),this.h()},h(){C(a,"id","reader"),C(a,"class","absolute top-0 left-0 w-full h-full object-cover"),C(t,"class","relative w-full rounded-3xl aspect-[3/4] sm:aspect-square bg-slate-950 overflow-hidden"),C(r,"class","text-2xl font-bold mb-1"),C(h,"class","text-center text-white my-6"),C(e,"class","bg-papaya flex flex-col flex-1 p-6")},m(_,p){x(_,e,p),w(e,t),w(t,a),w(t,s),w(e,n),w(e,h),w(h,r),w(r,d),w(h,v),w(h,l),w(l,S),w(e,O),u&&u.m(e,null),f=!0,y||(M=X(a,"click",g[5]),y=!0)},p(_,[p]){(!f||p&4)&&o!==(o=_[2]("station_scan.scan_qr_code")+"")&&Q(d,o),(!f||p&4)&&m!==(m=_[2]("station_scan.place_in_the_center")+"")&&Q(S,m),_[3]?u?(u.p(_,p),p&8&&L(u,1)):(u=G(_),u.c(),L(u,1),u.m(e,null)):u&&(ue(),T(u,1,1,()=>{u=null}),oe())},i(_){f||(L($),L(u),f=!0)},o(_){T($),T(u),f=!1},d(_){_&&E(e),u&&u.d(),y=!1,M()}}}let Oe=!1;function Ce(g,e,t){let a,i;q(g,me,l=>t(2,a=l));let s=!1,n=!1;_e({cssClass:"bg-papaya"});const{hasFlash:h,toggleFlash:r,isFlashOn:o,manualScan:d}=Ee("reader",l=>fe(`${ve.Home}?station_id=${l}`));q(g,h,l=>t(3,i=l));const v=async()=>{try{t(1,n=!0),await r(),t(0,s=o()??!1)}finally{t(1,n=!1)}};return Z(()=>{t(0,s=o()??!1)}),[s,n,a,i,h,d,v]}class Le extends Y{constructor(e){super(),J(this,e,Ce,Se,K,{})}}export{Le as component};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
