import{s as B,p as I,i as d,d as f,f as E,o as C,e as p,c as h,a as g,q as G,b as q,r as M,t as N,g as U,h as z,j as A}from"../chunks/index.UaHqEmIZ.js";import{S as H,i as J,b as _,f as P,t as u,g as j,c as $,a as v,m as y,d as k}from"../chunks/index.RK-K-o1D.js";import{e as D}from"../chunks/each.N0rfVI1r.js";import{S as K}from"../chunks/StationItem.SmkLFR3c.js";import{E as O}from"../chunks/EmptyPlaceholder.ts_EQhA2.js";import{L as Q}from"../chunks/Loader.0adt-66j.js";import{u as R}from"../chunks/useGeolocation.3AiEsf14.js";import{t as T}from"../chunks/style.HZSn-yMG.js";import{s as S}from"../chunks/supabase.xRgAeO37.js";import{u as W}from"../chunks/user.oAuVK7RJ.js";(function(){try{var i=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(i._sentryDebugIds=i._sentryDebugIds||{},i._sentryDebugIds[t]="3f8401bb-3d2e-48e5-944a-48ec4ec00c67",i._sentryDebugIdIdentifier="sentry-dbid-3f8401bb-3d2e-48e5-944a-48ec4ec00c67")}catch{}})();function F(i,t,r){const o=i.slice();return o[5]=t[r].station,o[6]=t[r].isFavorite,o}function X(i){let t,r,o=D(i[0]),e=[];for(let s=0;s<o.length;s+=1)e[s]=L(F(i,o,s));const a=s=>_(e[s],1,1,()=>{e[s]=null});return{c(){t=p("ul");for(let s=0;s<e.length;s+=1)e[s].c()},l(s){t=h(s,"UL",{});var l=g(t);for(let n=0;n<e.length;n+=1)e[n].l(l);l.forEach(f)},m(s,l){d(s,t,l);for(let n=0;n<e.length;n+=1)e[n]&&e[n].m(t,null);r=!0},p(s,l){if(l&1){o=D(s[0]);let n;for(n=0;n<o.length;n+=1){const c=F(s,o,n);e[n]?(e[n].p(c,l),u(e[n],1)):(e[n]=L(c),e[n].c(),u(e[n],1),e[n].m(t,null))}for(j(),n=o.length;n<e.length;n+=1)a(n);P()}},i(s){if(!r){for(let l=0;l<o.length;l+=1)u(e[l]);r=!0}},o(s){e=e.filter(Boolean);for(let l=0;l<e.length;l+=1)_(e[l]);r=!1},d(s){s&&f(t),G(e,s)}}}function Y(i){let t,r,o;return r=new O({props:{$$slots:{default:[ee]},$$scope:{ctx:i}}}),{c(){t=p("div"),$(r.$$.fragment),this.h()},l(e){t=h(e,"DIV",{class:!0});var a=g(t);v(r.$$.fragment,a),a.forEach(f),this.h()},h(){q(t,"class","mt-16")},m(e,a){d(e,t,a),y(r,t,null),o=!0},p(e,a){const s={};a&516&&(s.$$scope={dirty:a,ctx:e}),r.$set(s)},i(e){o||(u(r.$$.fragment,e),o=!0)},o(e){_(r.$$.fragment,e),o=!1},d(e){e&&f(t),k(r)}}}function Z(i){let t,r,o;return r=new Q({}),{c(){t=p("div"),$(r.$$.fragment),this.h()},l(e){t=h(e,"DIV",{class:!0});var a=g(t);v(r.$$.fragment,a),a.forEach(f),this.h()},h(){q(t,"class","flex justify-center mt-16")},m(e,a){d(e,t,a),y(r,t,null),o=!0},p:M,i(e){o||(u(r.$$.fragment,e),o=!0)},o(e){_(r.$$.fragment,e),o=!1},d(e){e&&f(t),k(r)}}}function L(i){let t,r;return t=new K({props:{station:i[5],isFavorite:i[6]}}),{c(){$(t.$$.fragment)},l(o){v(t.$$.fragment,o)},m(o,e){y(t,o,e),r=!0},p(o,e){const a={};e&1&&(a.station=o[5]),e&1&&(a.isFavorite=o[6]),t.$set(a)},i(o){r||(u(t.$$.fragment,o),r=!0)},o(o){_(t.$$.fragment,o),r=!1},d(o){k(t,o)}}}function ee(i){let t,r=i[2]("favorites.no_favorite")+"",o;return{c(){t=p("p"),o=N(r)},l(e){t=h(e,"P",{});var a=g(t);o=U(a,r),a.forEach(f)},m(e,a){d(e,t,a),z(t,o)},p(e,a){a&4&&r!==(r=e[2]("favorites.no_favorite")+"")&&A(o,r)},d(e){e&&f(t)}}}function te(i){let t,r,o,e;const a=[Z,Y,X],s=[];function l(n,c){return n[1]?0:n[0].length===0?1:2}return t=l(i),r=s[t]=a[t](i),{c(){r.c(),o=I()},l(n){r.l(n),o=I()},m(n,c){s[t].m(n,c),d(n,o,c),e=!0},p(n,[c]){let m=t;t=l(n),t===m?s[t].p(n,c):(j(),_(s[m],1,1,()=>{s[m]=null}),P(),r=s[t],r?r.p(n,c):(r=s[t]=a[t](n),r.c()),u(r,1),r.m(o.parentNode,o))},i(n){e||(u(r),e=!0)},o(n){_(r),e=!1},d(n){n&&f(o),s[t].d(n)}}}function re(i,t,r){let o,e;E(i,W,n=>r(3,o=n)),E(i,T,n=>r(2,e=n));let a=[],s=!0;R();const l=async()=>{try{if(!o)return;r(1,s=!0);const{data:n,error:c}=await S.from("favorite_stations").select("station_id").eq("user_id",o.id).order("created_at",{ascending:!1});if(c){console.error(c);return}const m=n.map(b=>b.station_id),{data:x,error:w}=await S.from("station_locations").select().in("station_id",m);if(w){console.error(w);return}r(0,a=m.map(b=>({station:x.find(V=>V.station_id===b),isFavorite:!0})))}catch(n){console.error(n)}finally{r(1,s=!1)}};return C(()=>{l()}),[a,s,e]}class me extends H{constructor(t){super(),J(this,t,re,te,B,{})}}export{me as component};
