import{o as p,w as i,u as s}from"./index.UaHqEmIZ.js";import{s as c}from"./supabase.xRgAeO37.js";import{u as f}from"./user.oAuVK7RJ.js";import{a as _,b as l,c as m}from"./OrderItem.caWmX4u5.js";import"./index.RK-K-o1D.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="ec7276c4-4a1d-45de-b47b-b92f2b5ea5cf",e._sentryDebugIdIdentifier="sentry-dbid-ec7276c4-4a1d-45de-b47b-b92f2b5ea5cf")}catch{}})();const o=i(null),b=i(null),d=i(!0),y=async()=>{const e=s(o);if(!e)return;const{error:r,data:t}=await c.from("station_locations").select().eq("station_id",e.station_id).maybeSingle();if(r){console.error(r);return}b.set(t)},g=async()=>{try{d.set(!0);const e=s(f);if(!e)return;const{error:r,data:t}=await c.from("orders").select().eq("user_id",e.id).eq("status",_.Ongoing).is("ended_at",null).maybeSingle();if(r||!t)return;o.set(t),await y()}finally{d.set(!1)}},w=()=>{const e=s(f);if(!e)return()=>{};const r=c.channel(e.id).subscribe(t=>{t==="SUBSCRIBED"&&r.on("broadcast",{event:l.BatteryPopped},async a=>{const{order:n}=a.payload;n.type===m.Rental&&(o.set(n),await y())}).on("broadcast",{event:l.BatteryReturned},a=>{const{order:n}=a.payload,u=s(o);u&&u.id===n.id&&o.set(null)})});return()=>{r.unsubscribe()}},S=()=>(p(()=>{const e=w();return g(),()=>{e()}}),{order:o,location:b,isLoading:d});export{o,S as u};
