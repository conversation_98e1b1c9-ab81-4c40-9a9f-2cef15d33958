var J=Object.defineProperty;var K=(t,e,n)=>e in t?J(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var f=(t,e,n)=>(K(t,typeof e!="symbol"?e+"":e,n),n);(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="2f9053c7-fb04-496e-9d5f-22a639825c36",t._sentryDebugIdIdentifier="sentry-dbid-2f9053c7-fb04-496e-9d5f-22a639825c36")}catch{}})();function m(){}const yt=t=>t;function Q(t,e){for(const n in e)t[n]=e[n];return t}function V(t){return t()}function bt(){return Object.create(null)}function O(t){t.forEach(V)}function X(t){return typeof t=="function"}function Y(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}let x;function gt(t,e){return t===e?!0:(x||(x=document.createElement("a")),x.href=e,t===x.href)}function xt(t){return Object.keys(t).length===0}function S(t,...e){if(t==null){for(const i of e)i(void 0);return m}const n=t.subscribe(...e);return n.unsubscribe?()=>n.unsubscribe():n}function wt(t){let e;return S(t,n=>e=n)(),e}function Et(t,e,n){t.$$.on_destroy.push(S(e,n))}function vt(t,e,n,i){if(t){const s=q(t,e,n,i);return t[0](s)}}function q(t,e,n,i){return t[1]&&i?Q(n.ctx.slice(),t[1](i(e))):n.ctx}function Tt(t,e,n,i){if(t[2]&&i){const s=t[2](i(n));if(e.dirty===void 0)return s;if(typeof s=="object"){const o=[],r=Math.max(e.dirty.length,s.length);for(let l=0;l<r;l+=1)o[l]=e.dirty[l]|s[l];return o}return e.dirty|s}return e.dirty}function Nt(t,e,n,i,s,o){if(s){const r=q(e,n,i,o);t.p(r,s)}}function At(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let i=0;i<n;i++)e[i]=-1;return e}return-1}function kt(t){const e={};for(const n in t)n[0]!=="$"&&(e[n]=t[n]);return e}function Dt(t,e){const n={};e=new Set(e);for(const i in t)!e.has(i)&&i[0]!=="$"&&(n[i]=t[i]);return n}function Ht(t){return t??""}function St(t){const e=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return e?[parseFloat(e[1]),e[2]||"px"]:[t,"px"]}let E=!1;function Mt(){E=!0}function Pt(){E=!1}function Z(t,e,n,i){for(;t<e;){const s=t+(e-t>>1);n(s)<=i?t=s+1:e=s}return t}function $(t){if(t.hydrate_init)return;t.hydrate_init=!0;let e=t.childNodes;if(t.nodeName==="HEAD"){const c=[];for(let u=0;u<e.length;u++){const a=e[u];a.claim_order!==void 0&&c.push(a)}e=c}const n=new Int32Array(e.length+1),i=new Int32Array(e.length);n[0]=-1;let s=0;for(let c=0;c<e.length;c++){const u=e[c].claim_order,a=(s>0&&e[n[s]].claim_order<=u?s+1:Z(1,s,g=>e[n[g]].claim_order,u))-1;i[c]=n[a]+1;const _=a+1;n[_]=c,s=Math.max(_,s)}const o=[],r=[];let l=e.length-1;for(let c=n[s]+1;c!=0;c=i[c-1]){for(o.push(e[c-1]);l>=c;l--)r.push(e[l]);l--}for(;l>=0;l--)r.push(e[l]);o.reverse(),r.sort((c,u)=>c.claim_order-u.claim_order);for(let c=0,u=0;c<r.length;c++){for(;u<o.length&&r[c].claim_order>=o[u].claim_order;)u++;const a=u<o.length?o[u]:null;t.insertBefore(r[c],a)}}function tt(t,e){t.appendChild(e)}function et(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;return e&&e.host?e:t.ownerDocument}function jt(t){const e=M("style");return e.textContent="/* empty */",nt(et(t),e),e.sheet}function nt(t,e){return tt(t.head||t,e),e.sheet}function it(t,e){if(E){for($(t),(t.actual_end_child===void 0||t.actual_end_child!==null&&t.actual_end_child.parentNode!==t)&&(t.actual_end_child=t.firstChild);t.actual_end_child!==null&&t.actual_end_child.claim_order===void 0;)t.actual_end_child=t.actual_end_child.nextSibling;e!==t.actual_end_child?(e.claim_order!==void 0||e.parentNode!==t)&&t.insertBefore(e,t.actual_end_child):t.actual_end_child=e.nextSibling}else(e.parentNode!==t||e.nextSibling!==null)&&t.appendChild(e)}function st(t,e,n){t.insertBefore(e,n||null)}function rt(t,e,n){E&&!n?it(t,e):(e.parentNode!==t||e.nextSibling!=n)&&t.insertBefore(e,n||null)}function k(t){t.parentNode&&t.parentNode.removeChild(t)}function Ct(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function M(t){return document.createElement(t)}function I(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function P(t){return document.createTextNode(t)}function Lt(){return P(" ")}function Ot(){return P("")}function qt(t,e,n,i){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n,i)}function It(t){return function(e){return e.preventDefault(),t.call(this,e)}}function Bt(t){return function(e){return e.stopPropagation(),t.call(this,e)}}function B(t,e,n){n==null?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}const ct=["width","height"];function Rt(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const i in e)e[i]==null?t.removeAttribute(i):i==="style"?t.style.cssText=e[i]:i==="__value"?t.value=t[i]=e[i]:n[i]&&n[i].set&&ct.indexOf(i)===-1?t[i]=e[i]:B(t,i,e[i])}function zt(t,e){for(const n in e)B(t,n,e[n])}function Ft(t){return t.dataset.svelteH}function Gt(t){let e;return{p(...n){e=n,e.forEach(i=>t.push(i))},r(){e.forEach(n=>t.splice(t.indexOf(n),1))}}}function Ut(t){return Array.from(t.childNodes)}function R(t){t.claim_info===void 0&&(t.claim_info={last_index:0,total_claimed:0})}function z(t,e,n,i,s=!1){R(t);const o=(()=>{for(let r=t.claim_info.last_index;r<t.length;r++){const l=t[r];if(e(l)){const c=n(l);return c===void 0?t.splice(r,1):t[r]=c,s||(t.claim_info.last_index=r),l}}for(let r=t.claim_info.last_index-1;r>=0;r--){const l=t[r];if(e(l)){const c=n(l);return c===void 0?t.splice(r,1):t[r]=c,s?c===void 0&&t.claim_info.last_index--:t.claim_info.last_index=r,l}}return i()})();return o.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1,o}function F(t,e,n,i){return z(t,s=>s.nodeName===e,s=>{const o=[];for(let r=0;r<s.attributes.length;r++){const l=s.attributes[r];n[l.name]||o.push(l.name)}o.forEach(r=>s.removeAttribute(r))},()=>i(e))}function Wt(t,e,n){return F(t,e,n,M)}function Jt(t,e,n){return F(t,e,n,I)}function lt(t,e){return z(t,n=>n.nodeType===3,n=>{const i=""+e;if(n.data.startsWith(i)){if(n.data.length!==i.length)return n.splitText(i.length)}else n.data=i},()=>P(e),!0)}function Kt(t){return lt(t," ")}function C(t,e,n){for(let i=n;i<t.length;i+=1){const s=t[i];if(s.nodeType===8&&s.textContent.trim()===e)return i}return-1}function Qt(t,e){const n=C(t,"HTML_TAG_START",0),i=C(t,"HTML_TAG_END",n+1);if(n===-1||i===-1)return new T(e);R(t);const s=t.splice(n,i-n+1);k(s[0]),k(s[s.length-1]);const o=s.slice(1,s.length-1);if(o.length===0)return new T(e);for(const r of o)r.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1;return new T(e,o)}function Vt(t,e){e=""+e,t.data!==e&&(t.data=e)}function Xt(t,e){t.value=e??""}function Yt(t,e,n,i){n==null?t.style.removeProperty(e):t.style.setProperty(e,n,i?"important":"")}function ot(t,e,{bubbles:n=!1,cancelable:i=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:i})}function Zt(t,e){const n=[];let i=0;for(const s of e.childNodes)if(s.nodeType===8){const o=s.textContent.trim();o===`HEAD_${t}_END`?(i-=1,n.push(s)):o===`HEAD_${t}_START`&&(i+=1,n.push(s))}else i>0&&n.push(s);return n}class ut{constructor(e=!1){f(this,"is_svg",!1);f(this,"e");f(this,"n");f(this,"t");f(this,"a");this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,n,i=null){this.e||(this.is_svg?this.e=I(n.nodeName):this.e=M(n.nodeType===11?"TEMPLATE":n.nodeName),this.t=n.tagName!=="TEMPLATE"?n:n.content,this.c(e)),this.i(i)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let n=0;n<this.n.length;n+=1)st(this.t,this.n[n],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(k)}}class T extends ut{constructor(n=!1,i){super(n);f(this,"l");this.e=this.n=null,this.l=i}c(n){this.l?this.n=this.l:super.c(n)}i(n){for(let i=0;i<this.n.length;i+=1)rt(this.t,this.n[i],n)}}function $t(t,e){return new t(e)}let w;function N(t){w=t}function j(){if(!w)throw new Error("Function called outside component initialization");return w}function te(t){j().$$.on_mount.push(t)}function ee(t){j().$$.after_update.push(t)}function ne(){const t=j();return(e,n,{cancelable:i=!1}={})=>{const s=t.$$.callbacks[e];if(s){const o=ot(e,n,{cancelable:i});return s.slice().forEach(r=>{r.call(t,o)}),!o.defaultPrevented}return!0}}const b=[],L=[];let y=[];const D=[],G=Promise.resolve();let H=!1;function at(){H||(H=!0,G.then(_t))}function ie(){return at(),G}function ft(t){y.push(t)}function se(t){D.push(t)}const A=new Set;let h=0;function _t(){if(h!==0)return;const t=w;do{try{for(;h<b.length;){const e=b[h];h++,N(e),dt(e.$$)}}catch(e){throw b.length=0,h=0,e}for(N(null),b.length=0,h=0;L.length;)L.pop()();for(let e=0;e<y.length;e+=1){const n=y[e];A.has(n)||(A.add(n),n())}y.length=0}while(b.length);for(;D.length;)D.pop()();H=!1,A.clear(),N(t)}function dt(t){if(t.fragment!==null){t.update(),O(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(ft)}}function re(t){const e=[],n=[];y.forEach(i=>t.indexOf(i)===-1?e.push(i):n.push(i)),n.forEach(i=>i()),y=e}const p=[];function ht(t,e){return{subscribe:pt(t,e).subscribe}}function pt(t,e=m){let n;const i=new Set;function s(l){if(Y(t,l)&&(t=l,n)){const c=!p.length;for(const u of i)u[1](),p.push(u,t);if(c){for(let u=0;u<p.length;u+=2)p[u][0](p[u+1]);p.length=0}}}function o(l){s(l(t))}function r(l,c=m){const u=[l,c];return i.add(u),i.size===1&&(n=e(s,o)||m),l(t),()=>{i.delete(u),i.size===0&&n&&(n(),n=null)}}return{set:s,update:o,subscribe:r}}function ce(t,e,n){const i=!Array.isArray(t),s=i?[t]:t;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=e.length<2;return ht(n,(r,l)=>{let c=!1;const u=[];let a=0,_=m;const g=()=>{if(a)return;_();const d=e(i?u[0]:u,r,l);o?r(d):_=X(d)?d:m},U=s.map((d,v)=>S(d,W=>{u[v]=W,a&=~(1<<v),c&&g()},()=>{a|=1<<v}));return c=!0,g(),function(){O(U),_(),c=!1}})}export{ot as $,Dt as A,Q as B,kt as C,ne as D,qt as E,ft as F,Ft as G,ce as H,O as I,I as J,T as K,Jt as L,Qt as M,zt as N,St as O,$t as P,Zt as Q,ee as R,Yt as S,ie as T,gt as U,Rt as V,Bt as W,It as X,et as Y,jt as Z,X as _,Ut as a,yt as a0,bt as a1,_t as a2,xt as a3,re as a4,w as a5,N as a6,V as a7,b as a8,at as a9,Mt as aa,Pt as ab,Gt as ac,Xt as ad,Ht as ae,B as b,Wt as c,k as d,M as e,Et as f,lt as g,it as h,rt as i,Vt as j,L as k,Lt as l,Kt as m,se as n,te as o,Ot as p,Ct as q,m as r,Y as s,P as t,wt as u,vt as v,pt as w,Nt as x,At as y,Tt as z};
