import{O as m}from"./index.UaHqEmIZ.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="666a6b6c-edc5-4152-9c86-146ca68c7a99",e._sentryDebugIdIdentifier="sentry-dbid-666a6b6c-edc5-4152-9c86-146ca68c7a99")}catch{}})();function b(e){const r=e-1;return r*r*r+1}function w(e,{delay:r=0,duration:d=400,easing:l=b,x:i=0,y:a=0,opacity:y=0}={}){const s=getComputedStyle(e),c=+s.opacity,n=s.transform==="none"?"":s.transform,o=c*(1-y),[$,u]=m(i),[_,f]=m(a);return{delay:r,duration:d,easing:l,css:(p,g)=>`
			transform: ${n} translate(${(1-p)*$}${u}, ${(1-p)*_}${f});
			opacity: ${c-o*g}`}}function v(e,{delay:r=0,duration:d=400,easing:l=b,axis:i="y"}={}){const a=getComputedStyle(e),y=+a.opacity,s=i==="y"?"height":"width",c=parseFloat(a[s]),n=i==="y"?["top","bottom"]:["left","right"],o=n.map(t=>`${t[0].toUpperCase()}${t.slice(1)}`),$=parseFloat(a[`padding${o[0]}`]),u=parseFloat(a[`padding${o[1]}`]),_=parseFloat(a[`margin${o[0]}`]),f=parseFloat(a[`margin${o[1]}`]),p=parseFloat(a[`border${o[0]}Width`]),g=parseFloat(a[`border${o[1]}Width`]);return{delay:r,duration:d,easing:l,css:t=>`overflow: hidden;opacity: ${Math.min(t*20,1)*y};${s}: ${t*c}px;padding-${n[0]}: ${t*$}px;padding-${n[1]}: ${t*u}px;margin-${n[0]}: ${t*_}px;margin-${n[1]}: ${t*f}px;border-${n[0]}-width: ${t*p}px;border-${n[1]}-width: ${t*g}px;`}}export{w as f,v as s};
