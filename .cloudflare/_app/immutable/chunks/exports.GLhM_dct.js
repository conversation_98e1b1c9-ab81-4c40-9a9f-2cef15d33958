(function(){try{var i=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(i._sentryDebugIds=i._sentryDebugIds||{},i._sentryDebugIds[e]="85f1f1fc-7ff8-4f73-b4cc-f0be739fb64f",i._sentryDebugIdIdentifier="sentry-dbid-85f1f1fc-7ff8-4f73-b4cc-f0be739fb64f")}catch{}})();var fe,g=typeof window<"u"?window:void 0,te=typeof globalThis<"u"?globalThis:g,Ss=Array.prototype,nr=Ss.forEach,rr=Ss.indexOf,ne=te==null?void 0:te.navigator,S=te==null?void 0:te.document,ae=te==null?void 0:te.location,Xi=te==null?void 0:te.fetch,Qi=te!=null&&te.XMLHttpRequest&&"withCredentials"in new te.XMLHttpRequest?te.XMLHttpRequest:void 0,sr=te==null?void 0:te.AbortController,H=ne==null?void 0:ne.userAgent,P=g??{},Ce={DEBUG:!1,LIB_VERSION:"1.219.4"},Mi="$copy_autocapture",Ho=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"];(function(i){i.GZipJS="gzip-js",i.Base64="base64"})(fe||(fe={}));var oi={};function qe(i,e,t){if(q(i)){if(nr&&i.forEach===nr)i.forEach(e,t);else if("length"in i&&i.length===+i.length){for(var n=0,r=i.length;n<r;n++)if(n in i&&e.call(t,i[n],n)===oi)return}}}function A(i,e,t){if(!F(i)){if(q(i))return qe(i,e,t);if(Jo(i)){for(var n of i.entries())if(e.call(t,n[1],n[0])===oi)return}else for(var r in i)if(Is.call(i,r)&&e.call(t,i[r],r)===oi)return}}var G=function(i){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];return qe(t,function(r){for(var s in r)r[s]!==void 0&&(i[s]=r[s])}),i},ti=function(i){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];return qe(t,function(r){qe(r,function(s){i.push(s)})}),i};function ii(i){for(var e=Object.keys(i),t=e.length,n=new Array(t);t--;)n[t]=[e[t],i[e[t]]];return n}var or=function(i){try{return i()}catch{return}},jo=function(i){return function(){try{for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.apply(this,t)}catch(r){k.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),k.critical(r)}}},ai=function(i){var e={};return A(i,function(t,n){V(t)&&t.length>0&&(e[n]=t)}),e};function zo(i,e){return t=i,n=s=>V(s)&&!je(e)?s.slice(0,e):s,r=new Set,function s(o,a){return o!==Object(o)?n?n(o,a):o:r.has(o)?void 0:(r.add(o),q(o)?(l=[],qe(o,u=>{l.push(s(u))})):(l={},A(o,(u,d)=>{r.has(u)||(l[d]=s(u,d))})),l);var l}(t);var t,n,r}var Go=["herokuapp.com","vercel.app","netlify.app"];function Wo(i){var e=i==null?void 0:i.hostname;if(!V(e))return!1;var t=e.split(".").slice(-2).join(".");for(var n of Go)if(t===n)return!1;return!0}function Es(i,e){for(var t=0;t<i.length;t++)if(e(i[t]))return i[t]}function U(i,e,t,n){var{capture:r=!1,passive:s=!0}=n??{};i==null||i.addEventListener(e,t,{capture:r,passive:s})}function T(i,e){return i.indexOf(e)!==-1}var Ei=function(i){return i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},ks=function(i){return i.replace(/^\$/,"")},_t=function(i,e){if(!function(t){try{new RegExp(t)}catch{return!1}return!0}(e))return!1;try{return new RegExp(e).test(i)}catch{return!1}},Vo=Array.isArray,xs=Object.prototype,Is=xs.hasOwnProperty,ki=xs.toString,q=Vo||function(i){return ki.call(i)==="[object Array]"},le=i=>typeof i=="function",B=i=>i===Object(i)&&!q(i),ct=i=>{if(B(i)){for(var e in i)if(Is.call(i,e))return!1;return!0}return!1},E=i=>i===void 0,V=i=>ki.call(i)=="[object String]",ar=i=>V(i)&&i.trim().length===0,je=i=>i===null,F=i=>E(i)||je(i),Z=i=>ki.call(i)=="[object Number]",De=i=>ki.call(i)==="[object Boolean]",Jo=i=>i instanceof FormData,$i=i=>i instanceof Error,Yo=i=>T(Ho,i),Ps=i=>{var e={_log:function(t){if(g&&(Ce.DEBUG||P.POSTHOG_DEBUG)&&!E(g.console)&&g.console){for(var n=("__rrweb_original__"in g.console[t])?g.console[t].__rrweb_original__:g.console[t],r=arguments.length,s=new Array(r>1?r-1:0),o=1;o<r;o++)s[o-1]=arguments[o];n(i,...s)}},info:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e._log("log",...n)},warn:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e._log("warn",...n)},error:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e._log("error",...n)},critical:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];console.error(i,...n)},uninitializedWarning:t=>{e.error("You must initialize PostHog before calling ".concat(t))},createLogger:t=>Ps("".concat(i," ").concat(t))};return e},k=Ps("[PostHog.js]"),K=k.createLogger,Ko=K("[ExternalScriptsLoader]"),lr=(i,e,t)=>{if(i.config.disable_external_dependency_loading)return Ko.warn("".concat(e," was requested but loading of external scripts is disabled.")),t("Loading of external scripts is disabled");var n=()=>{if(!S)return t("document not found");var r=S.createElement("script");if(r.type="text/javascript",r.crossOrigin="anonymous",r.src=e,r.onload=a=>t(void 0,a),r.onerror=a=>t(a),i.config.prepare_external_dependency_script&&(r=i.config.prepare_external_dependency_script(r)),!r)return t("prepare_external_dependency_script returned null");var s,o=S.querySelectorAll("body > script");o.length>0?(s=o[0].parentNode)===null||s===void 0||s.insertBefore(r,o[0]):S.body.appendChild(r)};S!=null&&S.body?n():S==null||S.addEventListener("DOMContentLoaded",n)};function cr(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),t.push.apply(t,n)}return t}function v(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?cr(Object(t),!0).forEach(function(n){y(i,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(t)):cr(Object(t)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(t,n))})}return i}function y(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}function Cs(i,e){if(i==null)return{};var t,n,r=function(o,a){if(o==null)return{};var l,u,d={},c=Object.keys(o);for(u=0;u<c.length;u++)l=c[u],a.indexOf(l)>=0||(d[l]=o[l]);return d}(i,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(i);for(n=0;n<s.length;n++)t=s[n],e.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(i,t)&&(r[t]=i[t])}return r}P.__PosthogExtensions__=P.__PosthogExtensions__||{},P.__PosthogExtensions__.loadExternalDependency=(i,e,t)=>{var n="/static/".concat(e,".js")+"?v=".concat(i.version);if(e==="remote-config"&&(n="/array/".concat(i.config.token,"/config.js")),e==="toolbar"){var r=3e5,s=Math.floor(Date.now()/r)*r;n="".concat(n,"&t=").concat(s)}var o=i.requestRouter.endpointFor("assets",n);lr(i,o,t)},P.__PosthogExtensions__.loadSiteApp=(i,e,t)=>{var n=i.requestRouter.endpointFor("api",e);lr(i,n,t)};var Ts="$people_distinct_id",Pt="__alias",Ct="__timers",ur="$autocapture_disabled_server_side",Zi="$heatmaps_enabled_server_side",dr="$exception_capture_enabled_server_side",hr="$web_vitals_enabled_server_side",Rs="$dead_clicks_enabled_server_side",pr="$web_vitals_allowed_metrics",en="$session_recording_enabled_server_side",_r="$console_log_recording_enabled_server_side",gr="$session_recording_network_payload_capture",fr="$session_recording_canvas_recording",vr="$replay_sample_rate",mr="$replay_minimum_duration",yr="$replay_script_config",li="$sesid",Tt="$session_is_sampled",Di="$session_recording_url_trigger_activated_session",Li="$session_recording_event_trigger_activated_session",ut="$enabled_feature_flags",ni="$early_access_features",Rt="$stored_person_properties",Xe="$stored_group_properties",tn="$surveys",Gt="$surveys_activated",ci="$flag_call_reported",ke="$user_state",nn="$client_session_props",rn="$capture_rate_limit",sn="$initial_campaign_params",on="$initial_referrer_info",an="$initial_person_info",ui="$epp",Fs="__POSTHOG_TOOLBAR__",br="$posthog_cookieless",Xo=[Ts,Pt,"__cmpns",Ct,en,Zi,li,ut,ke,ni,Xe,Rt,tn,ci,nn,rn,sn,on,ui],at=K("[FeatureFlags]"),Ni="$active_feature_flags",St="$override_feature_flags",wr="$feature_flag_payloads",Wt="$override_feature_flag_payloads",Sr=i=>{var e={};for(var[t,n]of ii(i||{}))n&&(e[t]=n);return e};class Qo{constructor(e){y(this,"_override_warning",!1),y(this,"_hasLoadedFlags",!1),y(this,"_requestInFlight",!1),y(this,"_reloadingDisabled",!1),y(this,"_additionalReloadRequested",!1),y(this,"_decideCalled",!1),y(this,"_flagsLoadedFromRemote",!1),this.instance=e,this.featureFlagEventHandlers=[]}decide(){if(this.instance.config.__preview_remote_config)this._decideCalled=!0;else{var e=!this._reloadDebouncer&&(this.instance.config.advanced_disable_feature_flags||this.instance.config.advanced_disable_feature_flags_on_first_load);this._callDecideEndpoint({disableFlags:e})}}get hasLoadedFlags(){return this._hasLoadedFlags}getFlags(){return Object.keys(this.getFlagVariants())}getFlagVariants(){var e=this.instance.get_property(ut),t=this.instance.get_property(St);if(!t)return e||{};for(var n=G({},e),r=Object.keys(t),s=0;s<r.length;s++)n[r[s]]=t[r[s]];return this._override_warning||(at.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:n}),this._override_warning=!0),n}getFlagPayloads(){var e=this.instance.get_property(wr),t=this.instance.get_property(Wt);if(!t)return e||{};for(var n=G({},e||{}),r=Object.keys(t),s=0;s<r.length;s++)n[r[s]]=t[r[s]];return this._override_warning||(at.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:n}),this._override_warning=!0),n}reloadFeatureFlags(){this._reloadingDisabled||this.instance.config.advanced_disable_feature_flags||this._reloadDebouncer||(this._reloadDebouncer=setTimeout(()=>{this._callDecideEndpoint()},5))}clearDebouncer(){clearTimeout(this._reloadDebouncer),this._reloadDebouncer=void 0}ensureFlagsLoaded(){this._hasLoadedFlags||this._requestInFlight||this._reloadDebouncer||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this._reloadingDisabled=e}_callDecideEndpoint(e){if(this.clearDebouncer(),!this.instance.config.advanced_disable_decide)if(this._requestInFlight)this._additionalReloadRequested=!0;else{var t={token:this.instance.config.token,distinct_id:this.instance.get_distinct_id(),groups:this.instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:this.instance.get_property(Rt),group_properties:this.instance.get_property(Xe)};(e!=null&&e.disableFlags||this.instance.config.advanced_disable_feature_flags)&&(t.disable_flags=!0),this._requestInFlight=!0,this.instance._send_request({method:"POST",url:this.instance.requestRouter.endpointFor("api","/decide/?v=3"),data:t,compression:this.instance.config.disable_compression?void 0:fe.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:n=>{var r,s,o=!0;n.statusCode===200&&(this.$anon_distinct_id=void 0,o=!1),this._requestInFlight=!1,this._decideCalled||(this._decideCalled=!0,this.instance._onRemoteConfig((s=n.json)!==null&&s!==void 0?s:{})),t.disable_flags||(this._flagsLoadedFromRemote=!o,this.receivedFeatureFlags((r=n.json)!==null&&r!==void 0?r:{},o),this._additionalReloadRequested&&(this._additionalReloadRequested=!1,this._callDecideEndpoint()))}})}}getFeatureFlag(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0){var n,r,s,o,a,l=this.getFlagVariants()[e],u="".concat(l),d=this.instance.get_property(ci)||{};return(t.send_event||!("send_event"in t))&&(!(e in d)||!d[e].includes(u))&&(q(d[e])?d[e].push(u):d[e]=[u],(n=this.instance.persistence)===null||n===void 0||n.register({[ci]:d}),this.instance.capture("$feature_flag_called",{$feature_flag:e,$feature_flag_response:l,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_bootstrapped_response:((r=this.instance.config.bootstrap)===null||r===void 0||(s=r.featureFlags)===null||s===void 0?void 0:s[e])||null,$feature_flag_bootstrapped_payload:((o=this.instance.config.bootstrap)===null||o===void 0||(a=o.featureFlagPayloads)===null||a===void 0?void 0:a[e])||null,$used_bootstrap_value:!this._flagsLoadedFromRemote})),l}at.warn('getFeatureFlag for key "'+e+`" failed. Feature flags didn't load in time.`)}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var n=this.instance.config.token;this.instance._send_request({method:"POST",url:this.instance.requestRouter.endpointFor("api","/decide/?v=3"),data:{distinct_id:this.instance.get_distinct_id(),token:n},compression:this.instance.config.disable_compression?void 0:fe.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:r=>{var s,o=(s=r.json)===null||s===void 0?void 0:s.featureFlagPayloads;t((o==null?void 0:o[e])||void 0)}})}isFeatureEnabled(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);at.warn('isFeatureEnabled for key "'+e+`" failed. Feature flags didn't load in time.`)}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this.instance.persistence){this._hasLoadedFlags=!0;var n=this.getFlagVariants(),r=this.getFlagPayloads();(function(s,o){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},l=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},u=s.featureFlags,d=s.featureFlagPayloads;if(u)if(q(u)){var c={};if(u)for(var p=0;p<u.length;p++)c[u[p]]=!0;o&&o.register({[Ni]:u,[ut]:c})}else{var h=u,_=d;s.errorsWhileComputingFlags&&(h=v(v({},a),h),_=v(v({},l),_)),o&&o.register({[Ni]:Object.keys(Sr(h)),[ut]:h||{},[wr]:_||{}})}})(e,this.instance.persistence,n,r),this._fireFeatureFlagsCallbacks(t)}}override(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];at.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this.instance.__loaded||!this.instance.persistence)return at.uninitializedWarning("posthog.feature_flags.overrideFeatureFlags");if(e===!1)return this.instance.persistence.unregister(St),this.instance.persistence.unregister(Wt),void this._fireFeatureFlagsCallbacks();if(e&&typeof e=="object"&&("flags"in e||"payloads"in e)){var t,n=e;if(this._override_warning=!!((t=n.suppressWarning)!==null&&t!==void 0&&t),"flags"in n){if(n.flags===!1)this.instance.persistence.unregister(St);else if(n.flags)if(q(n.flags)){for(var r={},s=0;s<n.flags.length;s++)r[n.flags[s]]=!0;this.instance.persistence.register({[St]:r})}else this.instance.persistence.register({[St]:n.flags})}return"payloads"in n&&(n.payloads===!1?this.instance.persistence.unregister(Wt):n.payloads&&this.instance.persistence.register({[Wt]:n.payloads})),void this._fireFeatureFlagsCallbacks()}this._fireFeatureFlagsCallbacks()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this._hasLoadedFlags){var{flags:t,flagVariants:n}=this._prepareFeatureFlagsForCallbacks();e(t,n)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t){var n,r=(this.instance.get_property(ni)||[]).find(l=>l.flagKey===e),s={["$feature_enrollment/".concat(e)]:t},o={$feature_flag:e,$feature_enrollment:t,$set:s};r&&(o.$early_access_feature_name=r.name),this.instance.capture("$feature_enrollment_update",o),this.setPersonPropertiesForFlags(s,!1);var a=v(v({},this.getFlagVariants()),{},{[e]:t});(n=this.instance.persistence)===null||n===void 0||n.register({[Ni]:Object.keys(Sr(a)),[ut]:a}),this._fireFeatureFlagsCallbacks()}getEarlyAccessFeatures(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1],n=this.instance.get_property(ni);if(n&&!t)return e(n);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/early_access_features/?token=".concat(this.instance.config.token)),method:"GET",callback:r=>{var s;if(r.json){var o=r.json.earlyAccessFeatures;return(s=this.instance.persistence)===null||s===void 0||s.register({[ni]:o}),e(o)}}})}_prepareFeatureFlagsForCallbacks(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(n=>t[n]),flagVariants:Object.keys(t).filter(n=>t[n]).reduce((n,r)=>(n[r]=t[r],n),{})}}_fireFeatureFlagsCallbacks(e){var{flags:t,flagVariants:n}=this._prepareFeatureFlagsForCallbacks();this.featureFlagEventHandlers.forEach(r=>r(t,n,{errorsLoading:e}))}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this.instance.get_property(Rt)||{};this.instance.register({[Rt]:v(v({},n),e)}),t&&this.instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this.instance.unregister(Rt)}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this.instance.get_property(Xe)||{};Object.keys(n).length!==0&&Object.keys(n).forEach(r=>{n[r]=v(v({},n[r]),e[r]),delete e[r]}),this.instance.register({[Xe]:v(v({},n),e)}),t&&this.instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this.instance.get_property(Xe)||{};this.instance.register({[Xe]:v(v({},t),{},{[e]:{}})})}else this.instance.unregister(Xe)}}Math.trunc||(Math.trunc=function(i){return i<0?Math.ceil(i):Math.floor(i)}),Number.isInteger||(Number.isInteger=function(i){return Z(i)&&isFinite(i)&&Math.floor(i)===i});var Er="0123456789abcdef";class di{constructor(e){if(this.bytes=e,e.length!==16)throw new TypeError("not 128-bit length")}static fromFieldsV7(e,t,n,r){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(n)||!Number.isInteger(r)||e<0||t<0||n<0||r<0||e>0xffffffffffff||t>4095||n>1073741823||r>4294967295)throw new RangeError("invalid field value");var s=new Uint8Array(16);return s[0]=e/Math.pow(2,40),s[1]=e/Math.pow(2,32),s[2]=e/Math.pow(2,24),s[3]=e/Math.pow(2,16),s[4]=e/Math.pow(2,8),s[5]=e,s[6]=112|t>>>8,s[7]=t,s[8]=128|n>>>24,s[9]=n>>>16,s[10]=n>>>8,s[11]=n,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new di(s)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+Er.charAt(this.bytes[t]>>>4)+Er.charAt(15&this.bytes[t]),t!==3&&t!==5&&t!==7&&t!==9||(e+="-");if(e.length!==36)throw new Error("Invalid UUIDv7 was generated");return e}clone(){return new di(this.bytes.slice(0))}equals(e){return this.compareTo(e)===0}compareTo(e){for(var t=0;t<16;t++){var n=this.bytes[t]-e.bytes[t];if(n!==0)return Math.sign(n)}return 0}}class Zo{constructor(){y(this,"timestamp",0),y(this,"counter",0),y(this,"random",new ea)}generate(){var e=this.generateOrAbort();if(E(e)){this.timestamp=0;var t=this.generateOrAbort();if(E(t))throw new Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+1e4>this.timestamp))return;this.counter++,this.counter>4398046511103&&(this.timestamp++,this.resetCounter())}return di.fromFieldsV7(this.timestamp,Math.trunc(this.counter/Math.pow(2,30)),this.counter&Math.pow(2,30)-1,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}}var kr,Os=i=>{if(typeof UUIDV7_DENY_WEAK_RNG<"u"&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var e=0;e<i.length;e++)i[e]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return i};g&&!E(g.crypto)&&crypto.getRandomValues&&(Os=i=>crypto.getRandomValues(i));class ea{constructor(){y(this,"buffer",new Uint32Array(8)),y(this,"cursor",1/0)}nextUint32(){return this.cursor>=this.buffer.length&&(Os(this.buffer),this.cursor=0),this.buffer[this.cursor++]}}var Le=()=>ta().toString(),ta=()=>(kr||(kr=new Zo)).generate(),ia="Thu, 01 Jan 1970 00:00:00 GMT",Et="",na=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i;function ra(i,e){if(e){var t=function(r){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S;if(Et)return Et;if(!s||["localhost","127.0.0.1"].includes(r))return"";for(var o=r.split("."),a=Math.min(o.length,8),l="dmn_chk_"+Le(),u=new RegExp("(^|;)\\s*"+l+"=1");!Et&&a--;){var d=o.slice(a).join("."),c=l+"=1;domain=."+d;s.cookie=c,u.test(s.cookie)&&(s.cookie=c+";expires="+ia,Et=d)}return Et}(i);if(!t){var n=(r=>{var s=r.match(na);return s?s[0]:""})(i);n!==t&&k.info("Warning: cookie subdomain discovery mismatch",n,t),t=n}return t?"; domain=."+t:""}return""}var Re={is_supported:()=>!!S,error:function(i){k.error("cookieStore error: "+i)},get:function(i){if(S){try{for(var e=i+"=",t=S.cookie.split(";").filter(s=>s.length),n=0;n<t.length;n++){for(var r=t[n];r.charAt(0)==" ";)r=r.substring(1,r.length);if(r.indexOf(e)===0)return decodeURIComponent(r.substring(e.length,r.length))}}catch{}return null}},parse:function(i){var e;try{e=JSON.parse(Re.get(i))||{}}catch{}return e},set:function(i,e,t,n,r){if(S)try{var s="",o="",a=ra(S.location.hostname,n);if(t){var l=new Date;l.setTime(l.getTime()+24*t*60*60*1e3),s="; expires="+l.toUTCString()}r&&(o="; secure");var u=i+"="+encodeURIComponent(JSON.stringify(e))+s+"; SameSite=Lax; path=/"+a+o;return u.length>3686.4&&k.warn("cookieStore warning: large cookie, len="+u.length),S.cookie=u,u}catch{return}},remove:function(i,e){try{Re.set(i,"",-1,e)}catch{return}}},qi=null,j={is_supported:function(){if(!je(qi))return qi;var i=!0;if(E(g))i=!1;else try{var e="__mplssupport__";j.set(e,"xyz"),j.get(e)!=='"xyz"'&&(i=!1),j.remove(e)}catch{i=!1}return i||k.error("localStorage unsupported; falling back to cookie store"),qi=i,i},error:function(i){k.error("localStorage error: "+i)},get:function(i){try{return g==null?void 0:g.localStorage.getItem(i)}catch(e){j.error(e)}return null},parse:function(i){try{return JSON.parse(j.get(i))||{}}catch{}return null},set:function(i,e){try{g==null||g.localStorage.setItem(i,JSON.stringify(e))}catch(t){j.error(t)}},remove:function(i){try{g==null||g.localStorage.removeItem(i)}catch(e){j.error(e)}}},sa=["distinct_id",li,Tt,ui,an],Vt=v(v({},j),{},{parse:function(i){try{var e={};try{e=Re.parse(i)||{}}catch{}var t=G(e,JSON.parse(j.get(i)||"{}"));return j.set(i,t),t}catch{}return null},set:function(i,e,t,n,r,s){try{j.set(i,e,void 0,void 0,s);var o={};sa.forEach(a=>{e[a]&&(o[a]=e[a])}),Object.keys(o).length&&Re.set(i,o,t,n,r,s)}catch(a){j.error(a)}},remove:function(i,e){try{g==null||g.localStorage.removeItem(i),Re.remove(i,e)}catch(t){j.error(t)}}}),Jt={},oa={is_supported:function(){return!0},error:function(i){k.error("memoryStorage error: "+i)},get:function(i){return Jt[i]||null},parse:function(i){return Jt[i]||null},set:function(i,e){Jt[i]=e},remove:function(i){delete Jt[i]}},Ge=null,W={is_supported:function(){if(!je(Ge))return Ge;if(Ge=!0,E(g))Ge=!1;else try{var i="__support__";W.set(i,"xyz"),W.get(i)!=='"xyz"'&&(Ge=!1),W.remove(i)}catch{Ge=!1}return Ge},error:function(i){k.error("sessionStorage error: ",i)},get:function(i){try{return g==null?void 0:g.sessionStorage.getItem(i)}catch(e){W.error(e)}return null},parse:function(i){try{return JSON.parse(W.get(i))||null}catch{}return null},set:function(i,e){try{g==null||g.sessionStorage.setItem(i,JSON.stringify(e))}catch(t){W.error(t)}},remove:function(i){try{g==null||g.sessionStorage.removeItem(i)}catch(e){W.error(e)}}},aa=["localhost","127.0.0.1"],dt=i=>{var e=S==null?void 0:S.createElement("a");return E(e)?null:(e.href=i,e)},la=function(i){var e,t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"&",r=[];return A(i,function(s,o){E(s)||E(o)||o==="undefined"||(e=encodeURIComponent((a=>a instanceof File)(s)?s.name:s.toString()),t=encodeURIComponent(o),r[r.length]=t+"="+e)}),r.join(n)},hi=function(i,e){for(var t,n=((i.split("#")[0]||"").split("?")[1]||"").split("&"),r=0;r<n.length;r++){var s=n[r].split("=");if(s[0]===e){t=s;break}}if(!q(t)||t.length<2)return"";var o=t[1];try{o=decodeURIComponent(o)}catch{k.error("Skipping decoding for malformed query param: "+o)}return o.replace(/\+/g," ")},Bi=function(i,e,t){if(!i||!e||!e.length)return i;for(var n=i.split("#"),r=n[0]||"",s=n[1],o=r.split("?"),a=o[1],l=o[0],u=(a||"").split("&"),d=[],c=0;c<u.length;c++){var p=u[c].split("=");q(p)&&(e.includes(p[0])?d.push(p[0]+"="+t):d.push(u[c]))}var h=l;return a!=null&&(h+="?"+d.join("&")),s!=null&&(h+="#"+s),h},pi=function(i,e){var t=i.match(new RegExp(e+"=([^&]*)"));return t?t[1]:null},ue="Mobile",_i="iOS",we="Android",Bt="Tablet",As=we+" "+Bt,Ms="iPad",$s="Apple",Ds=$s+" Watch",Ut="Safari",gt="BlackBerry",Ls="Samsung",Ns=Ls+"Browser",qs=Ls+" Internet",it="Chrome",ca=it+" OS",Bs=it+" "+_i,Fn="Internet Explorer",Us=Fn+" "+ue,On="Opera",ua=On+" Mini",An="Edge",Hs="Microsoft "+An,ht="Firefox",js=ht+" "+_i,Ht="Nintendo",jt="PlayStation",pt="Xbox",zs=we+" "+ue,Gs=ue+" "+Ut,Ft="Windows",ln=Ft+" Phone",xr="Nokia",cn="Ouya",Ws="Generic",da=Ws+" "+ue.toLowerCase(),Vs=Ws+" "+Bt.toLowerCase(),un="Konqueror",Q="(\\d+(\\.\\d+)?)",Ui=new RegExp("Version/"+Q),ha=new RegExp(pt,"i"),pa=new RegExp(jt+" \\w+","i"),_a=new RegExp(Ht+" \\w+","i"),Mn=new RegExp(gt+"|PlayBook|BB10","i"),ga={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},fa=(i,e)=>e&&T(e,$s)||function(t){return T(t,Ut)&&!T(t,it)&&!T(t,we)}(i),Ir=function(i,e){return e=e||"",T(i," OPR/")&&T(i,"Mini")?ua:T(i," OPR/")?On:Mn.test(i)?gt:T(i,"IE"+ue)||T(i,"WPDesktop")?Us:T(i,Ns)?qs:T(i,An)||T(i,"Edg/")?Hs:T(i,"FBIOS")?"Facebook "+ue:T(i,"UCWEB")||T(i,"UCBrowser")?"UC Browser":T(i,"CriOS")?Bs:T(i,"CrMo")||T(i,it)?it:T(i,we)&&T(i,Ut)?zs:T(i,"FxiOS")?js:T(i.toLowerCase(),un.toLowerCase())?un:fa(i,e)?T(i,ue)?Gs:Ut:T(i,ht)?ht:T(i,"MSIE")||T(i,"Trident/")?Fn:T(i,"Gecko")?ht:""},va={[Us]:[new RegExp("rv:"+Q)],[Hs]:[new RegExp(An+"?\\/"+Q)],[it]:[new RegExp("("+it+"|CrMo)\\/"+Q)],[Bs]:[new RegExp("CriOS\\/"+Q)],"UC Browser":[new RegExp("(UCBrowser|UCWEB)\\/"+Q)],[Ut]:[Ui],[Gs]:[Ui],[On]:[new RegExp("(Opera|OPR)\\/"+Q)],[ht]:[new RegExp(ht+"\\/"+Q)],[js]:[new RegExp("FxiOS\\/"+Q)],[un]:[new RegExp("Konqueror[:/]?"+Q,"i")],[gt]:[new RegExp(gt+" "+Q),Ui],[zs]:[new RegExp("android\\s"+Q,"i")],[qs]:[new RegExp(Ns+"\\/"+Q)],[Fn]:[new RegExp("(rv:|MSIE )"+Q)],Mozilla:[new RegExp("rv:"+Q)]},Pr=[[new RegExp(pt+"; "+pt+" (.*?)[);]","i"),i=>[pt,i&&i[1]||""]],[new RegExp(Ht,"i"),[Ht,""]],[new RegExp(jt,"i"),[jt,""]],[Mn,[gt,""]],[new RegExp(Ft,"i"),(i,e)=>{if(/Phone/.test(e)||/WPDesktop/.test(e))return[ln,""];if(new RegExp(ue).test(e)&&!/IEMobile\b/.test(e))return[Ft+" "+ue,""];var t=/Windows NT ([0-9.]+)/i.exec(e);if(t&&t[1]){var n=t[1],r=ga[n]||"";return/arm/i.test(e)&&(r="RT"),[Ft,r]}return[Ft,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,i=>{if(i&&i[3]){var e=[i[3],i[4],i[5]||"0"];return[_i,e.join(".")]}return[_i,""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,i=>{var e="";return i&&i.length>=3&&(e=E(i[2])?i[3]:i[2]),["watchOS",e]}],[new RegExp("("+we+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+we+")","i"),i=>{if(i&&i[2]){var e=[i[2],i[3],i[4]||"0"];return[we,e.join(".")]}return[we,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,i=>{var e=["Mac OS X",""];if(i&&i[1]){var t=[i[1],i[2],i[3]||"0"];e[1]=t.join(".")}return e}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[ca,""]],[/Linux|debian/i,["Linux",""]]],Cr=function(i){return _a.test(i)?Ht:pa.test(i)?jt:ha.test(i)?pt:new RegExp(cn,"i").test(i)?cn:new RegExp("("+ln+"|WPDesktop)","i").test(i)?ln:/iPad/.test(i)?Ms:/iPod/.test(i)?"iPod Touch":/iPhone/.test(i)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(i)?Ds:Mn.test(i)?gt:/(kobo)\s(ereader|touch)/i.test(i)?"Kobo":new RegExp(xr,"i").test(i)?xr:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(i)||/(kf[a-z]+)( bui|\)).+silk\//i.test(i)?"Kindle Fire":/(Android|ZTE)/i.test(i)?!new RegExp(ue).test(i)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(i)?/pixel[\daxl ]{1,6}/i.test(i)&&!/pixel c/i.test(i)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(i)||/lmy47v/i.test(i)&&!/QTAQZ3/i.test(i)?we:As:we:new RegExp("(pda|"+ue+")","i").test(i)?da:new RegExp(Bt,"i").test(i)&&!new RegExp(Bt+" pc","i").test(i)?Vs:""},Yt="https?://(.*)",ri=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","irclid","_kx"],ma=ti(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],ri),Hi="<masked>",D={campaignParams:function(){var{customTrackedParams:i,maskPersonalDataProperties:e,customPersonalDataProperties:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!S)return{};var n=e?ti([],ri,t||[]):[];return this._campaignParamsFromUrl(Bi(S.URL,n,Hi),i)},_campaignParamsFromUrl:function(i,e){var t=ma.concat(e||[]),n={};return A(t,function(r){var s=hi(i,r);n[r]=s||null}),n},_searchEngine:function(i){return i?i.search(Yt+"google.([^/?]*)")===0?"google":i.search(Yt+"bing.com")===0?"bing":i.search(Yt+"yahoo.com")===0?"yahoo":i.search(Yt+"duckduckgo.com")===0?"duckduckgo":null:null},_searchInfoFromReferrer:function(i){var e=D._searchEngine(i),t=e!="yahoo"?"q":"p",n={};if(!je(e)){n.$search_engine=e;var r=S?hi(S.referrer,t):"";r.length&&(n.ph_keyword=r)}return n},searchInfo:function(){var i=S==null?void 0:S.referrer;return i?this._searchInfoFromReferrer(i):{}},browser:Ir,browserVersion:function(i,e){var t=Ir(i,e),n=va[t];if(E(n))return null;for(var r=0;r<n.length;r++){var s=n[r],o=i.match(s);if(o)return parseFloat(o[o.length-2])}return null},browserLanguage:function(){return navigator.language||navigator.userLanguage},browserLanguagePrefix:function(){var i=this.browserLanguage();return typeof i=="string"?i.split("-")[0]:void 0},os:function(i){for(var e=0;e<Pr.length;e++){var[t,n]=Pr[e],r=t.exec(i),s=r&&(le(n)?n(r,i):n);if(s)return s}return["",""]},device:Cr,deviceType:function(i){var e=Cr(i);return e===Ms||e===As||e==="Kobo"||e==="Kindle Fire"||e===Vs?Bt:e===Ht||e===pt||e===jt||e===cn?"Console":e===Ds?"Wearable":e?ue:"Desktop"},referrer:function(){return(S==null?void 0:S.referrer)||"$direct"},referringDomain:function(){var i;return S!=null&&S.referrer&&((i=dt(S.referrer))===null||i===void 0?void 0:i.host)||"$direct"},referrerInfo:function(){return{$referrer:this.referrer(),$referring_domain:this.referringDomain()}},personInfo:function(){var{maskPersonalDataProperties:i,customPersonalDataProperties:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=i?ti([],ri,e||[]):[],n=ae==null?void 0:ae.href.substring(0,1e3);return{r:this.referrer().substring(0,1e3),u:n?Bi(n,t,Hi):void 0}},personPropsFromInfo:function(i){var e,{r:t,u:n}=i,r={$referrer:t,$referring_domain:t==null?void 0:t=="$direct"?"$direct":(e=dt(t))===null||e===void 0?void 0:e.host};if(n){r.$current_url=n;var s=dt(n);r.$host=s==null?void 0:s.host,r.$pathname=s==null?void 0:s.pathname;var o=this._campaignParamsFromUrl(n);G(r,o)}if(t){var a=this._searchInfoFromReferrer(t);G(r,a)}return r},initialPersonPropsFromInfo:function(i){var e=this.personPropsFromInfo(i),t={};return A(e,function(n,r){t["$initial_".concat(ks(r))]=n}),t},timezone:function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch{return}},timezoneOffset:function(){try{return new Date().getTimezoneOffset()}catch{return}},properties:function(){var{maskPersonalDataProperties:i,customPersonalDataProperties:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!H)return{};var t=i?ti([],ri,e||[]):[],[n,r]=D.os(H);return G(ai({$os:n,$os_version:r,$browser:D.browser(H,navigator.vendor),$device:D.device(H),$device_type:D.deviceType(H),$timezone:D.timezone(),$timezone_offset:D.timezoneOffset()}),{$current_url:Bi(ae==null?void 0:ae.href,t,Hi),$host:ae==null?void 0:ae.host,$pathname:ae==null?void 0:ae.pathname,$raw_user_agent:H.length>1e3?H.substring(0,997)+"...":H,$browser_version:D.browserVersion(H,navigator.vendor),$browser_language:D.browserLanguage(),$browser_language_prefix:D.browserLanguagePrefix(),$screen_height:g==null?void 0:g.screen.height,$screen_width:g==null?void 0:g.screen.width,$viewport_height:g==null?void 0:g.innerHeight,$viewport_width:g==null?void 0:g.innerWidth,$lib:"web",$lib_version:Ce.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})},people_properties:function(){if(!H)return{};var[i,e]=D.os(H);return G(ai({$os:i,$os_version:e,$browser:D.browser(H,navigator.vendor)}),{$browser_version:D.browserVersion(H,navigator.vendor)})}},ya=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class ji{constructor(e){this.config=e,this.props={},this.campaign_params_saved=!1,this.name=(t=>{var n="";return t.token&&(n=t.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),t.persistence_name?"ph_"+t.persistence_name:"ph_"+n+"_posthog"})(e),this.storage=this.buildStorage(e),this.load(),e.debug&&k.info("Persistence loaded",e.persistence,v({},this.props)),this.update_config(e,e),this.save()}buildStorage(e){ya.indexOf(e.persistence.toLowerCase())===-1&&(k.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return t==="localstorage"&&j.is_supported()?j:t==="localstorage+cookie"&&Vt.is_supported()?Vt:t==="sessionstorage"&&W.is_supported()?W:t==="memory"?oa:t==="cookie"?Re:Vt.is_supported()?Vt:Re}properties(){var e={};return A(this.props,function(t,n){if(n===ut&&B(t))for(var r=Object.keys(t),s=0;s<r.length;s++)e["$feature/".concat(r[s])]=t[r[s]];else a=n,l=!1,(je(o=Xo)?l:rr&&o.indexOf===rr?o.indexOf(a)!=-1:(A(o,function(u){if(l||(l=u===a))return oi}),l))||(e[n]=t);var o,a,l}),e}load(){if(!this.disabled){var e=this.storage.parse(this.name);e&&(this.props=G({},e))}}save(){this.disabled||this.storage.set(this.name,this.props,this.expire_days,this.cross_subdomain,this.secure,this.config.debug)}remove(){this.storage.remove(this.name,!1),this.storage.remove(this.name,!0)}clear(){this.remove(),this.props={}}register_once(e,t,n){if(B(e)){E(t)&&(t="None"),this.expire_days=E(n)?this.default_expiry:n;var r=!1;if(A(e,(s,o)=>{this.props.hasOwnProperty(o)&&this.props[o]!==t||(this.props[o]=s,r=!0)}),r)return this.save(),!0}return!1}register(e,t){if(B(e)){this.expire_days=E(t)?this.default_expiry:t;var n=!1;if(A(e,(r,s)=>{e.hasOwnProperty(s)&&this.props[s]!==r&&(this.props[s]=r,n=!0)}),n)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.campaign_params_saved){var e=D.campaignParams({customTrackedParams:this.config.custom_campaign_params,maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});ct(ai(e))||this.register(e),this.campaign_params_saved=!0}}update_search_keyword(){this.register(D.searchInfo())}update_referrer_info(){this.register_once(D.referrerInfo(),void 0)}set_initial_person_info(){this.props[sn]||this.props[on]||this.register_once({[an]:D.personInfo({maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties})},void 0)}get_referrer_info(){return ai({$referrer:this.props.$referrer,$referring_domain:this.props.$referring_domain})}get_initial_props(){var e={};A([on,sn],r=>{var s=this.props[r];s&&A(s,function(o,a){e["$initial_"+ks(a)]=o})});var t=this.props[an];if(t){var n=D.initialPersonPropsFromInfo(t);G(e,n)}return e}safe_merge(e){return A(this.props,function(t,n){n in e||(e[n]=t)}),e}update_config(e,t){if(this.default_expiry=this.expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var n=this.buildStorage(e),r=this.props;this.clear(),this.storage=n,this.props=r,this.save()}}set_disabled(e){this.disabled=e,this.disabled?this.remove():this.save()}set_cross_subdomain(e){e!==this.cross_subdomain&&(this.cross_subdomain=e,this.remove(),this.save())}get_cross_subdomain(){return!!this.cross_subdomain}set_secure(e){e!==this.secure&&(this.secure=e,this.remove(),this.save())}set_event_timer(e,t){var n=this.props[Ct]||{};n[e]=t,this.props[Ct]=n,this.save()}remove_event_timer(e){var t=(this.props[Ct]||{})[e];return E(t)||(delete this.props[Ct][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}function gi(i){var e,t;return((e=JSON.stringify(i,(t=[],function(n,r){if(B(r)){for(;t.length>0&&t[t.length-1]!==this;)t.pop();return t.includes(r)?"[Circular]":(t.push(r),r)}return r})))===null||e===void 0?void 0:e.length)||0}function dn(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:66060288e-1;if(i.size>=e&&i.data.length>1){var t=Math.floor(i.data.length/2),n=i.data.slice(0,t),r=i.data.slice(t);return[dn({size:gi(n),data:n,sessionId:i.sessionId,windowId:i.windowId}),dn({size:gi(r),data:r,sessionId:i.sessionId,windowId:i.windowId})].flatMap(s=>s)}return[i]}var xe=(i=>(i[i.DomContentLoaded=0]="DomContentLoaded",i[i.Load=1]="Load",i[i.FullSnapshot=2]="FullSnapshot",i[i.IncrementalSnapshot=3]="IncrementalSnapshot",i[i.Meta=4]="Meta",i[i.Custom=5]="Custom",i[i.Plugin=6]="Plugin",i))(xe||{}),ge=(i=>(i[i.Mutation=0]="Mutation",i[i.MouseMove=1]="MouseMove",i[i.MouseInteraction=2]="MouseInteraction",i[i.Scroll=3]="Scroll",i[i.ViewportResize=4]="ViewportResize",i[i.Input=5]="Input",i[i.TouchMove=6]="TouchMove",i[i.MediaInteraction=7]="MediaInteraction",i[i.StyleSheetRule=8]="StyleSheetRule",i[i.CanvasMutation=9]="CanvasMutation",i[i.Font=10]="Font",i[i.Log=11]="Log",i[i.Drag=12]="Drag",i[i.StyleDeclaration=13]="StyleDeclaration",i[i.Selection=14]="Selection",i[i.AdoptedStyleSheet=15]="AdoptedStyleSheet",i[i.CustomElement=16]="CustomElement",i))(ge||{});function Tr(i){var e;return i instanceof Element&&(i.id===Fs||!((e=i.closest)===null||e===void 0||!e.call(i,".toolbar-global-fade-container")))}function xi(i){return!!i&&i.nodeType===1}function Be(i,e){return!!i&&!!i.tagName&&i.tagName.toLowerCase()===e.toLowerCase()}function Js(i){return!!i&&i.nodeType===3}function Ys(i){return!!i&&i.nodeType===11}function $n(i){return i?Ei(i).split(/\s+/):[]}function Rr(i){var e=g==null?void 0:g.location.href;return!!(e&&i&&i.some(t=>e.match(t)))}function fi(i){var e="";switch(typeof i.className){case"string":e=i.className;break;case"object":e=(i.className&&"baseVal"in i.className?i.className.baseVal:null)||i.getAttribute("class")||"";break;default:e=""}return $n(e)}function Ks(i){return F(i)?null:Ei(i).split(/(\s+)/).filter(e=>ft(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function Ii(i){var e="";return pn(i)&&!Zs(i)&&i.childNodes&&i.childNodes.length&&A(i.childNodes,function(t){var n;Js(t)&&t.textContent&&(e+=(n=Ks(t.textContent))!==null&&n!==void 0?n:"")}),Ei(e)}function Xs(i){return E(i.target)?i.srcElement||null:(e=i.target)!==null&&e!==void 0&&e.shadowRoot?i.composedPath()[0]||null:i.target||null;var e}var hn=["a","button","form","input","select","textarea","label"];function Qs(i){var e=i.parentNode;return!(!e||!xi(e))&&e}function ba(i,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;if(!g||!i||Be(i,"html")||!xi(i)||t!=null&&t.url_allowlist&&!Rr(t.url_allowlist)||t!=null&&t.url_ignorelist&&Rr(t.url_ignorelist))return!1;if(t!=null&&t.dom_event_allowlist){var s=t.dom_event_allowlist;if(s&&!s.some(h=>e.type===h))return!1}for(var o=!1,a=[i],l=!0,u=i;u.parentNode&&!Be(u,"body");)if(Ys(u.parentNode))a.push(u.parentNode.host),u=u.parentNode.host;else{if(!(l=Qs(u)))break;if(n||hn.indexOf(l.tagName.toLowerCase())>-1)o=!0;else{var d=g.getComputedStyle(l);d&&d.getPropertyValue("cursor")==="pointer"&&(o=!0)}a.push(l),u=l}if(!function(h,_){var f=_==null?void 0:_.element_allowlist;if(E(f))return!0;var b=function(x){if(f.some(I=>x.tagName.toLowerCase()===I))return{v:!0}};for(var m of h){var w=b(m);if(typeof w=="object")return w.v}return!1}(a,t)||!function(h,_){var f=_==null?void 0:_.css_selector_allowlist;if(E(f))return!0;var b=function(x){if(f.some(I=>x.matches(I)))return{v:!0}};for(var m of h){var w=b(m);if(typeof w=="object")return w.v}return!1}(a,t))return!1;var c=g.getComputedStyle(i);if(c&&c.getPropertyValue("cursor")==="pointer"&&e.type==="click")return!0;var p=i.tagName.toLowerCase();switch(p){case"html":return!1;case"form":return(r||["submit"]).indexOf(e.type)>=0;case"input":case"select":case"textarea":return(r||["change","click"]).indexOf(e.type)>=0;default:return o?(r||["click"]).indexOf(e.type)>=0:(r||["click"]).indexOf(e.type)>=0&&(hn.indexOf(p)>-1||i.getAttribute("contenteditable")==="true")}}function pn(i){for(var e=i;e.parentNode&&!Be(e,"body");e=e.parentNode){var t=fi(e);if(T(t,"ph-sensitive")||T(t,"ph-no-capture"))return!1}if(T(fi(i),"ph-include"))return!0;var n=i.type||"";if(V(n))switch(n.toLowerCase()){case"hidden":case"password":return!1}var r=i.name||i.id||"";return!(V(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function Zs(i){return!!(Be(i,"input")&&!["button","checkbox","submit","reset"].includes(i.type)||Be(i,"select")||Be(i,"textarea")||i.getAttribute("contenteditable")==="true")}var eo="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",wa=new RegExp("^(?:".concat(eo,")$")),Sa=new RegExp(eo),to="\\d{3}-?\\d{2}-?\\d{4}",Ea=new RegExp("^(".concat(to,")$")),ka=new RegExp("(".concat(to,")"));function ft(i){var e=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];return!(F(i)||V(i)&&(i=Ei(i),(e?wa:Sa).test((i||"").replace(/[- ]/g,""))||(e?Ea:ka).test(i)))}function io(i){var e=Ii(i);return ft(e="".concat(e," ").concat(no(i)).trim())?e:""}function no(i){var e="";return i&&i.childNodes&&i.childNodes.length&&A(i.childNodes,function(t){var n;if(t&&((n=t.tagName)===null||n===void 0?void 0:n.toLowerCase())==="span")try{var r=Ii(t);e="".concat(e," ").concat(r).trim(),t.childNodes&&t.childNodes.length&&(e="".concat(e," ").concat(no(t)).trim())}catch(s){k.error("[AutoCapture]",s)}}),e}function xa(i){return function(e){var t=e.map(n=>{var r,s,o="";if(n.tag_name&&(o+=n.tag_name),n.attr_class)for(var a of(n.attr_class.sort(),n.attr_class))o+=".".concat(a.replace(/"/g,""));var l=v(v(v(v({},n.text?{text:n.text}:{}),{},{"nth-child":(r=n.nth_child)!==null&&r!==void 0?r:0,"nth-of-type":(s=n.nth_of_type)!==null&&s!==void 0?s:0},n.href?{href:n.href}:{}),n.attr_id?{attr_id:n.attr_id}:{}),n.attributes),u={};return ii(l).sort((d,c)=>{var[p]=d,[h]=c;return p.localeCompare(h)}).forEach(d=>{var[c,p]=d;return u[Fr(c.toString())]=Fr(p.toString())}),o+=":",o+=ii(l).map(d=>{var[c,p]=d;return"".concat(c,'="').concat(p,'"')}).join("")});return t.join(";")}(function(e){return e.map(t=>{var n,r,s={text:(n=t.$el_text)===null||n===void 0?void 0:n.slice(0,400),tag_name:t.tag_name,href:(r=t.attr__href)===null||r===void 0?void 0:r.slice(0,2048),attr_class:Ia(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return ii(t).filter(o=>{var[a]=o;return a.indexOf("attr__")===0}).forEach(o=>{var[a,l]=o;return s.attributes[a]=l}),s})}(i))}function Fr(i){return i.replace(/"|\\"/g,'\\"')}function Ia(i){var e=i.attr__class;return e?q(e)?e:$n(e):void 0}var _n="[SessionRecording]",gn="redacted",Kt={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:i=>i,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com"]},Pa=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],Ca=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],Ta=["/s/","/e/","/i/"];function Or(i,e,t,n){if(F(i))return i;var r=(e==null?void 0:e["content-length"])||function(s){return new Blob([s]).size}(i);return V(r)&&(r=parseInt(r)),r>t?_n+" ".concat(n," body too large to record (").concat(r," bytes)"):i}function Ar(i,e){if(F(i))return i;var t=i;return ft(t,!1)||(t=_n+" "+e+" body "+gn),A(Ca,n=>{var r,s;(r=t)!==null&&r!==void 0&&r.length&&((s=t)===null||s===void 0?void 0:s.indexOf(n))!==-1&&(t=_n+" "+e+" body "+gn+" as might contain: "+n)}),t}var Ra=(i,e)=>{var t,n,r,s={payloadSizeLimitBytes:Kt.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...Kt.performanceEntryTypeToObserve],payloadHostDenyList:[...e.payloadHostDenyList||[],...Kt.payloadHostDenyList]},o=i.session_recording.recordHeaders!==!1&&e.recordHeaders,a=i.session_recording.recordBody!==!1&&e.recordBody,l=i.capture_performance!==!1&&e.recordPerformance,u=(t=s,r=Math.min(1e6,(n=t.payloadSizeLimitBytes)!==null&&n!==void 0?n:1e6),p=>(p!=null&&p.requestBody&&(p.requestBody=Or(p.requestBody,p.requestHeaders,r,"Request")),p!=null&&p.responseBody&&(p.responseBody=Or(p.responseBody,p.responseHeaders,r,"Response")),p)),d=p=>{return u(((f,b)=>{var m,w=dt(f.name),x=b.indexOf("http")===0?(m=dt(b))===null||m===void 0?void 0:m.pathname:b;x==="/"&&(x="");var I=w==null?void 0:w.pathname.replace(x||"","");if(!(w&&I&&Ta.some(R=>I.indexOf(R)===0)))return f})((_=(h=p).requestHeaders,F(_)||A(Object.keys(_??{}),f=>{Pa.includes(f.toLowerCase())&&(_[f]=gn)}),h),i.api_host));var h,_},c=le(i.session_recording.maskNetworkRequestFn);return c&&le(i.session_recording.maskCapturedNetworkRequestFn)&&k.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),c&&(i.session_recording.maskCapturedNetworkRequestFn=p=>{var h=i.session_recording.maskNetworkRequestFn({url:p.name});return v(v({},p),{},{name:h==null?void 0:h.url})}),s.maskRequestFn=le(i.session_recording.maskCapturedNetworkRequestFn)?p=>{var h,_,f,b=d(p);return b&&(h=(_=(f=i.session_recording).maskCapturedNetworkRequestFn)===null||_===void 0?void 0:_.call(f,b))!==null&&h!==void 0?h:void 0}:p=>function(h){if(!E(h))return h.requestBody=Ar(h.requestBody,"Request"),h.responseBody=Ar(h.responseBody,"Response"),h}(d(p)),v(v(v({},Kt),s),{},{recordHeaders:o,recordBody:a,recordPerformance:l,recordInitialRequests:l})};function ce(i,e,t,n,r){return e>t&&(k.warn("min cannot be greater than max."),e=t),Z(i)?i>t?(n&&k.warn(n+" cannot be  greater than max: "+t+". Using max value instead."),t):i<e?(n&&k.warn(n+" cannot be less than min: "+e+". Using min value instead."),e):i:(n&&k.warn(n+" must be a number. using max or fallback. max: "+t+", fallback: "+r),ce(r||t,e,t,n))}class Fa{constructor(e){var t,n,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};y(this,"bucketSize",100),y(this,"refillRate",10),y(this,"mutationBuckets",{}),y(this,"loggedTracker",{}),y(this,"refillBuckets",()=>{Object.keys(this.mutationBuckets).forEach(s=>{this.mutationBuckets[s]=this.mutationBuckets[s]+this.refillRate,this.mutationBuckets[s]>=this.bucketSize&&delete this.mutationBuckets[s]})}),y(this,"getNodeOrRelevantParent",s=>{var o=this.rrweb.mirror.getNode(s);if((o==null?void 0:o.nodeName)!=="svg"&&o instanceof Element){var a=o.closest("svg");if(a)return[this.rrweb.mirror.getId(a),a]}return[s,o]}),y(this,"numberOfChanges",s=>{var o,a,l,u,d,c,p,h;return((o=(a=s.removes)===null||a===void 0?void 0:a.length)!==null&&o!==void 0?o:0)+((l=(u=s.attributes)===null||u===void 0?void 0:u.length)!==null&&l!==void 0?l:0)+((d=(c=s.texts)===null||c===void 0?void 0:c.length)!==null&&d!==void 0?d:0)+((p=(h=s.adds)===null||h===void 0?void 0:h.length)!==null&&p!==void 0?p:0)}),y(this,"throttleMutations",s=>{if(s.type!==3||s.data.source!==0)return s;var o=s.data,a=this.numberOfChanges(o);o.attributes&&(o.attributes=o.attributes.filter(u=>{var d,c,p,[h,_]=this.getNodeOrRelevantParent(u.id);return this.mutationBuckets[h]===0?!1:(this.mutationBuckets[h]=(d=this.mutationBuckets[h])!==null&&d!==void 0?d:this.bucketSize,this.mutationBuckets[h]=Math.max(this.mutationBuckets[h]-1,0),this.mutationBuckets[h]===0&&(this.loggedTracker[h]||(this.loggedTracker[h]=!0,(c=(p=this.options).onBlockedNode)===null||c===void 0||c.call(p,h,_))),u)}));var l=this.numberOfChanges(o);return l!==0||a===l?s:void 0}),this.rrweb=e,this.options=r,this.refillRate=ce((t=this.options.refillRate)!==null&&t!==void 0?t:this.refillRate,0,100,"mutation throttling refill rate"),this.bucketSize=ce((n=this.options.bucketSize)!==null&&n!==void 0?n:this.bucketSize,0,100,"mutation throttling bucket size"),setInterval(()=>{this.refillBuckets()},1e3)}}var de=Uint8Array,ee=Uint16Array,vt=Uint32Array,Dn=new de([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Ln=new de([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Mr=new de([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ro=function(i,e){for(var t=new ee(31),n=0;n<31;++n)t[n]=e+=1<<i[n-1];var r=new vt(t[30]);for(n=1;n<30;++n)for(var s=t[n];s<t[n+1];++s)r[s]=s-t[n]<<5|n;return[t,r]},so=ro(Dn,2),Oa=so[0],fn=so[1];Oa[28]=258,fn[258]=28;for(var $r=ro(Ln,0)[1],vn=new ee(32768),M=0;M<32768;++M){var We=(43690&M)>>>1|(21845&M)<<1;We=(61680&(We=(52428&We)>>>2|(13107&We)<<2))>>>4|(3855&We)<<4,vn[M]=((65280&We)>>>8|(255&We)<<8)>>>1}var Mt=function(i,e,t){for(var n=i.length,r=0,s=new ee(e);r<n;++r)++s[i[r]-1];var o,a=new ee(e);for(r=0;r<e;++r)a[r]=a[r-1]+s[r-1]<<1;if(t){o=new ee(1<<e);var l=15-e;for(r=0;r<n;++r)if(i[r])for(var u=r<<4|i[r],d=e-i[r],c=a[i[r]-1]++<<d,p=c|(1<<d)-1;c<=p;++c)o[vn[c]>>>l]=u}else for(o=new ee(n),r=0;r<n;++r)o[r]=vn[a[i[r]-1]++]>>>15-i[r];return o},nt=new de(288);for(M=0;M<144;++M)nt[M]=8;for(M=144;M<256;++M)nt[M]=9;for(M=256;M<280;++M)nt[M]=7;for(M=280;M<288;++M)nt[M]=8;var vi=new de(32);for(M=0;M<32;++M)vi[M]=5;var Aa=Mt(nt,9,0),Ma=Mt(vi,5,0),oo=function(i){return(i/8>>0)+(7&i&&1)},ao=function(i,e,t){(t==null||t>i.length)&&(t=i.length);var n=new(i instanceof ee?ee:i instanceof vt?vt:de)(t-e);return n.set(i.subarray(e,t)),n},Ee=function(i,e,t){t<<=7&e;var n=e/8>>0;i[n]|=t,i[n+1]|=t>>>8},kt=function(i,e,t){t<<=7&e;var n=e/8>>0;i[n]|=t,i[n+1]|=t>>>8,i[n+2]|=t>>>16},zi=function(i,e){for(var t=[],n=0;n<i.length;++n)i[n]&&t.push({s:n,f:i[n]});var r=t.length,s=t.slice();if(!r)return[new de(0),0];if(r==1){var o=new de(t[0].s+1);return o[t[0].s]=1,[o,1]}t.sort(function(R,O){return R.f-O.f}),t.push({s:-1,f:25001});var a=t[0],l=t[1],u=0,d=1,c=2;for(t[0]={s:-1,f:a.f+l.f,l:a,r:l};d!=r-1;)a=t[t[u].f<t[c].f?u++:c++],l=t[u!=d&&t[u].f<t[c].f?u++:c++],t[d++]={s:-1,f:a.f+l.f,l:a,r:l};var p=s[0].s;for(n=1;n<r;++n)s[n].s>p&&(p=s[n].s);var h=new ee(p+1),_=mn(t[d-1],h,0);if(_>e){n=0;var f=0,b=_-e,m=1<<b;for(s.sort(function(R,O){return h[O.s]-h[R.s]||R.f-O.f});n<r;++n){var w=s[n].s;if(!(h[w]>e))break;f+=m-(1<<_-h[w]),h[w]=e}for(f>>>=b;f>0;){var x=s[n].s;h[x]<e?f-=1<<e-h[x]++-1:++n}for(;n>=0&&f;--n){var I=s[n].s;h[I]==e&&(--h[I],++f)}_=e}return[new de(h),_]},mn=function(i,e,t){return i.s==-1?Math.max(mn(i.l,e,t+1),mn(i.r,e,t+1)):e[i.s]=t},Dr=function(i){for(var e=i.length;e&&!i[--e];);for(var t=new ee(++e),n=0,r=i[0],s=1,o=function(l){t[n++]=l},a=1;a<=e;++a)if(i[a]==r&&a!=e)++s;else{if(!r&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(r),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(r);s=1,r=i[a]}return[t.subarray(0,n),e]},xt=function(i,e){for(var t=0,n=0;n<e.length;++n)t+=i[n]*e[n];return t},yn=function(i,e,t){var n=t.length,r=oo(e+2);i[r]=255&n,i[r+1]=n>>>8,i[r+2]=255^i[r],i[r+3]=255^i[r+1];for(var s=0;s<n;++s)i[r+s+4]=t[s];return 8*(r+4+n)},Lr=function(i,e,t,n,r,s,o,a,l,u,d){Ee(e,d++,t),++r[256];for(var c=zi(r,15),p=c[0],h=c[1],_=zi(s,15),f=_[0],b=_[1],m=Dr(p),w=m[0],x=m[1],I=Dr(f),R=I[0],O=I[1],L=new ee(19),C=0;C<w.length;++C)L[31&w[C]]++;for(C=0;C<R.length;++C)L[31&R[C]]++;for(var $=zi(L,7),z=$[0],pe=$[1],re=19;re>4&&!z[Mr[re-1]];--re);var Se,ve,me,rt,Fe=u+5<<3,ze=xt(r,nt)+xt(s,vi)+o,ye=xt(r,p)+xt(s,f)+o+14+3*re+xt(L,z)+(2*L[16]+3*L[17]+7*L[18]);if(Fe<=ze&&Fe<=ye)return yn(e,d,i.subarray(l,l+u));if(Ee(e,d,1+(ye<ze)),d+=2,ye<ze){Se=Mt(p,h,0),ve=p,me=Mt(f,b,0),rt=f;var Oe=Mt(z,pe,0);for(Ee(e,d,x-257),Ee(e,d+5,O-1),Ee(e,d+10,re-4),d+=14,C=0;C<re;++C)Ee(e,d+3*C,z[Mr[C]]);d+=3*re;for(var st=[w,R],X=0;X<2;++X){var se=st[X];for(C=0;C<se.length;++C){var ie=31&se[C];Ee(e,d,Oe[ie]),d+=z[ie],ie>15&&(Ee(e,d,se[C]>>>5&127),d+=se[C]>>>12)}}}else Se=Aa,ve=nt,me=Ma,rt=vi;for(C=0;C<a;++C)if(n[C]>255){ie=n[C]>>>18&31,kt(e,d,Se[ie+257]),d+=ve[ie+257],ie>7&&(Ee(e,d,n[C]>>>23&31),d+=Dn[ie]);var ot=31&n[C];kt(e,d,me[ot]),d+=rt[ot],ot>3&&(kt(e,d,n[C]>>>5&8191),d+=Ln[ot])}else kt(e,d,Se[n[C]]),d+=ve[n[C]];return kt(e,d,Se[256]),d+ve[256]},$a=new vt([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Da=function(){for(var i=new vt(256),e=0;e<256;++e){for(var t=e,n=9;--n;)t=(1&t&&3988292384)^t>>>1;i[e]=t}return i}(),La=function(){var i=4294967295;return{p:function(e){for(var t=i,n=0;n<e.length;++n)t=Da[255&t^e[n]]^t>>>8;i=t},d:function(){return 4294967295^i}}},Na=function(i,e,t,n,r){return function(s,o,a,l,u,d){var c=s.length,p=new de(l+c+5*(1+Math.floor(c/7e3))+u),h=p.subarray(l,p.length-u),_=0;if(!o||c<8)for(var f=0;f<=c;f+=65535){var b=f+65535;b<c?_=yn(h,_,s.subarray(f,b)):(h[f]=d,_=yn(h,_,s.subarray(f,c)))}else{for(var m=$a[o-1],w=m>>>13,x=8191&m,I=(1<<a)-1,R=new ee(32768),O=new ee(I+1),L=Math.ceil(a/3),C=2*L,$=function(Ai){return(s[Ai]^s[Ai+1]<<L^s[Ai+2]<<C)&I},z=new vt(25e3),pe=new ee(288),re=new ee(32),Se=0,ve=0,me=(f=0,0),rt=0,Fe=0;f<c;++f){var ze=$(f),ye=32767&f,Oe=O[ze];if(R[ye]=Oe,O[ze]=ye,rt<=f){var st=c-f;if((Se>7e3||me>24576)&&st>423){_=Lr(s,h,0,z,pe,re,ve,me,Fe,f-Fe,_),me=Se=ve=0,Fe=f;for(var X=0;X<286;++X)pe[X]=0;for(X=0;X<30;++X)re[X]=0}var se=2,ie=0,ot=x,Ae=ye-Oe&32767;if(st>2&&ze==$(f-Ae))for(var No=Math.min(w,st)-1,qo=Math.min(32767,f),Bo=Math.min(258,st);Ae<=qo&&--ot&&ye!=Oe;){if(s[f+se]==s[f+se-Ae]){for(var Me=0;Me<Bo&&s[f+Me]==s[f+Me-Ae];++Me);if(Me>se){if(se=Me,ie=Ae,Me>No)break;var Uo=Math.min(Ae,Me-2),Zn=0;for(X=0;X<Uo;++X){var Oi=f-Ae+X+32768&32767,er=Oi-R[Oi]+32768&32767;er>Zn&&(Zn=er,Oe=Oi)}}}Ae+=(ye=Oe)-(Oe=R[ye])+32768&32767}if(ie){z[me++]=268435456|fn[se]<<18|$r[ie];var tr=31&fn[se],ir=31&$r[ie];ve+=Dn[tr]+Ln[ir],++pe[257+tr],++re[ir],rt=f+se,++Se}else z[me++]=s[f],++pe[s[f]]}}_=Lr(s,h,d,z,pe,re,ve,me,Fe,f-Fe,_)}return ao(p,0,l+oo(_)+u)}(i,e.level==null?6:e.level,e.mem==null?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(i.length)))):12+e.mem,t,n,!r)},bn=function(i,e,t){for(;t;++e)i[e]=t,t>>>=8},qa=function(i,e){var t=e.filename;if(i[0]=31,i[1]=139,i[2]=8,i[8]=e.level<2?4:e.level==9?2:0,i[9]=3,e.mtime!=0&&bn(i,4,Math.floor(new Date(e.mtime||Date.now())/1e3)),t){i[3]=8;for(var n=0;n<=t.length;++n)i[n+10]=t.charCodeAt(n)}},Ba=function(i){return 10+(i.filename&&i.filename.length+1||0)};function lo(i,e){e===void 0&&(e={});var t=La(),n=i.length;t.p(i);var r=Na(i,e,Ba(e),8),s=r.length;return qa(r,e),bn(r,s-8,t.d()),bn(r,s-4,n),r}function co(i,e){var t=i.length;if(typeof TextEncoder<"u")return new TextEncoder().encode(i);for(var n=new de(i.length+(i.length>>>1)),r=0,s=function(u){n[r++]=u},o=0;o<t;++o){if(r+5>n.length){var a=new de(r+8+(t-o<<1));a.set(n),n=a}var l=i.charCodeAt(o);l<128||e?s(l):l<2048?(s(192|l>>>6),s(128|63&l)):l>55295&&l<57344?(s(240|(l=65536+(1047552&l)|1023&i.charCodeAt(++o))>>>18),s(128|l>>>12&63),s(128|l>>>6&63),s(128|63&l)):(s(224|l>>>12),s(128|l>>>6&63),s(128|63&l))}return ao(n,0,r)}function Ua(i,e){return function(t){for(var n=0,r=0;r<t.length;r++)n=(n<<5)-n+t.charCodeAt(r),n|=0;return Math.abs(n)}(i)%100<ce(100*e,0,100)}var Ot="[SessionRecording]",J=K(Ot),Ha=3e5,ja=[ge.MouseMove,ge.MouseInteraction,ge.Scroll,ge.ViewportResize,ge.Input,ge.TouchMove,ge.MediaInteraction,ge.Drag],Nr=i=>({rrwebMethod:i,enqueuedAt:Date.now(),attempt:1});function Ve(i){return function(e,t){for(var n="",r=0;r<e.length;){var s=e[r++];s<128||t?n+=String.fromCharCode(s):s<224?n+=String.fromCharCode((31&s)<<6|63&e[r++]):s<240?n+=String.fromCharCode((15&s)<<12|(63&e[r++])<<6|63&e[r++]):(s=((15&s)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,n+=String.fromCharCode(55296|s>>10,56320|1023&s))}return n}(lo(co(JSON.stringify(i))),!0)}function qr(i){return i.type===xe.Custom&&i.data.tag==="sessionIdle"}function Br(i,e){return e.some(t=>t.matching==="regex"&&new RegExp(t.url).test(i))}class za{get sessionIdleThresholdMilliseconds(){return this.instance.config.session_recording.session_idle_threshold_ms||3e5}get rrwebRecord(){var e,t;return P==null||(e=P.__PosthogExtensions__)===null||e===void 0||(t=e.rrweb)===null||t===void 0?void 0:t.record}get started(){return this._captureStarted}get sessionManager(){if(!this.instance.sessionManager)throw new Error(Ot+" must be started with a valid sessionManager.");return this.instance.sessionManager}get fullSnapshotIntervalMillis(){var e,t;return this.triggerStatus==="trigger_pending"?6e4:(e=(t=this.instance.config.session_recording)===null||t===void 0?void 0:t.full_snapshot_interval_millis)!==null&&e!==void 0?e:Ha}get isSampled(){var e=this.instance.get_property(Tt);return De(e)?e:null}get sessionDuration(){var e,t,n=(e=this.buffer)===null||e===void 0?void 0:e.data[((t=this.buffer)===null||t===void 0?void 0:t.data.length)-1],{sessionStartTimestamp:r}=this.sessionManager.checkAndGetSessionAndWindowId(!0);return n?n.timestamp-r:null}get isRecordingEnabled(){var e=!!this.instance.get_property(en),t=!this.instance.config.disable_session_recording;return g&&e&&t}get isConsoleLogCaptureEnabled(){var e=!!this.instance.get_property(_r),t=this.instance.config.enable_recording_console_log;return t??e}get canvasRecording(){var e,t,n,r,s,o,a=this.instance.config.session_recording.captureCanvas,l=this.instance.get_property(fr),u=(e=(t=a==null?void 0:a.recordCanvas)!==null&&t!==void 0?t:l==null?void 0:l.enabled)!==null&&e!==void 0&&e,d=(n=(r=a==null?void 0:a.canvasFps)!==null&&r!==void 0?r:l==null?void 0:l.fps)!==null&&n!==void 0?n:4,c=(s=(o=a==null?void 0:a.canvasQuality)!==null&&o!==void 0?o:l==null?void 0:l.quality)!==null&&s!==void 0?s:.4;if(typeof c=="string"){var p=parseFloat(c);c=isNaN(p)?.4:p}return{enabled:u,fps:ce(d,0,12,"canvas recording fps",4),quality:ce(c,0,1,"canvas recording quality",.4)}}get networkPayloadCapture(){var e,t,n=this.instance.get_property(gr),r={recordHeaders:(e=this.instance.config.session_recording)===null||e===void 0?void 0:e.recordHeaders,recordBody:(t=this.instance.config.session_recording)===null||t===void 0?void 0:t.recordBody},s=(r==null?void 0:r.recordHeaders)||(n==null?void 0:n.recordHeaders),o=(r==null?void 0:r.recordBody)||(n==null?void 0:n.recordBody),a=B(this.instance.config.capture_performance)?this.instance.config.capture_performance.network_timing:this.instance.config.capture_performance,l=!!(De(a)?a:n!=null&&n.capturePerformance);return s||o||l?{recordHeaders:s,recordBody:o,recordPerformance:l}:void 0}get sampleRate(){var e=this.instance.get_property(vr);return Z(e)?e:null}get minimumDuration(){var e=this.instance.get_property(mr);return Z(e)?e:null}get status(){return this.receivedDecide?this.isRecordingEnabled?this.isSampled===!1?"disabled":this._urlBlocked?"paused":F(this._linkedFlag)||this._linkedFlagSeen?this.triggerStatus==="trigger_pending"?"buffering":De(this.isSampled)?this.isSampled?"sampled":"disabled":"active":"buffering":"disabled":"buffering"}get urlTriggerStatus(){var e;return this._urlTriggers.length===0?"trigger_disabled":((e=this.instance)===null||e===void 0?void 0:e.get_property(Di))===this.sessionId?"trigger_activated":"trigger_pending"}get eventTriggerStatus(){var e;return this._eventTriggers.length===0?"trigger_disabled":((e=this.instance)===null||e===void 0?void 0:e.get_property(Li))===this.sessionId?"trigger_activated":"trigger_pending"}get triggerStatus(){var e=this.eventTriggerStatus==="trigger_activated"||this.urlTriggerStatus==="trigger_activated",t=this.eventTriggerStatus==="trigger_pending"||this.urlTriggerStatus==="trigger_pending";return e?"trigger_activated":t?"trigger_pending":"trigger_disabled"}constructor(e){if(y(this,"queuedRRWebEvents",[]),y(this,"isIdle",!1),y(this,"_linkedFlagSeen",!1),y(this,"_lastActivityTimestamp",Date.now()),y(this,"_linkedFlag",null),y(this,"_removePageViewCaptureHook",void 0),y(this,"_onSessionIdListener",void 0),y(this,"_persistDecideOnSessionListener",void 0),y(this,"_samplingSessionListener",void 0),y(this,"_urlTriggers",[]),y(this,"_urlBlocklist",[]),y(this,"_urlBlocked",!1),y(this,"_eventTriggers",[]),y(this,"_removeEventTriggerCaptureHook",void 0),y(this,"_forceAllowLocalhostNetworkCapture",!1),y(this,"_onBeforeUnload",()=>{this._flushBuffer()}),y(this,"_onOffline",()=>{this._tryAddCustomEvent("browser offline",{})}),y(this,"_onOnline",()=>{this._tryAddCustomEvent("browser online",{})}),y(this,"_onVisibilityChange",()=>{if(S!=null&&S.visibilityState){var r="window "+S.visibilityState;this._tryAddCustomEvent(r,{})}}),this.instance=e,this._captureStarted=!1,this._endpoint="/s/",this.stopRrweb=void 0,this.receivedDecide=!1,!this.instance.sessionManager)throw J.error("started without valid sessionManager"),new Error(Ot+" started without valid sessionManager. This is a bug.");if(this.instance.config.__preview_experimental_cookieless_mode)throw new Error(Ot+" cannot be used with __preview_experimental_cookieless_mode.");var{sessionId:t,windowId:n}=this.sessionManager.checkAndGetSessionAndWindowId();this.sessionId=t,this.windowId=n,this.buffer=this.clearBuffer(),this.sessionIdleThresholdMilliseconds>=this.sessionManager.sessionTimeoutMs&&J.warn("session_idle_threshold_ms (".concat(this.sessionIdleThresholdMilliseconds,") is greater than the session timeout (").concat(this.sessionManager.sessionTimeoutMs,"). Session will never be detected as idle"))}startIfEnabledOrStop(e){this.isRecordingEnabled?(this._startCapture(e),U(g,"beforeunload",this._onBeforeUnload),U(g,"offline",this._onOffline),U(g,"online",this._onOnline),U(g,"visibilitychange",this._onVisibilityChange),this._setupSampling(),this._addEventTriggerListener(),F(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this.instance.on("eventCaptured",t=>{try{if(t.event==="$pageview"){var n=t!=null&&t.properties.$current_url?this._maskUrl(t==null?void 0:t.properties.$current_url):"";if(!n)return;this._tryAddCustomEvent("$pageview",{href:n})}}catch(r){J.error("Could not add $pageview to rrweb session",r)}})),this._onSessionIdListener||(this._onSessionIdListener=this.sessionManager.onSessionId((t,n,r)=>{var s,o,a,l;r&&(this._tryAddCustomEvent("$session_id_change",{sessionId:t,windowId:n,changeReason:r}),(s=this.instance)===null||s===void 0||(o=s.persistence)===null||o===void 0||o.unregister(Li),(a=this.instance)===null||a===void 0||(l=a.persistence)===null||l===void 0||l.unregister(Di))}))):this.stopRecording()}stopRecording(){var e,t,n,r;this._captureStarted&&this.stopRrweb&&(this.stopRrweb(),this.stopRrweb=void 0,this._captureStarted=!1,g==null||g.removeEventListener("beforeunload",this._onBeforeUnload),g==null||g.removeEventListener("offline",this._onOffline),g==null||g.removeEventListener("online",this._onOnline),g==null||g.removeEventListener("visibilitychange",this._onVisibilityChange),this.clearBuffer(),clearInterval(this._fullSnapshotTimer),(e=this._removePageViewCaptureHook)===null||e===void 0||e.call(this),this._removePageViewCaptureHook=void 0,(t=this._removeEventTriggerCaptureHook)===null||t===void 0||t.call(this),this._removeEventTriggerCaptureHook=void 0,(n=this._onSessionIdListener)===null||n===void 0||n.call(this),this._onSessionIdListener=void 0,(r=this._samplingSessionListener)===null||r===void 0||r.call(this),this._samplingSessionListener=void 0,J.info("stopped"))}makeSamplingDecision(e){var t,n=this.sessionId!==e,r=this.sampleRate;if(Z(r)){var s=this.isSampled,o=n||!De(s),a=o?Ua(e,r):s;o&&(a?this._reportStarted("sampled"):J.warn("Sample rate (".concat(r,") has determined that this sessionId (").concat(e,") will not be sent to the server.")),this._tryAddCustomEvent("samplingDecisionMade",{sampleRate:r,isSampled:a})),(t=this.instance.persistence)===null||t===void 0||t.register({[Tt]:a})}else{var l;(l=this.instance.persistence)===null||l===void 0||l.register({[Tt]:null})}}onRemoteConfig(e){var t,n,r,s,o,a;if(this._tryAddCustomEvent("$remote_config_received",e),this._persistRemoteConfig(e),this._linkedFlag=((t=e.sessionRecording)===null||t===void 0?void 0:t.linkedFlag)||null,(n=e.sessionRecording)!==null&&n!==void 0&&n.endpoint&&(this._endpoint=(a=e.sessionRecording)===null||a===void 0?void 0:a.endpoint),this._setupSampling(),!F(this._linkedFlag)&&!this._linkedFlagSeen){var l=V(this._linkedFlag)?this._linkedFlag:this._linkedFlag.flag,u=V(this._linkedFlag)?null:this._linkedFlag.variant;this.instance.onFeatureFlags((d,c)=>{var p=B(c)&&l in c,h=u?c[l]===u:p;h&&this._reportStarted("linked_flag_matched",{linkedFlag:l,linkedVariant:u}),this._linkedFlagSeen=h})}(r=e.sessionRecording)!==null&&r!==void 0&&r.urlTriggers&&(this._urlTriggers=e.sessionRecording.urlTriggers),(s=e.sessionRecording)!==null&&s!==void 0&&s.urlBlocklist&&(this._urlBlocklist=e.sessionRecording.urlBlocklist),(o=e.sessionRecording)!==null&&o!==void 0&&o.eventTriggers&&(this._eventTriggers=e.sessionRecording.eventTriggers),this.receivedDecide=!0,this.startIfEnabledOrStop()}_setupSampling(){Z(this.sampleRate)&&F(this._samplingSessionListener)&&(this._samplingSessionListener=this.sessionManager.onSessionId(e=>{this.makeSamplingDecision(e)}))}_persistRemoteConfig(e){if(this.instance.persistence){var t,n=this.instance.persistence,r=()=>{var s,o,a,l,u,d,c,p,h=(s=e.sessionRecording)===null||s===void 0?void 0:s.sampleRate,_=F(h)?null:parseFloat(h),f=(o=e.sessionRecording)===null||o===void 0?void 0:o.minimumDurationMilliseconds;n.register({[en]:!!e.sessionRecording,[_r]:(a=e.sessionRecording)===null||a===void 0?void 0:a.consoleLogRecordingEnabled,[gr]:v({capturePerformance:e.capturePerformance},(l=e.sessionRecording)===null||l===void 0?void 0:l.networkPayloadCapture),[fr]:{enabled:(u=e.sessionRecording)===null||u===void 0?void 0:u.recordCanvas,fps:(d=e.sessionRecording)===null||d===void 0?void 0:d.canvasFps,quality:(c=e.sessionRecording)===null||c===void 0?void 0:c.canvasQuality},[vr]:_,[mr]:E(f)?null:f,[yr]:(p=e.sessionRecording)===null||p===void 0?void 0:p.scriptConfig})};r(),(t=this._persistDecideOnSessionListener)===null||t===void 0||t.call(this),this._persistDecideOnSessionListener=this.sessionManager.onSessionId(r)}}log(e){var t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"log";(t=this.instance.sessionRecording)===null||t===void 0||t.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:n,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}_startCapture(e){if(!E(Object.assign)&&!E(Array.from)&&!(this._captureStarted||this.instance.config.disable_session_recording||this.instance.consent.isOptedOut())){var t,n;this._captureStarted=!0,this.sessionManager.checkAndGetSessionAndWindowId(),this.rrwebRecord?this._onScriptLoaded():(t=P.__PosthogExtensions__)===null||t===void 0||(n=t.loadExternalDependency)===null||n===void 0||n.call(t,this.instance,this.scriptName,r=>{if(r)return J.error("could not load recorder",r);this._onScriptLoaded()}),J.info("starting"),this.status==="active"&&this._reportStarted(e||"recording_initialized")}}get scriptName(){var e,t,n;return((e=this.instance)===null||e===void 0||(t=e.persistence)===null||t===void 0||(n=t.get_property(yr))===null||n===void 0?void 0:n.script)||"recorder"}isInteractiveEvent(e){var t;return e.type===3&&ja.indexOf((t=e.data)===null||t===void 0?void 0:t.source)!==-1}_updateWindowAndSessionIds(e){var t=this.isInteractiveEvent(e);t||this.isIdle||e.timestamp-this._lastActivityTimestamp>this.sessionIdleThresholdMilliseconds&&(this.isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:this.sessionIdleThresholdMilliseconds,bufferLength:this.buffer.data.length,bufferSize:this.buffer.size}),this._flushBuffer());var n=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this.isIdle&&(this.isIdle=!1,this._tryAddCustomEvent("sessionNoLongerIdle",{reason:"user activity",type:e.type}),n=!0)),!this.isIdle){var{windowId:r,sessionId:s}=this.sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),o=this.sessionId!==s,a=this.windowId!==r;this.windowId=r,this.sessionId=s,o||a?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):n&&this._scheduleFullSnapshot()}}_tryRRWebMethod(e){try{return e.rrwebMethod(),!0}catch(t){return this.queuedRRWebEvents.length<10?this.queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):J.warn("could not emit queued rrweb event.",t,e),!1}}_tryAddCustomEvent(e,t){return this._tryRRWebMethod(Nr(()=>this.rrwebRecord.addCustomEvent(e,t)))}_tryTakeFullSnapshot(){return this._tryRRWebMethod(Nr(()=>this.rrwebRecord.takeFullSnapshot()))}_onScriptLoaded(){var e,t={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},n=this.instance.config.session_recording;for(var[r,s]of Object.entries(n||{}))r in t&&(r==="maskInputOptions"?t.maskInputOptions=v({password:!0},s):t[r]=s);if(this.canvasRecording&&this.canvasRecording.enabled&&(t.recordCanvas=!0,t.sampling={canvas:this.canvasRecording.fps},t.dataURLOptions={type:"image/webp",quality:this.canvasRecording.quality}),this.rrwebRecord){this.mutationRateLimiter=(e=this.mutationRateLimiter)!==null&&e!==void 0?e:new Fa(this.rrwebRecord,{refillRate:this.instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this.instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(a,l)=>{var u="Too many mutations on node '".concat(a,"'. Rate limiting. This could be due to SVG animations or something similar");J.info(u,{node:l}),this.log(Ot+" "+u,"warn")}});var o=this._gatherRRWebPlugins();this.stopRrweb=this.rrwebRecord(v({emit:a=>{this.onRRwebEmit(a)},plugins:o},t)),this._lastActivityTimestamp=Date.now(),this.isIdle=!1,this._tryAddCustomEvent("$session_options",{sessionRecordingOptions:t,activePlugins:o.map(a=>a==null?void 0:a.name)}),this._tryAddCustomEvent("$posthog_config",{config:this.instance.config})}else J.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}_scheduleFullSnapshot(){if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!this.isIdle){var e=this.fullSnapshotIntervalMillis;e&&(this._fullSnapshotTimer=setInterval(()=>{this._tryTakeFullSnapshot()},e))}}_gatherRRWebPlugins(){var e,t,n,r,s=[],o=(e=P.__PosthogExtensions__)===null||e===void 0||(t=e.rrwebPlugins)===null||t===void 0?void 0:t.getRecordConsolePlugin;o&&this.isConsoleLogCaptureEnabled&&s.push(o());var a=(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.rrwebPlugins)===null||r===void 0?void 0:r.getRecordNetworkPlugin;return this.networkPayloadCapture&&le(a)&&(!aa.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?s.push(a(Ra(this.instance.config,this.networkPayloadCapture))):J.info("NetworkCapture not started because we are on localhost.")),s}onRRwebEmit(e){var t;if(this._processQueuedEvents(),e&&B(e)){if(e.type===xe.Meta){var n=this._maskUrl(e.data.href);if(this._lastHref=n,!n)return;e.data.href=n}else this._pageViewFallBack();if(this._checkUrlTriggerConditions(),!this._urlBlocked||function(c){return c.type===xe.Custom&&c.data.tag==="recording paused"}(e)){e.type===xe.FullSnapshot&&this._scheduleFullSnapshot(),e.type===xe.FullSnapshot&&this.triggerStatus==="trigger_pending"&&this.clearBuffer();var r=this.mutationRateLimiter?this.mutationRateLimiter.throttleMutations(e):e;if(r){var s=function(c){var p=c;if(p&&B(p)&&p.type===6&&B(p.data)&&p.data.plugin==="rrweb/console@1"){p.data.payload.payload.length>10&&(p.data.payload.payload=p.data.payload.payload.slice(0,10),p.data.payload.payload.push("...[truncated]"));for(var h=[],_=0;_<p.data.payload.payload.length;_++)p.data.payload.payload[_]&&p.data.payload.payload[_].length>2e3?h.push(p.data.payload.payload[_].slice(0,2e3)+"...[truncated]"):h.push(p.data.payload.payload[_]);return p.data.payload.payload=h,c}return c}(r);if(this._updateWindowAndSessionIds(s),!this.isIdle||qr(s)){if(qr(s)){var o=s.data.payload;if(o){var a=o.lastActivityTimestamp,l=o.threshold;s.timestamp=a+l}}var u=(t=this.instance.config.session_recording.compress_events)===null||t===void 0||t?function(c){if(gi(c)<1024)return c;try{if(c.type===xe.FullSnapshot)return v(v({},c),{},{data:Ve(c.data),cv:"2024-10"});if(c.type===xe.IncrementalSnapshot&&c.data.source===ge.Mutation)return v(v({},c),{},{cv:"2024-10",data:v(v({},c.data),{},{texts:Ve(c.data.texts),attributes:Ve(c.data.attributes),removes:Ve(c.data.removes),adds:Ve(c.data.adds)})});if(c.type===xe.IncrementalSnapshot&&c.data.source===ge.StyleSheetRule)return v(v({},c),{},{cv:"2024-10",data:v(v({},c.data),{},{adds:c.data.adds?Ve(c.data.adds):void 0,removes:c.data.removes?Ve(c.data.removes):void 0})})}catch(p){J.error("could not compress event - will use uncompressed event",p)}return c}(s):s,d={$snapshot_bytes:gi(u),$snapshot_data:u,$session_id:this.sessionId,$window_id:this.windowId};this.status!=="disabled"?this._captureSnapshotBuffered(d):this.clearBuffer()}}}}}_pageViewFallBack(){if(!this.instance.config.capture_pageview&&g){var e=this._maskUrl(g.location.href);this._lastHref!==e&&(this._tryAddCustomEvent("$url_changed",{href:e}),this._lastHref=e)}}_processQueuedEvents(){if(this.queuedRRWebEvents.length){var e=[...this.queuedRRWebEvents];this.queuedRRWebEvents=[],e.forEach(t=>{Date.now()-t.enqueuedAt<=2e3&&this._tryRRWebMethod(t)})}}_maskUrl(e){var t=this.instance.config.session_recording;if(t.maskNetworkRequestFn){var n,r={url:e};return(n=r=t.maskNetworkRequestFn(r))===null||n===void 0?void 0:n.url}return e}clearBuffer(){return this.buffer={size:0,data:[],sessionId:this.sessionId,windowId:this.windowId},this.buffer}_flushBuffer(){this.flushBufferTimer&&(clearTimeout(this.flushBufferTimer),this.flushBufferTimer=void 0);var e=this.minimumDuration,t=this.sessionDuration,n=Z(t)&&t>=0,r=Z(e)&&n&&t<e;return this.status==="buffering"||this.status==="paused"||this.status==="disabled"||r?(this.flushBufferTimer=setTimeout(()=>{this._flushBuffer()},2e3),this.buffer):(this.buffer.data.length>0&&dn(this.buffer).forEach(s=>{this._captureSnapshot({$snapshot_bytes:s.size,$snapshot_data:s.data,$session_id:s.sessionId,$window_id:s.windowId,$lib:"web",$lib_version:Ce.LIB_VERSION})}),this.clearBuffer())}_captureSnapshotBuffered(e){var t,n=2+(((t=this.buffer)===null||t===void 0?void 0:t.data.length)||0);!this.isIdle&&(this.buffer.size+e.$snapshot_bytes+n>943718.4||this.buffer.sessionId!==this.sessionId)&&(this.buffer=this._flushBuffer()),this.buffer.size+=e.$snapshot_bytes,this.buffer.data.push(e.$snapshot_data),this.flushBufferTimer||this.isIdle||(this.flushBufferTimer=setTimeout(()=>{this._flushBuffer()},2e3))}_captureSnapshot(e){this.instance.capture("$snapshot",e,{_url:this.instance.requestRouter.endpointFor("api",this._endpoint),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}_checkUrlTriggerConditions(){if(g!==void 0&&g.location.href){var e=g.location.href,t=this._urlBlocked,n=Br(e,this._urlBlocklist);n&&!t?this._pauseRecording():!n&&t&&this._resumeRecording(),Br(e,this._urlTriggers)&&this._activateTrigger("url")}}_activateTrigger(e){var t,n;this.triggerStatus==="trigger_pending"&&((t=this.instance)===null||t===void 0||(n=t.persistence)===null||n===void 0||n.register({[e==="url"?Di:Li]:this.sessionId}),this._flushBuffer(),this._reportStarted(e+"_trigger_matched"))}_pauseRecording(){this._urlBlocked||(this._urlBlocked=!0,clearInterval(this._fullSnapshotTimer),J.info("recording paused due to URL blocker"),this._tryAddCustomEvent("recording paused",{reason:"url blocker"}))}_resumeRecording(){this._urlBlocked&&(this._urlBlocked=!1,this._tryTakeFullSnapshot(),this._scheduleFullSnapshot(),this._tryAddCustomEvent("recording resumed",{reason:"left blocked url"}),J.info("recording resumed"))}_addEventTriggerListener(){this._eventTriggers.length!==0&&F(this._removeEventTriggerCaptureHook)&&(this._removeEventTriggerCaptureHook=this.instance.on("eventCaptured",e=>{try{this._eventTriggers.includes(e.event)&&this._activateTrigger("event")}catch(t){J.error("Could not activate event trigger",t)}}))}overrideLinkedFlag(){this._linkedFlagSeen=!0,this._tryTakeFullSnapshot(),this._reportStarted("linked_flag_overridden")}overrideSampling(){var e;(e=this.instance.persistence)===null||e===void 0||e.register({[Tt]:!0}),this._tryTakeFullSnapshot(),this._reportStarted("sampling_overridden")}overrideTrigger(e){this._activateTrigger(e)}_reportStarted(e,t){this.instance.register_for_session({$session_recording_start_reason:e}),J.info(e.replace("_"," "),t),T(["recording_initialized","session_id_changed"],e)||this._tryAddCustomEvent(e,t)}}var Je=K("[RemoteConfig]");class Ga{constructor(e){this.instance=e}get remoteConfig(){var e,t;return(e=P._POSTHOG_REMOTE_CONFIG)===null||e===void 0||(t=e[this.instance.config.token])===null||t===void 0?void 0:t.config}_loadRemoteConfigJs(e){var t,n,r;(t=P.__PosthogExtensions__)!==null&&t!==void 0&&t.loadExternalDependency?(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.loadExternalDependency)===null||r===void 0||r.call(n,this.instance,"remote-config",()=>e(this.remoteConfig)):(Je.error("PostHog Extensions not found. Cannot load remote config."),e())}_loadRemoteConfigJSON(e){this.instance._send_request({method:"GET",url:this.instance.requestRouter.endpointFor("assets","/array/".concat(this.instance.config.token,"/config")),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return Je.info("Using preloaded remote config",this.remoteConfig),void this.onRemoteConfig(this.remoteConfig);if(this.instance.config.advanced_disable_decide)return void Je.warn("Remote config is disabled. Falling back to local config.");this._loadRemoteConfigJs(e=>{if(!e)return Je.info("No config found after loading remote JS config. Falling back to JSON."),void this._loadRemoteConfigJSON(t=>{this.onRemoteConfig(t)});this.onRemoteConfig(e)})}catch(e){Je.error("Error loading remote config",e)}}onRemoteConfig(e){e?this.instance.config.__preview_remote_config?(this.instance._onRemoteConfig(e),e.hasFeatureFlags!==!1&&this.instance.featureFlags.ensureFlagsLoaded()):Je.info("__preview_remote_config is disabled. Logging config instead",e):Je.error("Failed to fetch remote config from PostHog.")}}var Ie,Wa=g!=null&&g.location?pi(g.location.hash,"__posthog")||pi(location.hash,"state"):null,Ur="_postHogToolbarParams",Hr=K("[Toolbar]");(function(i){i[i.UNINITIALIZED=0]="UNINITIALIZED",i[i.LOADING=1]="LOADING",i[i.LOADED=2]="LOADED"})(Ie||(Ie={}));class Va{constructor(e){this.instance=e}setToolbarState(e){P.ph_toolbar_state=e}getToolbarState(){var e;return(e=P.ph_toolbar_state)!==null&&e!==void 0?e:Ie.UNINITIALIZED}maybeLoadToolbar(){var e,t,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;if(!g||!S)return!1;n=(e=n)!==null&&e!==void 0?e:g.location,s=(t=s)!==null&&t!==void 0?t:g.history;try{if(!r){try{g.localStorage.setItem("test","test"),g.localStorage.removeItem("test")}catch{return!1}r=g==null?void 0:g.localStorage}var o,a=Wa||pi(n.hash,"__posthog")||pi(n.hash,"state"),l=a?or(()=>JSON.parse(atob(decodeURIComponent(a))))||or(()=>JSON.parse(decodeURIComponent(a))):null;return l&&l.action==="ph_authorize"?((o=l).source="url",o&&Object.keys(o).length>0&&(l.desiredHash?n.hash=l.desiredHash:s?s.replaceState(s.state,"",n.pathname+n.search):n.hash="")):((o=JSON.parse(r.getItem(Ur)||"{}")).source="localstorage",delete o.userIntent),!(!o.token||this.instance.config.token!==o.token)&&(this.loadToolbar(o),!0)}catch{return!1}}_callLoadToolbar(e){var t=P.ph_load_toolbar||P.ph_load_editor;!F(t)&&le(t)?t(e,this.instance):Hr.warn("No toolbar load function found")}loadToolbar(e){var t=!(S==null||!S.getElementById(Fs));if(!g||t)return!1;var n=this.instance.requestRouter.region==="custom"&&this.instance.config.advanced_disable_toolbar_metrics,r=v(v({token:this.instance.config.token},e),{},{apiURL:this.instance.requestRouter.endpointFor("ui")},n?{instrument:!1}:{});if(g.localStorage.setItem(Ur,JSON.stringify(v(v({},r),{},{source:void 0}))),this.getToolbarState()===Ie.LOADED)this._callLoadToolbar(r);else if(this.getToolbarState()===Ie.UNINITIALIZED){var s,o;this.setToolbarState(Ie.LOADING),(s=P.__PosthogExtensions__)===null||s===void 0||(o=s.loadExternalDependency)===null||o===void 0||o.call(s,this.instance,"toolbar",a=>{if(a)return Hr.error("[Toolbar] Failed to load",a),void this.setToolbarState(Ie.UNINITIALIZED);this.setToolbarState(Ie.LOADED),this._callLoadToolbar(r)}),U(g,"turbolinks:load",()=>{this.setToolbarState(Ie.UNINITIALIZED),this.loadToolbar(r)})}return!0}_loadEditor(e){return this.loadToolbar(e)}maybeLoadEditor(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;return this.maybeLoadToolbar(e,t,n)}}var wn=3e3;class Ja{constructor(e,t){y(this,"isPaused",!0),y(this,"queue",[]),this.flushTimeoutMs=ce((t==null?void 0:t.flush_interval_ms)||wn,250,5e3,"flush interval",wn),this.sendRequest=e}enqueue(e){this.queue.push(e),this.flushTimeout||this.setFlushTimeout()}unload(){this.clearFlushTimeout();var e=this.queue.length>0?this.formatQueue():{},t=Object.values(e),n=[...t.filter(r=>r.url.indexOf("/e")===0),...t.filter(r=>r.url.indexOf("/e")!==0)];n.map(r=>{this.sendRequest(v(v({},r),{},{transport:"sendBeacon"}))})}enable(){this.isPaused=!1,this.setFlushTimeout()}setFlushTimeout(){var e=this;this.isPaused||(this.flushTimeout=setTimeout(()=>{if(this.clearFlushTimeout(),this.queue.length>0){var t=this.formatQueue(),n=function(s){var o=t[s],a=new Date().getTime();o.data&&q(o.data)&&A(o.data,l=>{l.offset=Math.abs(l.timestamp-a),delete l.timestamp}),e.sendRequest(o)};for(var r in t)n(r)}},this.flushTimeoutMs))}clearFlushTimeout(){clearTimeout(this.flushTimeout),this.flushTimeout=void 0}formatQueue(){var e={};return A(this.queue,t=>{var n,r=t,s=(r?r.batchKey:null)||r.url;E(e[s])&&(e[s]=v(v({},r),{},{data:[]})),(n=e[s].data)===null||n===void 0||n.push(r.data)}),this.queue=[],e}}var Ya=function(i){var e,t,n,r,s="";for(e=t=0,n=(i=(i+"").replace(/\r\n/g,`
`).replace(/\r/g,`
`)).length,r=0;r<n;r++){var o=i.charCodeAt(r),a=null;o<128?t++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),je(a)||(t>e&&(s+=i.substring(e,t)),s+=a,e=t=r+1)}return t>e&&(s+=i.substring(e,i.length)),s},Ka=!!Qi||!!Xi,jr="text/plain",mi=(i,e)=>{var[t,n]=i.split("?"),r=v({},e);n==null||n.split("&").forEach(o=>{var[a]=o.split("=");delete r[a]});var s=la(r);return s=s?(n?n+"&":"")+s:n,"".concat(t,"?").concat(s)},At=(i,e)=>JSON.stringify(i,(t,n)=>typeof n=="bigint"?n.toString():n,e),Gi=i=>{var{data:e,compression:t}=i;if(e){if(t===fe.GZipJS){var n=lo(co(At(e)),{mtime:0}),r=new Blob([n],{type:jr});return{contentType:jr,body:r,estimatedSize:r.size}}if(t===fe.Base64){var s=function(l){var u,d,c,p,h,_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,b=0,m="",w=[];if(!l)return l;l=Ya(l);do u=(h=l.charCodeAt(f++)<<16|l.charCodeAt(f++)<<8|l.charCodeAt(f++))>>18&63,d=h>>12&63,c=h>>6&63,p=63&h,w[b++]=_.charAt(u)+_.charAt(d)+_.charAt(c)+_.charAt(p);while(f<l.length);switch(m=w.join(""),l.length%3){case 1:m=m.slice(0,-2)+"==";break;case 2:m=m.slice(0,-1)+"="}return m}(At(e)),o=(l=>"data="+encodeURIComponent(typeof l=="string"?l:At(l)))(s);return{contentType:"application/x-www-form-urlencoded",body:o,estimatedSize:new Blob([o]).size}}var a=At(e);return{contentType:"application/json",body:a,estimatedSize:new Blob([a]).size}}},$t=[];Xi&&$t.push({transport:"fetch",method:i=>{var e,t,{contentType:n,body:r,estimatedSize:s}=(e=Gi(i))!==null&&e!==void 0?e:{},o=new Headers;A(i.headers,function(d,c){o.append(c,d)}),n&&o.append("Content-Type",n);var a=i.url,l=null;if(sr){var u=new sr;l={signal:u.signal,timeout:setTimeout(()=>u.abort(),i.timeout)}}Xi(a,v({method:(i==null?void 0:i.method)||"GET",headers:o,keepalive:i.method==="POST"&&(s||0)<52428.8,body:r,signal:(t=l)===null||t===void 0?void 0:t.signal},i.fetchOptions)).then(d=>d.text().then(c=>{var p,h={statusCode:d.status,text:c};if(d.status===200)try{h.json=JSON.parse(c)}catch(_){k.error(_)}(p=i.callback)===null||p===void 0||p.call(i,h)})).catch(d=>{var c;k.error(d),(c=i.callback)===null||c===void 0||c.call(i,{statusCode:0,text:d})}).finally(()=>l?clearTimeout(l.timeout):null)}}),Qi&&$t.push({transport:"XHR",method:i=>{var e,t=new Qi;t.open(i.method||"GET",i.url,!0);var{contentType:n,body:r}=(e=Gi(i))!==null&&e!==void 0?e:{};A(i.headers,function(s,o){t.setRequestHeader(o,s)}),n&&t.setRequestHeader("Content-Type",n),i.timeout&&(t.timeout=i.timeout),t.withCredentials=!0,t.onreadystatechange=()=>{if(t.readyState===4){var s,o={statusCode:t.status,text:t.responseText};if(t.status===200)try{o.json=JSON.parse(t.responseText)}catch{}(s=i.callback)===null||s===void 0||s.call(i,o)}},t.send(r)}}),ne!=null&&ne.sendBeacon&&$t.push({transport:"sendBeacon",method:i=>{var e=mi(i.url,{beacon:"1"});try{var t,{contentType:n,body:r}=(t=Gi(i))!==null&&t!==void 0?t:{},s=typeof r=="string"?new Blob([r],{type:n}):r;ne.sendBeacon(e,s)}catch{}}});var Xa=["retriesPerformedSoFar"];class Qa{constructor(e){y(this,"isPolling",!1),y(this,"pollIntervalMs",3e3),y(this,"queue",[]),this.instance=e,this.queue=[],this.areWeOnline=!0,!E(g)&&"onLine"in g.navigator&&(this.areWeOnline=g.navigator.onLine,U(g,"online",()=>{this.areWeOnline=!0,this.flush()}),U(g,"offline",()=>{this.areWeOnline=!1}))}retriableRequest(e){var{retriesPerformedSoFar:t}=e,n=Cs(e,Xa);Z(t)&&t>0&&(n.url=mi(n.url,{retry_count:t})),this.instance._send_request(v(v({},n),{},{callback:r=>{var s;r.statusCode!==200&&(r.statusCode<400||r.statusCode>=500)&&(t??0)<10?this.enqueue(v({retriesPerformedSoFar:t},n)):(s=n.callback)===null||s===void 0||s.call(n,r)}}))}enqueue(e){var t=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=t+1;var n=function(o){var a=3e3*Math.pow(2,o),l=a/2,u=Math.min(18e5,a),d=(Math.random()-.5)*(u-l);return Math.ceil(u+d)}(t),r=Date.now()+n;this.queue.push({retryAt:r,requestOptions:e});var s="Enqueued failed request for retry in ".concat(n);navigator.onLine||(s+=" (Browser is offline)"),k.warn(s),this.isPolling||(this.isPolling=!0,this.poll())}poll(){this.poller&&clearTimeout(this.poller),this.poller=setTimeout(()=>{this.areWeOnline&&this.queue.length>0&&this.flush(),this.poll()},this.pollIntervalMs)}flush(){var e=Date.now(),t=[],n=this.queue.filter(s=>s.retryAt<e||(t.push(s),!1));if(this.queue=t,n.length>0)for(var{requestOptions:r}of n)this.retriableRequest(r)}unload(){for(var{requestOptions:e}of(this.poller&&(clearTimeout(this.poller),this.poller=void 0),this.queue))try{this.instance._send_request(v(v({},e),{},{transport:"sendBeacon"}))}catch(t){k.error(t)}this.queue=[]}}var tt,zr=K("[SessionId]");class Za{constructor(e,t,n){var r;if(y(this,"_sessionIdChangedHandlers",[]),!e.persistence)throw new Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw new Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.config=e.config,this.persistence=e.persistence,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=t||Le,this._windowIdGenerator=n||Le;var s=this.config.persistence_name||this.config.token,o=this.config.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*ce(o,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.resetIdleTimer(),this._window_id_storage_key="ph_"+s+"_window_id",this._primary_window_exists_storage_key="ph_"+s+"_primary_window_exists",this._canUseSessionStorage()){var a=W.parse(this._window_id_storage_key),l=W.parse(this._primary_window_exists_storage_key);a&&!l?this._windowId=a:W.remove(this._window_id_storage_key),W.set(this._primary_window_exists_storage_key,!0)}if((r=this.config.bootstrap)!==null&&r!==void 0&&r.sessionID)try{var u=(d=>{var c=d.replace(/-/g,"");if(c.length!==32)throw new Error("Not a valid UUID");if(c[12]!=="7")throw new Error("Not a UUIDv7");return parseInt(c.substring(0,12),16)})(this.config.bootstrap.sessionID);this._setSessionId(this.config.bootstrap.sessionID,new Date().getTime(),u)}catch(d){zr.error("Invalid sessionID in bootstrap",d)}this._listenToReloadWindow()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return E(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),()=>{this._sessionIdChangedHandlers=this._sessionIdChangedHandlers.filter(t=>t!==e)}}_canUseSessionStorage(){return this.config.persistence!=="memory"&&!this.persistence.disabled&&W.is_supported()}_setWindowId(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&W.set(this._window_id_storage_key,e))}_getWindowId(){return this._windowId?this._windowId:this._canUseSessionStorage()?W.parse(this._window_id_storage_key):null}_setSessionId(e,t,n){e===this._sessionId&&t===this._sessionActivityTimestamp&&n===this._sessionStartTimestamp||(this._sessionStartTimestamp=n,this._sessionActivityTimestamp=t,this._sessionId=e,this.persistence.register({[li]:[t,e,n]}))}_getSessionId(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this.persistence.props[li];return q(e)&&e.length===2&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this._setSessionId(null,null,null)}_listenToReloadWindow(){U(g,"beforeunload",()=>{this._canUseSessionStorage()&&W.remove(this._primary_window_exists_storage_key)},{capture:!1})}checkAndGetSessionAndWindowId(){var e=arguments.length>0&&arguments[0]!==void 0&&arguments[0],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.config.__preview_experimental_cookieless_mode)throw new Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var n=t||new Date().getTime(),[r,s,o]=this._getSessionId(),a=this._getWindowId(),l=Z(o)&&o>0&&Math.abs(n-o)>864e5,u=!1,d=!s,c=!e&&Math.abs(n-r)>this.sessionTimeoutMs;d||c||l?(s=this._sessionIdGenerator(),a=this._windowIdGenerator(),zr.info("new session ID generated",{sessionId:s,windowId:a,changeReason:{noSessionId:d,activityTimeout:c,sessionPastMaximumLength:l}}),o=n,u=!0):a||(a=this._windowIdGenerator(),u=!0);var p=r===0||!e||l?n:r,h=o===0?new Date().getTime():o;return this._setWindowId(a),this._setSessionId(s,p,h),e||this.resetIdleTimer(),u&&this._sessionIdChangedHandlers.forEach(_=>_(s,a,u?{noSessionId:d,activityTimeout:c,sessionPastMaximumLength:l}:void 0)),{sessionId:s,windowId:a,sessionStartTimestamp:h,changeReason:u?{noSessionId:d,activityTimeout:c,sessionPastMaximumLength:l}:void 0,lastActivityTimestamp:r}}resetIdleTimer(){clearTimeout(this._enforceIdleTimeout),this._enforceIdleTimeout=setTimeout(()=>{this.resetSessionId()},1.1*this.sessionTimeoutMs)}}(function(i){i.US="us",i.EU="eu",i.CUSTOM="custom"})(tt||(tt={}));var Gr="i.posthog.com";class el{constructor(e){y(this,"_regionCache",{}),this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return e==="https://app.posthog.com"?"https://us.i.posthog.com":e}get uiHost(){var e,t=(e=this.instance.config.ui_host)===null||e===void 0?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace(".".concat(Gr),".posthog.com")),t==="https://app.posthog.com"?"https://us.posthog.com":t}get region(){return this._regionCache[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=tt.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=tt.EU:this._regionCache[this.apiHost]=tt.CUSTOM),this._regionCache[this.apiHost]}endpointFor(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t&&(t=t[0]==="/"?t:"/".concat(t)),e==="ui")return this.uiHost+t;if(this.region===tt.CUSTOM)return this.apiHost+t;var n=Gr+t;switch(e){case"assets":return"https://".concat(this.region,"-assets.").concat(n);case"api":return"https://".concat(this.region,".").concat(n)}}}var uo="posthog-js";function ho(i){var{organization:e,projectId:t,prefix:n,severityAllowList:r=["error"]}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return s=>{var o,a,l,u,d;if(!(r==="*"||r.includes(s.level))||!i.__loaded)return s;s.tags||(s.tags={});var c=i.requestRouter.endpointFor("ui","/project/".concat(i.config.token,"/person/").concat(i.get_distinct_id()));s.tags["PostHog Person URL"]=c,i.sessionRecordingStarted()&&(s.tags["PostHog Recording URL"]=i.get_session_replay_url({withTimestamp:!0}));var p=((o=s.exception)===null||o===void 0?void 0:o.values)||[];p.forEach(_=>{_.stacktrace&&(_.stacktrace.type="raw",_.stacktrace.frames.forEach(f=>{f.platform="web:javascript"}))});var h={$exception_message:((a=p[0])===null||a===void 0?void 0:a.value)||s.message,$exception_type:(l=p[0])===null||l===void 0?void 0:l.type,$exception_personURL:c,$exception_level:s.level,$exception_list:p,$sentry_event_id:s.event_id,$sentry_exception:s.exception,$sentry_exception_message:((u=p[0])===null||u===void 0?void 0:u.value)||s.message,$sentry_exception_type:(d=p[0])===null||d===void 0?void 0:d.type,$sentry_tags:s.tags};return e&&t&&(h.$sentry_url=(n||"https://sentry.io/organizations/")+e+"/issues/?project="+t+"&query="+s.event_id),i.exceptions.sendExceptionEvent(h),s}}class tl{constructor(e,t,n,r,s){this.name=uo,this.setupOnce=function(o){o(ho(e,{organization:t,projectId:n,prefix:r,severityAllowList:s}))}}}var Wi=K("[SegmentIntegration]");function il(i,e){var t=i.config.segment;if(!t)return e();(function(n,r){var s=n.config.segment;if(!s)return r();var o=l=>{var u=()=>l.anonymousId()||Le();n.config.get_device_id=u,l.id()&&(n.register({distinct_id:l.id(),$device_id:u()}),n.persistence.set_property(ke,"identified")),r()},a=s.user();"then"in a&&le(a.then)?a.then(l=>o(l)):o(a)})(i,()=>{t.register((n=>{Promise&&Promise.resolve||Wi.warn("This browser does not have Promise support, and can not use the segment integration");var r=(s,o)=>{var a;if(!o)return s;s.event.userId||s.event.anonymousId===n.get_distinct_id()||(Wi.info("No userId set, resetting PostHog"),n.reset()),s.event.userId&&s.event.userId!==n.get_distinct_id()&&(Wi.info("UserId set, identifying with PostHog"),n.identify(s.event.userId));var l=n._calculate_event_properties(o,(a=s.event.properties)!==null&&a!==void 0?a:{},new Date);return s.event.properties=Object.assign({},l,s.event.properties),s};return{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:s=>r(s,s.event.event),page:s=>r(s,"$pageview"),identify:s=>r(s,"$identify"),screen:s=>r(s,"$screen")}})(i)).then(()=>{e()})})}class nl{constructor(e){this._instance=e}doPageView(e,t){var n,r=this._previousPageViewProperties(e,t);return this._currentPageview={pathname:(n=g==null?void 0:g.location.pathname)!==null&&n!==void 0?n:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),r}doPageLeave(e){var t;return this._previousPageViewProperties(e,(t=this._currentPageview)===null||t===void 0?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:(e=this._currentPageview)===null||e===void 0?void 0:e.pageViewId}}_previousPageViewProperties(e,t){var n=this._currentPageview;if(!n)return{$pageview_id:t};var r={$pageview_id:t,$prev_pageview_id:n.pageViewId},s=this._instance.scrollManager.getContext();if(s&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:o,lastScrollY:a,maxScrollY:l,maxContentHeight:u,lastContentY:d,maxContentY:c}=s;if(!(E(o)||E(a)||E(l)||E(u)||E(d)||E(c))){o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),d=Math.ceil(d),c=Math.ceil(c);var p=o<=1?1:ce(a/o,0,1),h=o<=1?1:ce(l/o,0,1),_=u<=1?1:ce(d/u,0,1),f=u<=1?1:ce(c/u,0,1);r=G(r,{$prev_pageview_last_scroll:a,$prev_pageview_last_scroll_percentage:p,$prev_pageview_max_scroll:l,$prev_pageview_max_scroll_percentage:h,$prev_pageview_last_content:d,$prev_pageview_last_content_percentage:_,$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:f})}}return n.pathname&&(r.$prev_pageview_pathname=n.pathname),n.timestamp&&(r.$prev_pageview_duration=(e.getTime()-n.timestamp.getTime())/1e3),r}}var po,N,_o,Ze,Wr,go,Sn,fo,En={},vo=[],rl=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Nn=Array.isArray;function Ne(i,e){for(var t in e)i[t]=e[t];return i}function mo(i){var e=i.parentNode;e&&e.removeChild(i)}function Vi(i,e,t,n,r){var s={type:i,props:e,key:t,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:r??++_o,__i:-1,__u:0};return r==null&&N.vnode!=null&&N.vnode(s),s}function qn(i){return i.children}function si(i,e){this.props=i,this.context=e}function mt(i,e){if(e==null)return i.__?mt(i.__,i.__i+1):null;for(var t;e<i.__k.length;e++)if((t=i.__k[e])!=null&&t.__e!=null)return t.__e;return typeof i.type=="function"?mt(i):null}function yo(i){var e,t;if((i=i.__)!=null&&i.__c!=null){for(i.__e=i.__c.base=null,e=0;e<i.__k.length;e++)if((t=i.__k[e])!=null&&t.__e!=null){i.__e=i.__c.base=t.__e;break}return yo(i)}}function kn(i){(!i.__d&&(i.__d=!0)&&Ze.push(i)&&!yi.__r++||Wr!==N.debounceRendering)&&((Wr=N.debounceRendering)||go)(yi)}function yi(){var i,e,t,n,r,s,o,a,l;for(Ze.sort(Sn);i=Ze.shift();)i.__d&&(e=Ze.length,n=void 0,s=(r=(t=i).__v).__e,a=[],l=[],(o=t.__P)&&((n=Ne({},r)).__v=r.__v+1,N.vnode&&N.vnode(n),So(o,n,r,t.__n,o.ownerSVGElement!==void 0,32&r.__u?[s]:null,a,s??mt(r),!!(32&r.__u),l),n.__.__k[n.__i]=n,al(a,n,l),n.__e!=s&&yo(n)),Ze.length>e&&Ze.sort(Sn));yi.__r=0}function bo(i,e,t,n,r,s,o,a,l,u,d){var c,p,h,_,f,b=n&&n.__k||vo,m=e.length;for(t.__d=l,sl(t,e,b),l=t.__d,c=0;c<m;c++)(h=t.__k[c])!=null&&typeof h!="boolean"&&typeof h!="function"&&(p=h.__i===-1?En:b[h.__i]||En,h.__i=c,So(i,h,p,r,s,o,a,l,u,d),_=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&Bn(p.ref,null,h),d.push(h.ref,h.__c||_,h)),f==null&&_!=null&&(f=_),65536&h.__u||p.__k===h.__k?l=wo(h,l,i):typeof h.type=="function"&&h.__d!==void 0?l=h.__d:_&&(l=_.nextSibling),h.__d=void 0,h.__u&=-196609);t.__d=l,t.__e=f}function sl(i,e,t){var n,r,s,o,a,l=e.length,u=t.length,d=u,c=0;for(i.__k=[],n=0;n<l;n++)(r=i.__k[n]=(r=e[n])==null||typeof r=="boolean"||typeof r=="function"?null:typeof r=="string"||typeof r=="number"||typeof r=="bigint"||r.constructor==String?Vi(null,r,null,null,r):Nn(r)?Vi(qn,{children:r},null,null,null):r.constructor===void 0&&r.__b>0?Vi(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r)!=null?(r.__=i,r.__b=i.__b+1,a=ol(r,t,o=n+c,d),r.__i=a,s=null,a!==-1&&(d--,(s=t[a])&&(s.__u|=131072)),s==null||s.__v===null?(a==-1&&c--,typeof r.type!="function"&&(r.__u|=65536)):a!==o&&(a===o+1?c++:a>o?d>l-o?c+=a-o:c--:c=a<o&&a==o-1?a-o:0,a!==n+c&&(r.__u|=65536))):(s=t[n])&&s.key==null&&s.__e&&(s.__e==i.__d&&(i.__d=mt(s)),xn(s,s,!1),t[n]=null,d--);if(d)for(n=0;n<u;n++)(s=t[n])!=null&&!(131072&s.__u)&&(s.__e==i.__d&&(i.__d=mt(s)),xn(s,s))}function wo(i,e,t){var n,r;if(typeof i.type=="function"){for(n=i.__k,r=0;n&&r<n.length;r++)n[r]&&(n[r].__=i,e=wo(n[r],e,t));return e}return i.__e!=e&&(t.insertBefore(i.__e,e||null),e=i.__e),e&&e.nextSibling}function ol(i,e,t,n){var r=i.key,s=i.type,o=t-1,a=t+1,l=e[t];if(l===null||l&&r==l.key&&s===l.type)return t;if(n>(l!=null&&!(131072&l.__u)?1:0))for(;o>=0||a<e.length;){if(o>=0){if((l=e[o])&&!(131072&l.__u)&&r==l.key&&s===l.type)return o;o--}if(a<e.length){if((l=e[a])&&!(131072&l.__u)&&r==l.key&&s===l.type)return a;a++}}return-1}function Vr(i,e,t){e[0]==="-"?i.setProperty(e,t??""):i[e]=t==null?"":typeof t!="number"||rl.test(e)?t:t+"px"}function Xt(i,e,t,n,r){var s;e:if(e==="style")if(typeof t=="string")i.style.cssText=t;else{if(typeof n=="string"&&(i.style.cssText=n=""),n)for(e in n)t&&e in t||Vr(i.style,e,"");if(t)for(e in t)n&&t[e]===n[e]||Vr(i.style,e,t[e])}else if(e[0]==="o"&&e[1]==="n")s=e!==(e=e.replace(/(PointerCapture)$|Capture$/,"$1")),e=e.toLowerCase()in i?e.toLowerCase().slice(2):e.slice(2),i.l||(i.l={}),i.l[e+s]=t,t?n?t.u=n.u:(t.u=Date.now(),i.addEventListener(e,s?Yr:Jr,s)):i.removeEventListener(e,s?Yr:Jr,s);else{if(r)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e!=="rowSpan"&&e!=="colSpan"&&e!=="role"&&e in i)try{i[e]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&e[4]!=="-"?i.removeAttribute(e):i.setAttribute(e,t))}}function Jr(i){var e=this.l[i.type+!1];if(i.t){if(i.t<=e.u)return}else i.t=Date.now();return e(N.event?N.event(i):i)}function Yr(i){return this.l[i.type+!0](N.event?N.event(i):i)}function So(i,e,t,n,r,s,o,a,l,u){var d,c,p,h,_,f,b,m,w,x,I,R,O,L,C,$=e.type;if(e.constructor!==void 0)return null;128&t.__u&&(l=!!(32&t.__u),s=[a=e.__e=t.__e]),(d=N.__b)&&d(e);e:if(typeof $=="function")try{if(m=e.props,w=(d=$.contextType)&&n[d.__c],x=d?w?w.props.value:d.__:n,t.__c?b=(c=e.__c=t.__c).__=c.__E:("prototype"in $&&$.prototype.render?e.__c=c=new $(m,x):(e.__c=c=new si(m,x),c.constructor=$,c.render=cl),w&&w.sub(c),c.props=m,c.state||(c.state={}),c.context=x,c.__n=n,p=c.__d=!0,c.__h=[],c._sb=[]),c.__s==null&&(c.__s=c.state),$.getDerivedStateFromProps!=null&&(c.__s==c.state&&(c.__s=Ne({},c.__s)),Ne(c.__s,$.getDerivedStateFromProps(m,c.__s))),h=c.props,_=c.state,c.__v=e,p)$.getDerivedStateFromProps==null&&c.componentWillMount!=null&&c.componentWillMount(),c.componentDidMount!=null&&c.__h.push(c.componentDidMount);else{if($.getDerivedStateFromProps==null&&m!==h&&c.componentWillReceiveProps!=null&&c.componentWillReceiveProps(m,x),!c.__e&&(c.shouldComponentUpdate!=null&&c.shouldComponentUpdate(m,c.__s,x)===!1||e.__v===t.__v)){for(e.__v!==t.__v&&(c.props=m,c.state=c.__s,c.__d=!1),e.__e=t.__e,e.__k=t.__k,e.__k.forEach(function(z){z&&(z.__=e)}),I=0;I<c._sb.length;I++)c.__h.push(c._sb[I]);c._sb=[],c.__h.length&&o.push(c);break e}c.componentWillUpdate!=null&&c.componentWillUpdate(m,c.__s,x),c.componentDidUpdate!=null&&c.__h.push(function(){c.componentDidUpdate(h,_,f)})}if(c.context=x,c.props=m,c.__P=i,c.__e=!1,R=N.__r,O=0,"prototype"in $&&$.prototype.render){for(c.state=c.__s,c.__d=!1,R&&R(e),d=c.render(c.props,c.state,c.context),L=0;L<c._sb.length;L++)c.__h.push(c._sb[L]);c._sb=[]}else do c.__d=!1,R&&R(e),d=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++O<25);c.state=c.__s,c.getChildContext!=null&&(n=Ne(Ne({},n),c.getChildContext())),p||c.getSnapshotBeforeUpdate==null||(f=c.getSnapshotBeforeUpdate(h,_)),bo(i,Nn(C=d!=null&&d.type===qn&&d.key==null?d.props.children:d)?C:[C],e,t,n,r,s,o,a,l,u),c.base=e.__e,e.__u&=-161,c.__h.length&&o.push(c),b&&(c.__E=c.__=null)}catch(z){e.__v=null,l||s!=null?(e.__e=a,e.__u|=l?160:32,s[s.indexOf(a)]=null):(e.__e=t.__e,e.__k=t.__k),N.__e(z,e,t)}else s==null&&e.__v===t.__v?(e.__k=t.__k,e.__e=t.__e):e.__e=ll(t.__e,e,t,n,r,s,o,l,u);(d=N.diffed)&&d(e)}function al(i,e,t){e.__d=void 0;for(var n=0;n<t.length;n++)Bn(t[n],t[++n],t[++n]);N.__c&&N.__c(e,i),i.some(function(r){try{i=r.__h,r.__h=[],i.some(function(s){s.call(r)})}catch(s){N.__e(s,r.__v)}})}function ll(i,e,t,n,r,s,o,a,l){var u,d,c,p,h,_,f,b=t.props,m=e.props,w=e.type;if(w==="svg"&&(r=!0),s!=null){for(u=0;u<s.length;u++)if((h=s[u])&&"setAttribute"in h==!!w&&(w?h.localName===w:h.nodeType===3)){i=h,s[u]=null;break}}if(i==null){if(w===null)return document.createTextNode(m);i=r?document.createElementNS("http://www.w3.org/2000/svg",w):document.createElement(w,m.is&&m),s=null,a=!1}if(w===null)b===m||a&&i.data===m||(i.data=m);else{if(s=s&&po.call(i.childNodes),b=t.props||En,!a&&s!=null)for(b={},u=0;u<i.attributes.length;u++)b[(h=i.attributes[u]).name]=h.value;for(u in b)h=b[u],u=="children"||(u=="dangerouslySetInnerHTML"?c=h:u==="key"||u in m||Xt(i,u,null,h,r));for(u in m)h=m[u],u=="children"?p=h:u=="dangerouslySetInnerHTML"?d=h:u=="value"?_=h:u=="checked"?f=h:u==="key"||a&&typeof h!="function"||b[u]===h||Xt(i,u,h,b[u],r);if(d)a||c&&(d.__html===c.__html||d.__html===i.innerHTML)||(i.innerHTML=d.__html),e.__k=[];else if(c&&(i.innerHTML=""),bo(i,Nn(p)?p:[p],e,t,n,r&&w!=="foreignObject",s,o,s?s[0]:t.__k&&mt(t,0),a,l),s!=null)for(u=s.length;u--;)s[u]!=null&&mo(s[u]);a||(u="value",_!==void 0&&(_!==i[u]||w==="progress"&&!_||w==="option"&&_!==b[u])&&Xt(i,u,_,b[u],!1),u="checked",f!==void 0&&f!==i[u]&&Xt(i,u,f,b[u],!1))}return i}function Bn(i,e,t){try{typeof i=="function"?i(e):i.current=e}catch(n){N.__e(n,t)}}function xn(i,e,t){var n,r;if(N.unmount&&N.unmount(i),(n=i.ref)&&(n.current&&n.current!==i.__e||Bn(n,null,e)),(n=i.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(s){N.__e(s,e)}n.base=n.__P=null,i.__c=void 0}if(n=i.__k)for(r=0;r<n.length;r++)n[r]&&xn(n[r],e,t||typeof i.type!="function");t||i.__e==null||mo(i.__e),i.__=i.__e=i.__d=void 0}function cl(i,e,t){return this.constructor(i,t)}po=vo.slice,N={__e:function(i,e,t,n){for(var r,s,o;e=e.__;)if((r=e.__c)&&!r.__)try{if((s=r.constructor)&&s.getDerivedStateFromError!=null&&(r.setState(s.getDerivedStateFromError(i)),o=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(i,n||{}),o=r.__d),o)return r.__E=r}catch(a){i=a}throw i}},_o=0,si.prototype.setState=function(i,e){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=Ne({},this.state),typeof i=="function"&&(i=i(Ne({},t),this.props)),i&&Ne(t,i),i!=null&&this.__v&&(e&&this._sb.push(e),kn(this))},si.prototype.forceUpdate=function(i){this.__v&&(this.__e=!0,i&&this.__h.push(i),kn(this))},si.prototype.render=qn,Ze=[],go=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Sn=function(i,e){return i.__v.__b-e.__v.__b},yi.__r=0,fo=0;var Kr,bi,_e;(function(i,e){var t={__c:e="__cC"+fo++,__:i,Consumer:function(n,r){return n.children(r)},Provider:function(n){var r,s;return this.getChildContext||(r=[],(s={})[e]=this,this.getChildContext=function(){return s},this.shouldComponentUpdate=function(o){this.props.value!==o.value&&r.some(function(a){a.__e=!0,kn(a)})},this.sub=function(o){r.push(o);var a=o.componentWillUnmount;o.componentWillUnmount=function(){r.splice(r.indexOf(o),1),a&&a.call(o)}}),n.children}};t.Provider.__=t.Consumer.contextType=t})({isPreviewMode:!1,previewPageIndex:0,handleCloseSurveyPopup:()=>{},isPopup:!0,onPreviewSubmit:()=>{}}),function(i){i.Popover="popover",i.API="api",i.Widget="widget"}(Kr||(Kr={})),function(i){i.Open="open",i.MultipleChoice="multiple_choice",i.SingleChoice="single_choice",i.Rating="rating",i.Link="link"}(bi||(bi={})),function(i){i.NextQuestion="next_question",i.End="end",i.ResponseBased="response_based",i.SpecificQuestion="specific_question"}(_e||(_e={}));class Eo{constructor(){y(this,"events",{}),this.events={}}on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),()=>{this.events[e]=this.events[e].filter(n=>n!==t)}}emit(e,t){for(var n of this.events[e]||[])n(t);for(var r of this.events["*"]||[])r(e,t)}}class et{constructor(e){y(this,"_debugEventEmitter",new Eo),y(this,"checkStep",(t,n)=>this.checkStepEvent(t,n)&&this.checkStepUrl(t,n)&&this.checkStepElement(t,n)),y(this,"checkStepEvent",(t,n)=>n==null||!n.event||(t==null?void 0:t.event)===(n==null?void 0:n.event)),this.instance=e,this.actionEvents=new Set,this.actionRegistry=new Set}init(){var e;if(!E((e=this.instance)===null||e===void 0?void 0:e._addCaptureHook)){var t;(t=this.instance)===null||t===void 0||t._addCaptureHook((n,r)=>{this.on(n,r)})}}register(e){var t,n;if(!E((t=this.instance)===null||t===void 0?void 0:t._addCaptureHook)&&(e.forEach(o=>{var a,l;(a=this.actionRegistry)===null||a===void 0||a.add(o),(l=o.steps)===null||l===void 0||l.forEach(u=>{var d;(d=this.actionEvents)===null||d===void 0||d.add((u==null?void 0:u.event)||"")})}),(n=this.instance)!==null&&n!==void 0&&n.autocapture)){var r,s=new Set;e.forEach(o=>{var a;(a=o.steps)===null||a===void 0||a.forEach(l=>{l!=null&&l.selector&&s.add(l==null?void 0:l.selector)})}),(r=this.instance)===null||r===void 0||r.autocapture.setElementSelectors(s)}}on(e,t){var n;t!=null&&e.length!=0&&(this.actionEvents.has(e)||this.actionEvents.has(t==null?void 0:t.event))&&this.actionRegistry&&((n=this.actionRegistry)===null||n===void 0?void 0:n.size)>0&&this.actionRegistry.forEach(r=>{this.checkAction(t,r)&&this._debugEventEmitter.emit("actionCaptured",r.name)})}_addActionHook(e){this.onAction("actionCaptured",t=>e(t))}checkAction(e,t){if((t==null?void 0:t.steps)==null)return!1;for(var n of t.steps)if(this.checkStep(e,n))return!0;return!1}onAction(e,t){return this._debugEventEmitter.on(e,t)}checkStepUrl(e,t){if(t!=null&&t.url){var n,r=e==null||(n=e.properties)===null||n===void 0?void 0:n.$current_url;if(!r||typeof r!="string"||!et.matchString(r,t==null?void 0:t.url,(t==null?void 0:t.url_matching)||"contains"))return!1}return!0}static matchString(e,t,n){switch(n){case"regex":return!!g&&_t(e,t);case"exact":return t===e;case"contains":var r=et.escapeStringRegexp(t).replace(/_/g,".").replace(/%/g,".*");return _t(e,r);default:return!1}}static escapeStringRegexp(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}checkStepElement(e,t){if((t!=null&&t.href||t!=null&&t.tag_name||t!=null&&t.text)&&!this.getElementsList(e).some(s=>!(t!=null&&t.href&&!et.matchString(s.href||"",t==null?void 0:t.href,(t==null?void 0:t.href_matching)||"exact"))&&(t==null||!t.tag_name||s.tag_name===(t==null?void 0:t.tag_name))&&!(t!=null&&t.text&&!et.matchString(s.text||"",t==null?void 0:t.text,(t==null?void 0:t.text_matching)||"exact")&&!et.matchString(s.$el_text||"",t==null?void 0:t.text,(t==null?void 0:t.text_matching)||"exact"))))return!1;if(t!=null&&t.selector){var n,r=e==null||(n=e.properties)===null||n===void 0?void 0:n.$element_selectors;if(!r||!r.includes(t==null?void 0:t.selector))return!1}return!0}getElementsList(e){return(e==null?void 0:e.properties.$elements)==null?[]:e==null?void 0:e.properties.$elements}}class zt{constructor(e){this.instance=e,this.eventToSurveys=new Map,this.actionToSurveys=new Map}register(e){var t;E((t=this.instance)===null||t===void 0?void 0:t._addCaptureHook)||(this.setupEventBasedSurveys(e),this.setupActionBasedSurveys(e))}setupActionBasedSurveys(e){var t=e.filter(n=>{var r,s,o,a;return((r=n.conditions)===null||r===void 0?void 0:r.actions)&&((s=n.conditions)===null||s===void 0||(o=s.actions)===null||o===void 0||(a=o.values)===null||a===void 0?void 0:a.length)>0});t.length!==0&&(this.actionMatcher==null&&(this.actionMatcher=new et(this.instance),this.actionMatcher.init(),this.actionMatcher._addActionHook(n=>{this.onAction(n)})),t.forEach(n=>{var r,s,o,a,l,u,d,c,p,h;n.conditions&&(r=n.conditions)!==null&&r!==void 0&&r.actions&&(s=n.conditions)!==null&&s!==void 0&&(o=s.actions)!==null&&o!==void 0&&o.values&&((a=n.conditions)===null||a===void 0||(l=a.actions)===null||l===void 0||(u=l.values)===null||u===void 0?void 0:u.length)>0&&((d=this.actionMatcher)===null||d===void 0||d.register(n.conditions.actions.values),(c=n.conditions)===null||c===void 0||(p=c.actions)===null||p===void 0||(h=p.values)===null||h===void 0||h.forEach(_=>{if(_&&_.name){var f=this.actionToSurveys.get(_.name);f&&f.push(n.id),this.actionToSurveys.set(_.name,f||[n.id])}}))}))}setupEventBasedSurveys(e){var t;e.filter(n=>{var r,s,o,a;return((r=n.conditions)===null||r===void 0?void 0:r.events)&&((s=n.conditions)===null||s===void 0||(o=s.events)===null||o===void 0||(a=o.values)===null||a===void 0?void 0:a.length)>0}).length!==0&&((t=this.instance)===null||t===void 0||t._addCaptureHook((n,r)=>{this.onEvent(n,r)}),e.forEach(n=>{var r,s,o;(r=n.conditions)===null||r===void 0||(s=r.events)===null||s===void 0||(o=s.values)===null||o===void 0||o.forEach(a=>{if(a&&a.name){var l=this.eventToSurveys.get(a.name);l&&l.push(n.id),this.eventToSurveys.set(a.name,l||[n.id])}})}))}onEvent(e,t){var n,r,s=((n=this.instance)===null||n===void 0||(r=n.persistence)===null||r===void 0?void 0:r.props[Gt])||[];if(zt.SURVEY_SHOWN_EVENT_NAME==e&&t&&s.length>0){var o,a=t==null||(o=t.properties)===null||o===void 0?void 0:o.$survey_id;if(a){var l=s.indexOf(a);l>=0&&(s.splice(l,1),this._updateActivatedSurveys(s))}}else this.eventToSurveys.has(e)&&this._updateActivatedSurveys(s.concat(this.eventToSurveys.get(e)||[]))}onAction(e){var t,n,r=((t=this.instance)===null||t===void 0||(n=t.persistence)===null||n===void 0?void 0:n.props[Gt])||[];this.actionToSurveys.has(e)&&this._updateActivatedSurveys(r.concat(this.actionToSurveys.get(e)||[]))}_updateActivatedSurveys(e){var t,n;(t=this.instance)===null||t===void 0||(n=t.persistence)===null||n===void 0||n.register({[Gt]:[...new Set(e)]})}getSurveys(){var e,t,n=(e=this.instance)===null||e===void 0||(t=e.persistence)===null||t===void 0?void 0:t.props[Gt];return n||[]}getEventToSurveys(){return this.eventToSurveys}_getActionMatcher(){return this.actionMatcher}}y(zt,"SURVEY_SHOWN_EVENT_NAME","survey shown");var oe=K("[Surveys]"),Xr={icontains:(i,e)=>i.some(t=>e.toLowerCase().includes(t.toLowerCase())),not_icontains:(i,e)=>i.every(t=>!e.toLowerCase().includes(t.toLowerCase())),regex:(i,e)=>i.some(t=>_t(e,t)),not_regex:(i,e)=>i.every(t=>!_t(e,t)),exact:(i,e)=>i.some(t=>e===t),is_not:(i,e)=>i.every(t=>e!==t)};function ul(i,e,t){var n,r=i.questions[e],s=e+1;if((n=r.branching)===null||n===void 0||!n.type)return e===i.questions.length-1?_e.End:s;if(r.branching.type===_e.End)return _e.End;if(r.branching.type===_e.SpecificQuestion){if(Number.isInteger(r.branching.index))return r.branching.index}else if(r.branching.type===_e.ResponseBased){if(r.type===bi.SingleChoice){var o,a,l=r.choices.indexOf("".concat(t));if((o=r.branching)!==null&&o!==void 0&&(a=o.responseValues)!==null&&a!==void 0&&a.hasOwnProperty(l)){var u=r.branching.responseValues[l];return Number.isInteger(u)?u:u===_e.End?_e.End:s}}else if(r.type===bi.Rating){var d,c;if(typeof t!="number"||!Number.isInteger(t))throw new Error("The response type must be an integer");var p=function(_,f){if(f===3){if(_<1||_>3)throw new Error("The response must be in range 1-3");return _===1?"negative":_===2?"neutral":"positive"}if(f===5){if(_<1||_>5)throw new Error("The response must be in range 1-5");return _<=2?"negative":_===3?"neutral":"positive"}if(f===7){if(_<1||_>7)throw new Error("The response must be in range 1-7");return _<=3?"negative":_===4?"neutral":"positive"}if(f===10){if(_<0||_>10)throw new Error("The response must be in range 0-10");return _<=6?"detractors":_<=8?"passives":"promoters"}throw new Error("The scale must be one of: 3, 5, 7, 10")}(t,r.scale);if((d=r.branching)!==null&&d!==void 0&&(c=d.responseValues)!==null&&c!==void 0&&c.hasOwnProperty(p)){var h=r.branching.responseValues[p];return Number.isInteger(h)?h:h===_e.End?_e.End:s}}return s}return oe.warn("Falling back to next question index due to unexpected branching type"),s}function Qr(i){return i??"icontains"}class dl{constructor(e){y(this,"getNextSurveyStep",ul),this.instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){this._decideServerResponse=!!e.surveys,oe.info("decideServerResponse set to ".concat(this._decideServerResponse)),this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");var e=(()=>{for(var t=[],n=0;n<localStorage.length;n++){var r=localStorage.key(n);r!=null&&r.startsWith("seenSurvey_")&&t.push(r)}return t})();e.forEach(t=>localStorage.removeItem(t))}loadIfEnabled(){if(!this._surveyManager)if(this.instance.config.disable_surveys)oe.info("Disabled. Not loading surveys.");else{var e=P==null?void 0:P.__PosthogExtensions__;if(e){var t=e.generateSurveys;if(this._decideServerResponse)if(this._surveyEventReceiver==null&&(this._surveyEventReceiver=new zt(this.instance)),t)this._surveyManager=t(this.instance);else{var n=e.loadExternalDependency;n?n(this.instance,"surveys",r=>{var s;r?oe.error("Could not load surveys script",r):this._surveyManager=(s=e.generateSurveys)===null||s===void 0?void 0:s.call(e,this.instance)}):oe.error("PostHog loadExternalDependency extension not found. Cannot load remote config.")}else oe.warn("Decide not loaded yet. Not loading surveys.")}else oe.error("PostHog Extensions not found.")}}getSurveys(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];if(this.instance.config.disable_surveys)return oe.info("Disabled. Not loading surveys."),e([]);this._surveyEventReceiver==null&&(this._surveyEventReceiver=new zt(this.instance));var n=this.instance.get_property(tn);if(n&&!t)return e(n);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/surveys/?token=".concat(this.instance.config.token)),method:"GET",callback:r=>{var s,o=r.statusCode;if(o!==200||!r.json)return oe.error("Surveys API could not be loaded, status: ".concat(o)),e([]);var a,l=r.json.surveys||[],u=l.filter(d=>{var c,p,h,_,f,b,m,w,x,I,R,O;return((c=d.conditions)===null||c===void 0?void 0:c.events)&&((p=d.conditions)===null||p===void 0||(h=p.events)===null||h===void 0?void 0:h.values)&&((_=d.conditions)===null||_===void 0||(f=_.events)===null||f===void 0||(b=f.values)===null||b===void 0?void 0:b.length)>0||((m=d.conditions)===null||m===void 0?void 0:m.actions)&&((w=d.conditions)===null||w===void 0||(x=w.actions)===null||x===void 0?void 0:x.values)&&((I=d.conditions)===null||I===void 0||(R=I.actions)===null||R===void 0||(O=R.values)===null||O===void 0?void 0:O.length)>0});return u.length>0&&((a=this._surveyEventReceiver)===null||a===void 0||a.register(u)),(s=this.instance.persistence)===null||s===void 0||s.register({[tn]:l}),e(l)}})}isSurveyFeatureFlagEnabled(e){return!e||this.instance.featureFlags.isFeatureEnabled(e)}getActiveMatchingSurveys(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];this.getSurveys(n=>{var r,s=n.filter(l=>!(!l.start_date||l.end_date)).filter(l=>{var u;if(!l.conditions)return!0;var d=function(h){var _,f,b;if((_=h.conditions)===null||_===void 0||!_.url)return!0;var m=g==null||(f=g.location)===null||f===void 0?void 0:f.href;if(!m)return!1;var w=[h.conditions.url];return Xr[Qr((b=h.conditions)===null||b===void 0?void 0:b.urlMatchType)](w,m)}(l),c=(u=l.conditions)===null||u===void 0||!u.selector||(S==null?void 0:S.querySelector(l.conditions.selector)),p=function(h){var _,f,b;if((_=h.conditions)===null||_===void 0||!_.deviceTypes||((f=h.conditions)===null||f===void 0?void 0:f.deviceTypes.length)===0)return!0;if(!H)return!1;var m=D.deviceType(H);return Xr[Qr((b=h.conditions)===null||b===void 0?void 0:b.deviceTypesMatchType)](h.conditions.deviceTypes,m)}(l);return d&&c&&p}),o=(r=this._surveyEventReceiver)===null||r===void 0?void 0:r.getSurveys(),a=s.filter(l=>{var u,d,c,p,h,_,f,b,m;if(!(l.linked_flag_key||l.targeting_flag_key||l.internal_targeting_flag_key||(u=l.feature_flag_keys)!==null&&u!==void 0&&u.length))return!0;var w=this.isSurveyFeatureFlagEnabled(l.linked_flag_key),x=this.isSurveyFeatureFlagEnabled(l.targeting_flag_key),I=((d=(c=l.conditions)===null||c===void 0||(p=c.events)===null||p===void 0||(h=p.values)===null||h===void 0?void 0:h.length)!==null&&d!==void 0?d:0)>0,R=((_=(f=l.conditions)===null||f===void 0||(b=f.actions)===null||b===void 0||(m=b.values)===null||m===void 0?void 0:m.length)!==null&&_!==void 0?_:0)>0,O=!I&&!R||(o==null?void 0:o.includes(l.id)),L=this._canActivateRepeatedly(l)||this.isSurveyFeatureFlagEnabled(l.internal_targeting_flag_key),C=this.checkFlags(l);return w&&x&&L&&O&&C});return e(a)},t)}checkFlags(e){var t;return(t=e.feature_flag_keys)===null||t===void 0||!t.length||e.feature_flag_keys.every(n=>{var{key:r,value:s}=n;return!r||!s||this.instance.featureFlags.isFeatureEnabled(s)})}_canActivateRepeatedly(e){var t;return F((t=P.__PosthogExtensions__)===null||t===void 0?void 0:t.canActivateRepeatedly)?(oe.warn("init was not called"),!1):P.__PosthogExtensions__.canActivateRepeatedly(e)}canRenderSurvey(e){F(this._surveyManager)?oe.warn("init was not called"):this.getSurveys(t=>{var n=t.filter(r=>r.id===e)[0];this._surveyManager.canRenderSurvey(n)})}renderSurvey(e,t){F(this._surveyManager)?oe.warn("init was not called"):this.getSurveys(n=>{var r=n.filter(s=>s.id===e)[0];this._surveyManager.renderSurvey(r,S==null?void 0:S.querySelector(t))})}}var Zr=K("[RateLimiter]");class hl{constructor(e){var t,n;y(this,"serverLimits",{}),y(this,"lastEventRateLimited",!1),y(this,"checkForLimiting",r=>{var s=r.text;if(s&&s.length)try{(JSON.parse(s).quota_limited||[]).forEach(o=>{Zr.info("".concat(o||"events"," is quota limited.")),this.serverLimits[o]=new Date().getTime()+6e4})}catch(o){return void Zr.warn('could not rate limit - continuing. Error: "'.concat(o==null?void 0:o.message,'"'),{text:s})}}),this.instance=e,this.captureEventsPerSecond=((t=e.config.rate_limiting)===null||t===void 0?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max(((n=e.config.rate_limiting)===null||n===void 0?void 0:n.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(){var e,t,n,r=arguments.length>0&&arguments[0]!==void 0&&arguments[0],s=new Date().getTime(),o=(e=(t=this.instance.persistence)===null||t===void 0?void 0:t.get_property(rn))!==null&&e!==void 0?e:{tokens:this.captureEventsBurstLimit,last:s};o.tokens+=(s-o.last)/1e3*this.captureEventsPerSecond,o.last=s,o.tokens>this.captureEventsBurstLimit&&(o.tokens=this.captureEventsBurstLimit);var a=o.tokens<1;return a||r||(o.tokens=Math.max(0,o.tokens-1)),!a||this.lastEventRateLimited||r||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to ".concat(this.captureEventsPerSecond," events per second and ").concat(this.captureEventsBurstLimit," events burst limit.")},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=a,(n=this.instance.persistence)===null||n===void 0||n.set_property(rn,o),{isRateLimited:a,remainingTokens:o.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return t!==!1&&new Date().getTime()<t}}var pl=i=>D.personInfo({maskPersonalDataProperties:i==null?void 0:i.config.mask_personal_data_properties,customPersonalDataProperties:i==null?void 0:i.config.custom_personal_data_properties});class _l{constructor(e,t,n,r){y(this,"_onSessionIdCallback",s=>{var o=this._getStored();if(!o||o.sessionId!==s){var a={sessionId:s,props:this._sessionSourceParamGenerator(this.instance)};this._persistence.register({[nn]:a})}}),this.instance=e,this._sessionIdManager=t,this._persistence=n,this._sessionSourceParamGenerator=r||pl,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}_getStored(){return this._persistence.props[nn]}getSetOnceInitialSessionPropsProps(){var e,t=(e=this._getStored())===null||e===void 0?void 0:e.props;return t?"r"in t?D.personPropsFromInfo(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}}var gl=["ahrefsbot","ahrefssiteaudit","applebot","baiduspider","better uptime bot","bingbot","bingpreview","bot.htm","bot.php","crawler","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","linkedinbot","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","semrushbot","sitebulb","slurp","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","gptbot","oai-searchbot","chatgpt-user","headlesschrome","cypress","Google-HotelAdsVerifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleweblight","mediapartners-google","storebot-google","Bytespider;"],es=function(i,e){if(!i)return!1;var t=i.toLowerCase();return gl.concat(e||[]).some(n=>{var r=n.toLowerCase();return t.indexOf(r)!==-1})},ko=function(i,e){if(!i)return!1;var t=i.userAgent;if(t&&es(t,e))return!0;try{var n=i==null?void 0:i.userAgentData;if(n!=null&&n.brands&&n.brands.some(r=>es(r==null?void 0:r.brand,e)))return!0}catch{}return!!i.webdriver};class xo{constructor(){this.clicks=[]}isRageClick(e,t,n){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(e-r.x)+Math.abs(t-r.y)<30&&n-r.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:n}),this.clicks.length===3)return!0}else this.clicks=[{x:e,y:t,timestamp:n}];return!1}}var Qt=K("[Dead Clicks]"),fl=()=>!0,vl=i=>{var e,t=!((e=i.instance.persistence)===null||e===void 0||!e.get_property(Rs)),n=i.instance.config.capture_dead_clicks;return De(n)?n:t};class Io{get lazyLoadedDeadClicksAutocapture(){return this._lazyLoadedDeadClicksAutocapture}constructor(e,t,n){this.instance=e,this.isEnabled=t,this.onCapture=n,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[Rs]:e==null?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.loadScript(()=>{this.start()})}loadScript(e){var t,n,r;(t=P.__PosthogExtensions__)!==null&&t!==void 0&&t.initDeadClicksAutocapture&&e(),(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.loadExternalDependency)===null||r===void 0||r.call(n,this.instance,"dead-clicks-autocapture",s=>{s?Qt.error("failed to load script",s):e()})}start(){var e;if(S){if(!this._lazyLoadedDeadClicksAutocapture&&(e=P.__PosthogExtensions__)!==null&&e!==void 0&&e.initDeadClicksAutocapture){var t=B(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this._lazyLoadedDeadClicksAutocapture=P.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this._lazyLoadedDeadClicksAutocapture.start(S),Qt.info("starting...")}}else Qt.error("`document` not found. Cannot start.")}stop(){this._lazyLoadedDeadClicksAutocapture&&(this._lazyLoadedDeadClicksAutocapture.stop(),this._lazyLoadedDeadClicksAutocapture=void 0,Qt.info("stopping..."))}}var ml=K("[Heatmaps]");function ts(i){return B(i)&&"clientX"in i&&"clientY"in i&&Z(i.clientX)&&Z(i.clientY)}class yl{constructor(e){var t;y(this,"rageclicks",new xo),y(this,"_enabledServerSide",!1),y(this,"_initialized",!1),y(this,"_flushInterval",null),this.instance=e,this._enabledServerSide=!((t=this.instance.persistence)===null||t===void 0||!t.props[Zi])}get flushIntervalMilliseconds(){var e=5e3;return B(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return E(this.instance.config.capture_heatmaps)?E(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:this.instance.config.capture_heatmaps!==!1}startIfEnabled(){if(this.isEnabled){if(this._initialized)return;ml.info("starting..."),this._setupListeners(),this._flushInterval=setInterval(this.flush.bind(this),this.flushIntervalMilliseconds)}else{var e,t;clearInterval((e=this._flushInterval)!==null&&e!==void 0?e:void 0),(t=this.deadClicksCapture)===null||t===void 0||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[Zi]:t}),this._enabledServerSide=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.buffer;return this.buffer=void 0,e}_onDeadClick(e){this._onClick(e.originalEvent,"deadclick")}_setupListeners(){g&&S&&(U(g,"beforeunload",this.flush.bind(this)),U(S,"click",e=>this._onClick(e||(g==null?void 0:g.event)),{capture:!0}),U(S,"mousemove",e=>this._onMouseMove(e||(g==null?void 0:g.event)),{capture:!0}),this.deadClicksCapture=new Io(this.instance,fl,this._onDeadClick.bind(this)),this.deadClicksCapture.startIfEnabled(),this._initialized=!0)}_getProperties(e,t){var n=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),s=this.instance.scrollManager.scrollElement(),o=function(a,l,u){for(var d=a;d&&xi(d)&&!Be(d,"body");){if(d===u)return!1;if(T(l,g==null?void 0:g.getComputedStyle(d).position))return!0;d=Qs(d)}return!1}(Xs(e),["fixed","sticky"],s);return{x:e.clientX+(o?0:r),y:e.clientY+(o?0:n),target_fixed:o,type:t}}_onClick(e){var t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"click";if(!Tr(e.target)&&ts(e)){var r=this._getProperties(e,n);(t=this.rageclicks)!==null&&t!==void 0&&t.isRageClick(e.clientX,e.clientY,new Date().getTime())&&this._capture(v(v({},r),{},{type:"rageclick"})),this._capture(r)}}_onMouseMove(e){!Tr(e.target)&&ts(e)&&(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout(()=>{this._capture(this._getProperties(e,"mousemove"))},500))}_capture(e){if(g){var t=g.location.href;this.buffer=this.buffer||{},this.buffer[t]||(this.buffer[t]=[]),this.buffer[t].push(e)}}flush(){this.buffer&&!ct(this.buffer)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class bl{constructor(e){y(this,"_updateScrollData",()=>{var t,n,r,s;this.context||(this.context={});var o=this.scrollElement(),a=this.scrollY(),l=o?Math.max(0,o.scrollHeight-o.clientHeight):0,u=a+((o==null?void 0:o.clientHeight)||0),d=(o==null?void 0:o.scrollHeight)||0;this.context.lastScrollY=Math.ceil(a),this.context.maxScrollY=Math.max(a,(t=this.context.maxScrollY)!==null&&t!==void 0?t:0),this.context.maxScrollHeight=Math.max(l,(n=this.context.maxScrollHeight)!==null&&n!==void 0?n:0),this.context.lastContentY=u,this.context.maxContentY=Math.max(u,(r=this.context.maxContentY)!==null&&r!==void 0?r:0),this.context.maxContentHeight=Math.max(d,(s=this.context.maxContentHeight)!==null&&s!==void 0?s:0)}),this.instance=e}getContext(){return this.context}resetContext(){var e=this.context;return setTimeout(this._updateScrollData,0),e}startMeasuringScrollPosition(){U(g,"scroll",this._updateScrollData,{capture:!0}),U(g,"scrollend",this._updateScrollData,{capture:!0}),U(g,"resize",this._updateScrollData)}scrollElement(){if(!this.instance.config.scroll_root_selector)return g==null?void 0:g.document.documentElement;var e=q(this.instance.config.scroll_root_selector)?this.instance.config.scroll_root_selector:[this.instance.config.scroll_root_selector];for(var t of e){var n=g==null?void 0:g.document.querySelector(t);if(n)return n}}scrollY(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return g&&(g.scrollY||g.pageYOffset||g.document.documentElement.scrollTop)||0}scrollX(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return g&&(g.scrollX||g.pageXOffset||g.document.documentElement.scrollLeft)||0}}var is=K("[AutoCapture]");function Ji(i,e){return e.length>i?e.slice(0,i)+"...":e}function wl(i){if(i.previousElementSibling)return i.previousElementSibling;var e=i;do e=e.previousSibling;while(e&&!xi(e));return e}function Sl(i,e,t,n){var r=i.tagName.toLowerCase(),s={tag_name:r};hn.indexOf(r)>-1&&!t&&(r.toLowerCase()==="a"||r.toLowerCase()==="button"?s.$el_text=Ji(1024,io(i)):s.$el_text=Ji(1024,Ii(i)));var o=fi(i);o.length>0&&(s.classes=o.filter(function(d){return d!==""})),A(i.attributes,function(d){var c;if((!Zs(i)||["name","id","class","aria-label"].indexOf(d.name)!==-1)&&(n==null||!n.includes(d.name))&&!e&&ft(d.value)&&(c=d.name,!V(c)||c.substring(0,10)!=="_ngcontent"&&c.substring(0,7)!=="_nghost")){var p=d.value;d.name==="class"&&(p=$n(p).join(" ")),s["attr__"+d.name]=Ji(1024,p)}});for(var a=1,l=1,u=i;u=wl(u);)a++,u.tagName===i.tagName&&l++;return s.nth_child=a,s.nth_of_type=l,s}function El(i,e){for(var t,n,{e:r,maskAllElementAttributes:s,maskAllText:o,elementAttributeIgnoreList:a,elementsChainAsString:l}=e,u=[i],d=i;d.parentNode&&!Be(d,"body");)Ys(d.parentNode)?(u.push(d.parentNode.host),d=d.parentNode.host):(u.push(d.parentNode),d=d.parentNode);var c,p=[],h={},_=!1,f=!1;if(A(u,I=>{var R=pn(I);I.tagName.toLowerCase()==="a"&&(_=I.getAttribute("href"),_=R&&_&&ft(_)&&_),T(fi(I),"ph-no-capture")&&(f=!0),p.push(Sl(I,s,o,a));var O=function(L){if(!pn(L))return{};var C={};return A(L.attributes,function($){if($.name&&$.name.indexOf("data-ph-capture-attribute")===0){var z=$.name.replace("data-ph-capture-attribute-",""),pe=$.value;z&&pe&&ft(pe)&&(C[z]=pe)}}),C}(I);G(h,O)}),f)return{props:{},explicitNoCapture:f};if(o||(i.tagName.toLowerCase()==="a"||i.tagName.toLowerCase()==="button"?p[0].$el_text=io(i):p[0].$el_text=Ii(i)),_){var b,m;p[0].attr__href=_;var w=(b=dt(_))===null||b===void 0?void 0:b.host,x=g==null||(m=g.location)===null||m===void 0?void 0:m.host;w&&x&&w!==x&&(c=_)}return{props:G({$event_type:r.type,$ce_version:1},l?{}:{$elements:p},{$elements_chain:xa(p)},(t=p[0])!==null&&t!==void 0&&t.$el_text?{$el_text:(n=p[0])===null||n===void 0?void 0:n.$el_text}:{},c&&r.type==="click"?{$external_click_url:c}:{},h)}}class kl{constructor(e){y(this,"_initialized",!1),y(this,"_isDisabledServerSide",null),y(this,"rageclicks",new xo),y(this,"_elementsChainAsString",!1),this.instance=e,this._elementSelectors=null}get config(){var e,t,n=B(this.instance.config.autocapture)?this.instance.config.autocapture:{};return n.url_allowlist=(e=n.url_allowlist)===null||e===void 0?void 0:e.map(r=>new RegExp(r)),n.url_ignorelist=(t=n.url_ignorelist)===null||t===void 0?void 0:t.map(r=>new RegExp(r)),n}_addDomEventHandlers(){if(this.isBrowserSupported()){if(g&&S){var e=n=>{n=n||(g==null?void 0:g.event);try{this._captureEvent(n)}catch(r){is.error("Failed to capture event",r)}};if(U(S,"submit",e,{capture:!0}),U(S,"change",e,{capture:!0}),U(S,"click",e,{capture:!0}),this.config.capture_copied_text){var t=n=>{n=n||(g==null?void 0:g.event),this._captureEvent(n,Mi)};U(S,"copy",t,{capture:!0}),U(S,"cut",t,{capture:!0})}}}else is.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[ur]:!!e.autocapture_opt_out}),this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this._elementSelectors=e}getElementSelectors(e){var t,n=[];return(t=this._elementSelectors)===null||t===void 0||t.forEach(r=>{var s=S==null?void 0:S.querySelectorAll(r);s==null||s.forEach(o=>{e===o&&n.push(r)})}),n}get isEnabled(){var e,t,n=(e=this.instance.persistence)===null||e===void 0?void 0:e.props[ur],r=this._isDisabledServerSide;if(je(r)&&!De(n)&&!this.instance.config.advanced_disable_decide)return!1;var s=(t=this._isDisabledServerSide)!==null&&t!==void 0?t:!!n;return!!this.instance.config.autocapture&&!s}_captureEvent(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"$autocapture";if(this.isEnabled){var n,r=Xs(e);Js(r)&&(r=r.parentNode||null),t==="$autocapture"&&e.type==="click"&&e instanceof MouseEvent&&this.instance.config.rageclick&&(n=this.rageclicks)!==null&&n!==void 0&&n.isRageClick(e.clientX,e.clientY,new Date().getTime())&&this._captureEvent(e,"$rageclick");var s=t===Mi;if(r&&ba(r,e,this.config,s,s?["copy","cut"]:void 0)){var{props:o,explicitNoCapture:a}=El(r,{e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.config.element_attribute_ignorelist,elementsChainAsString:this._elementsChainAsString});if(a)return!1;var l=this.getElementSelectors(r);if(l&&l.length>0&&(o.$element_selectors=l),t===Mi){var u,d=Ks(g==null||(u=g.getSelection())===null||u===void 0?void 0:u.toString()),c=e.type||"clipboard";if(!d)return!1;o.$selected_content=d,o.$copy_type=c}return this.instance.capture(t,o),!0}}}isBrowserSupported(){return le(S==null?void 0:S.querySelectorAll)}}var xl=K("[TracingHeaders]");class Il{constructor(e){y(this,"_restoreXHRPatch",void 0),y(this,"_restoreFetchPatch",void 0),y(this,"_startCapturing",()=>{var t,n,r,s;E(this._restoreXHRPatch)&&((t=P.__PosthogExtensions__)===null||t===void 0||(n=t.tracingHeadersPatchFns)===null||n===void 0||n._patchXHR(this.instance.sessionManager)),E(this._restoreFetchPatch)&&((r=P.__PosthogExtensions__)===null||r===void 0||(s=r.tracingHeadersPatchFns)===null||s===void 0||s._patchFetch(this.instance.sessionManager))}),this.instance=e}_loadScript(e){var t,n,r;(t=P.__PosthogExtensions__)!==null&&t!==void 0&&t.tracingHeadersPatchFns&&e(),(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.loadExternalDependency)===null||r===void 0||r.call(n,this.instance,"tracing-headers",s=>{if(s)return xl.error("failed to load script",s);e()})}startIfEnabledOrStop(){var e,t;this.instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):((e=this._restoreXHRPatch)===null||e===void 0||e.call(this),(t=this._restoreFetchPatch)===null||t===void 0||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)}}var $e;(function(i){i[i.PENDING=-1]="PENDING",i[i.DENIED=0]="DENIED",i[i.GRANTED=1]="GRANTED"})($e||($e={}));class Pl{constructor(e){this.instance=e}get config(){return this.instance.config}get consent(){return this.getDnt()?$e.DENIED:this.storedConsent}isOptedOut(){return this.consent===$e.DENIED||this.consent===$e.PENDING&&this.config.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.storage.set(this.storageKey,e?1:0,this.config.cookie_expiration,this.config.cross_subdomain_cookie,this.config.secure_cookie)}reset(){this.storage.remove(this.storageKey,this.config.cross_subdomain_cookie)}get storageKey(){var{token:e,opt_out_capturing_cookie_prefix:t}=this.instance.config;return(t||"__ph_opt_in_out_")+e}get storedConsent(){var e=this.storage.get(this.storageKey);return e==="1"?$e.GRANTED:e==="0"?$e.DENIED:$e.PENDING}get storage(){if(!this._storage){var e=this.config.opt_out_capturing_persistence_type;this._storage=e==="localStorage"?j:Re;var t=e==="localStorage"?Re:j;t.get(this.storageKey)&&(this._storage.get(this.storageKey)||this.optInOut(t.get(this.storageKey)==="1"),t.remove(this.storageKey,this.config.cross_subdomain_cookie))}return this._storage}getDnt(){return!!this.config.respect_dnt&&!!Es([ne==null?void 0:ne.doNotTrack,ne==null?void 0:ne.msDoNotTrack,P.doNotTrack],e=>T([!0,1,"1","yes"],e))}}var Zt=K("[ExceptionAutocapture]");class Cl{constructor(e){var t;y(this,"startCapturing",()=>{var n,r,s,o;if(g&&this.isEnabled&&!this.hasHandlers){var a=(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.errorWrappingFunctions)===null||r===void 0?void 0:r.wrapOnError,l=(s=P.__PosthogExtensions__)===null||s===void 0||(o=s.errorWrappingFunctions)===null||o===void 0?void 0:o.wrapUnhandledRejection;if(a&&l)try{this.unwrapOnError=a(this.captureException.bind(this)),this.unwrapUnhandledRejection=l(this.captureException.bind(this))}catch(u){Zt.error("failed to start",u),this.stopCapturing()}else Zt.error("failed to load error wrapping functions - cannot start")}}),this.instance=e,this.remoteEnabled=!((t=this.instance.persistence)===null||t===void 0||!t.props[dr]),this.startIfEnabled()}get isEnabled(){var e;return De(this.instance.config.capture_exceptions)?this.instance.config.capture_exceptions:(e=this.remoteEnabled)!==null&&e!==void 0&&e}get hasHandlers(){return!E(this.unwrapOnError)}startIfEnabled(){this.isEnabled&&!this.hasHandlers&&(Zt.info("enabled, starting..."),this.loadScript(this.startCapturing))}loadScript(e){var t,n;this.hasHandlers&&e(),(t=P.__PosthogExtensions__)===null||t===void 0||(n=t.loadExternalDependency)===null||n===void 0||n.call(t,this.instance,"exception-autocapture",r=>{if(r)return Zt.error("failed to load script",r);e()})}stopCapturing(){var e,t;(e=this.unwrapOnError)===null||e===void 0||e.call(this),this.unwrapOnError=void 0,(t=this.unwrapUnhandledRejection)===null||t===void 0||t.call(this),this.unwrapUnhandledRejection=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this.remoteEnabled=!!t||!1,this.instance.persistence&&this.instance.persistence.register({[dr]:this.remoteEnabled}),this.startIfEnabled()}captureException(e){var t=this.instance.requestRouter.endpointFor("ui");e.$exception_personURL="".concat(t,"/project/").concat(this.instance.config.token,"/person/").concat(this.instance.get_distinct_id()),this.instance.exceptions.sendExceptionEvent(e)}}var Ye=K("[Web Vitals]"),ns=9e5;class Tl{constructor(e){var t;y(this,"_enabledServerSide",!1),y(this,"_initialized",!1),y(this,"buffer",{url:void 0,metrics:[],firstMetricTimestamp:void 0}),y(this,"_flushToCapture",()=>{clearTimeout(this._delayedFlushTimer),this.buffer.metrics.length!==0&&(this.instance.capture("$web_vitals",this.buffer.metrics.reduce((n,r)=>v(v({},n),{},{["$web_vitals_".concat(r.name,"_event")]:v({},r),["$web_vitals_".concat(r.name,"_value")]:r.value}),{})),this.buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})}),y(this,"_addToBuffer",n=>{var r,s=(r=this.instance.sessionManager)===null||r===void 0?void 0:r.checkAndGetSessionAndWindowId(!0);if(E(s))Ye.error("Could not read session ID. Dropping metrics!");else{this.buffer=this.buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var o=this._currentURL();E(o)||(F(n==null?void 0:n.name)||F(n==null?void 0:n.value)?Ye.error("Invalid metric received",n):this._maxAllowedValue&&n.value>=this._maxAllowedValue?Ye.error("Ignoring metric with value >= "+this._maxAllowedValue,n):(this.buffer.url!==o&&(this._flushToCapture(),this._delayedFlushTimer=setTimeout(this._flushToCapture,this.flushToCaptureTimeoutMs)),E(this.buffer.url)&&(this.buffer.url=o),this.buffer.firstMetricTimestamp=E(this.buffer.firstMetricTimestamp)?Date.now():this.buffer.firstMetricTimestamp,n.attribution&&n.attribution.interactionTargetElement&&(n.attribution.interactionTargetElement=void 0),this.buffer.metrics.push(v(v({},n),{},{$current_url:o,$session_id:s.sessionId,$window_id:s.windowId,timestamp:Date.now()})),this.buffer.metrics.length===this.allowedMetrics.length&&this._flushToCapture()))}}),y(this,"_startCapturing",()=>{var n,r,s,o,a=P.__PosthogExtensions__;E(a)||E(a.postHogWebVitalsCallbacks)||({onLCP:n,onCLS:r,onFCP:s,onINP:o}=a.postHogWebVitalsCallbacks),n&&r&&s&&o?(this.allowedMetrics.indexOf("LCP")>-1&&n(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&r(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&s(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&o(this._addToBuffer.bind(this)),this._initialized=!0):Ye.error("web vitals callbacks not loaded - not starting")}),this.instance=e,this._enabledServerSide=!((t=this.instance.persistence)===null||t===void 0||!t.props[hr]),this.startIfEnabled()}get allowedMetrics(){var e,t,n=B(this.instance.config.capture_performance)?(e=this.instance.config.capture_performance)===null||e===void 0?void 0:e.web_vitals_allowed_metrics:void 0;return E(n)?((t=this.instance.persistence)===null||t===void 0?void 0:t.props[pr])||["CLS","FCP","INP","LCP"]:n}get flushToCaptureTimeoutMs(){return(B(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get _maxAllowedValue(){var e=B(this.instance.config.capture_performance)&&Z(this.instance.config.capture_performance.__web_vitals_max_value)?this.instance.config.capture_performance.__web_vitals_max_value:ns;return 0<e&&e<=6e4?ns:e}get isEnabled(){var e=B(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals:void 0;return De(e)?e:this._enabledServerSide}startIfEnabled(){this.isEnabled&&!this._initialized&&(Ye.info("enabled, starting..."),this.loadScript(this._startCapturing))}onRemoteConfig(e){var t=B(e.capturePerformance)&&!!e.capturePerformance.web_vitals,n=B(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this.instance.persistence&&(this.instance.persistence.register({[hr]:t}),this.instance.persistence.register({[pr]:n})),this._enabledServerSide=t,this.startIfEnabled()}loadScript(e){var t,n,r;(t=P.__PosthogExtensions__)!==null&&t!==void 0&&t.postHogWebVitalsCallbacks&&e(),(n=P.__PosthogExtensions__)===null||n===void 0||(r=n.loadExternalDependency)===null||r===void 0||r.call(n,this.instance,"web-vitals",s=>{s?Ye.error("failed to load script",s):e()})}_currentURL(){var e=g?g.location.href:void 0;return e||Ye.error("Could not determine current URL"),e}}var Rl={icontains:(i,e)=>!!g&&e.href.toLowerCase().indexOf(i.toLowerCase())>-1,not_icontains:(i,e)=>!!g&&e.href.toLowerCase().indexOf(i.toLowerCase())===-1,regex:(i,e)=>!!g&&_t(e.href,i),not_regex:(i,e)=>!!g&&!_t(e.href,i),exact:(i,e)=>e.href===i,is_not:(i,e)=>e.href!==i};class Y{constructor(e){var t=this;y(this,"getWebExperimentsAndEvaluateDisplayLogic",function(){var n=arguments.length>0&&arguments[0]!==void 0&&arguments[0];t.getWebExperiments(r=>{Y.logInfo("retrieved web experiments from the server"),t._flagToExperiments=new Map,r.forEach(s=>{if(s.feature_flag_key){var o;t._flagToExperiments&&(Y.logInfo("setting flag key ",s.feature_flag_key," to web experiment ",s),(o=t._flagToExperiments)===null||o===void 0||o.set(s.feature_flag_key,s));var a=t.instance.getFeatureFlag(s.feature_flag_key);V(a)&&s.variants[a]&&t.applyTransforms(s.name,a,s.variants[a].transforms)}else if(s.variants)for(var l in s.variants){var u=s.variants[l];Y.matchesTestVariant(u)&&t.applyTransforms(s.name,l,u.transforms)}})},n)}),this.instance=e,this.instance.onFeatureFlags(n=>{this.onFeatureFlags(n)})}onFeatureFlags(e){if(this._is_bot())Y.logInfo("Refusing to render web experiment since the viewer is a likely bot");else if(!this.instance.config.disable_web_experiments){if(F(this._flagToExperiments))return this._flagToExperiments=new Map,this.loadIfEnabled(),void this.previewWebExperiment();Y.logInfo("applying feature flags",e),e.forEach(t=>{var n;if(this._flagToExperiments&&(n=this._flagToExperiments)!==null&&n!==void 0&&n.has(t)){var r,s=this.instance.getFeatureFlag(t),o=(r=this._flagToExperiments)===null||r===void 0?void 0:r.get(t);s&&o!=null&&o.variants[s]&&this.applyTransforms(o.name,s,o.variants[s].transforms)}})}}previewWebExperiment(){var e=Y.getWindowLocation();if(e!=null&&e.search){var t=hi(e==null?void 0:e.search,"__experiment_id"),n=hi(e==null?void 0:e.search,"__experiment_variant");t&&n&&(Y.logInfo("previewing web experiments ".concat(t," && ").concat(n)),this.getWebExperiments(r=>{this.showPreviewWebExperiment(parseInt(t),n,r)},!1,!0))}}loadIfEnabled(){this.instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,n){if(this.instance.config.disable_web_experiments&&!n)return e([]);var r=this.instance.get_property("$web_experiments");if(r&&!t)return e(r);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/web_experiments/?token=".concat(this.instance.config.token)),method:"GET",callback:s=>{if(s.statusCode!==200||!s.json)return e([]);var o=s.json.experiments||[];return e(o)}})}showPreviewWebExperiment(e,t,n){var r=n.filter(s=>s.id===e);r&&r.length>0&&(Y.logInfo("Previewing web experiment [".concat(r[0].name,"] with variant [").concat(t,"]")),this.applyTransforms(r[0].name,t,r[0].variants[t].transforms))}static matchesTestVariant(e){return!F(e.conditions)&&Y.matchUrlConditions(e)&&Y.matchUTMConditions(e)}static matchUrlConditions(e){var t;if(F(e.conditions)||F((t=e.conditions)===null||t===void 0?void 0:t.url))return!0;var n,r,s,o=Y.getWindowLocation();return!!o&&((n=e.conditions)===null||n===void 0||!n.url||Rl[(r=(s=e.conditions)===null||s===void 0?void 0:s.urlMatchType)!==null&&r!==void 0?r:"icontains"](e.conditions.url,o))}static getWindowLocation(){return g==null?void 0:g.location}static matchUTMConditions(e){var t;if(F(e.conditions)||F((t=e.conditions)===null||t===void 0?void 0:t.utm))return!0;var n=D.campaignParams();if(n.utm_source){var r,s,o,a,l,u,d,c,p,h,_,f,b,m,w,x,I=(r=e.conditions)===null||r===void 0||(s=r.utm)===null||s===void 0||!s.utm_campaign||((o=e.conditions)===null||o===void 0||(a=o.utm)===null||a===void 0?void 0:a.utm_campaign)==n.utm_campaign,R=(l=e.conditions)===null||l===void 0||(u=l.utm)===null||u===void 0||!u.utm_source||((d=e.conditions)===null||d===void 0||(c=d.utm)===null||c===void 0?void 0:c.utm_source)==n.utm_source,O=(p=e.conditions)===null||p===void 0||(h=p.utm)===null||h===void 0||!h.utm_medium||((_=e.conditions)===null||_===void 0||(f=_.utm)===null||f===void 0?void 0:f.utm_medium)==n.utm_medium,L=(b=e.conditions)===null||b===void 0||(m=b.utm)===null||m===void 0||!m.utm_term||((w=e.conditions)===null||w===void 0||(x=w.utm)===null||x===void 0?void 0:x.utm_term)==n.utm_term;return I&&O&&L&&R}return!1}static logInfo(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];k.info("[WebExperiments] ".concat(e),n)}applyTransforms(e,t,n){this._is_bot()?Y.logInfo("Refusing to render web experiment since the viewer is a likely bot"):t!=="control"?n.forEach(r=>{if(r.selector){var s;Y.logInfo("applying transform of variant ".concat(t," for experiment ").concat(e," "),r);var o=(s=document)===null||s===void 0?void 0:s.querySelectorAll(r.selector);o==null||o.forEach(a=>{var l=a;r.html&&(l.innerHTML=r.html),r.css&&l.setAttribute("style",r.css)})}}):Y.logInfo("Control variants leave the page unmodified.")}_is_bot(){return ne&&this.instance?ko(ne,this.instance.config.custom_blocked_useragents):void 0}}class Fl{constructor(e){this.instance=e}sendExceptionEvent(e){this.instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"})}}var Ol=["$set_once","$set"],Ke=K("[SiteApps]");class Al{constructor(e){this.instance=e,this.bufferedInvocations=[],this.apps={}}get isEnabled(){return!!this.instance.config.opt_in_site_apps}eventCollector(e,t){if(t){var n=this.globalsForEvent(t);this.bufferedInvocations.push(n),this.bufferedInvocations.length>1e3&&(this.bufferedInvocations=this.bufferedInvocations.slice(10))}}get siteAppLoaders(){var e,t;return(e=P._POSTHOG_REMOTE_CONFIG)===null||e===void 0||(t=e[this.instance.config.token])===null||t===void 0?void 0:t.siteApps}init(){if(this.isEnabled){var e=this.instance._addCaptureHook(this.eventCollector.bind(this));this.stopBuffering=()=>{e(),this.bufferedInvocations=[],this.stopBuffering=void 0}}}globalsForEvent(e){var t,n,r,s,o,a,l;if(!e)throw new Error("Event payload is required");var u={},d=this.instance.get_property("$groups")||[],c=this.instance.get_property("$stored_group_properties")||{};for(var[p,h]of Object.entries(c))u[p]={id:d[p],type:p,properties:h};var{$set_once:_,$set:f}=e;return{event:v(v({},Cs(e,Ol)),{},{properties:v(v(v({},e.properties),f?{$set:v(v({},(t=(n=e.properties)===null||n===void 0?void 0:n.$set)!==null&&t!==void 0?t:{}),f)}:{}),_?{$set_once:v(v({},(r=(s=e.properties)===null||s===void 0?void 0:s.$set_once)!==null&&r!==void 0?r:{}),_)}:{}),elements_chain:(o=(a=e.properties)===null||a===void 0?void 0:a.$elements_chain)!==null&&o!==void 0?o:"",distinct_id:(l=e.properties)===null||l===void 0?void 0:l.distinct_id}),person:{properties:this.instance.get_property("$stored_person_properties")},groups:u}}setupSiteApp(e){var t={id:e.id,loaded:!1,errored:!1};this.apps[e.id]=t;var n=s=>{var o;for(var a of(this.apps[e.id].errored=!s,this.apps[e.id].loaded=!0,Ke.info("Site app with id ".concat(e.id," ").concat(s?"loaded":"errored")),s&&this.bufferedInvocations.length&&(Ke.info("Processing ".concat(this.bufferedInvocations.length," events for site app with id ").concat(e.id)),this.bufferedInvocations.forEach(l=>{var u;return(u=t.processEvent)===null||u===void 0?void 0:u.call(t,l)})),Object.values(this.apps)))if(!a.loaded)return;(o=this.stopBuffering)===null||o===void 0||o.call(this)};try{var{processEvent:r}=e.init({posthog:this.instance,callback:s=>{n(s)}});r&&(t.processEvent=r)}catch(s){Ke.error("Error while initializing PostHog app with config id ".concat(e.id),s),n(!1)}}onCapturedEvent(e){if(Object.keys(this.apps).length!==0){var t=this.globalsForEvent(e);for(var n of Object.values(this.apps))try{var r;(r=n.processEvent)===null||r===void 0||r.call(n,t)}catch(s){Ke.error("Error while processing event ".concat(e.event," for site app ").concat(n.id),s)}}}onRemoteConfig(e){var t,n,r,s=this;if((t=this.siteAppLoaders)!==null&&t!==void 0&&t.length){if(!this.isEnabled)return void Ke.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');for(var o of this.siteAppLoaders)this.setupSiteApp(o);this.instance.on("eventCaptured",d=>this.onCapturedEvent(d))}else if((n=this.stopBuffering)===null||n===void 0||n.call(this),(r=e.siteApps)!==null&&r!==void 0&&r.length)if(this.isEnabled){var a=function(d,c){var p,h;P["__$$ph_site_app_".concat(d)]=s.instance,(p=P.__PosthogExtensions__)===null||p===void 0||(h=p.loadSiteApp)===null||h===void 0||h.call(p,s.instance,c,_=>{if(_)return Ke.error("Error while initializing PostHog app with config id ".concat(d),_)})};for(var{id:l,url:u}of e.siteApps)a(l,u)}else Ke.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}function Yi(i,e,t){return At({distinct_id:i,userPropertiesToSet:e,userPropertiesToSetOnce:t})}var Dt={},In=()=>{},lt="posthog",Po=!Ka&&(H==null?void 0:H.indexOf("MSIE"))===-1&&(H==null?void 0:H.indexOf("Mozilla"))===-1,rs=()=>{var i;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:Wo(S==null?void 0:S.location),persistence:"localStorage+cookie",persistence_name:"",loaded:In,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:"if_capture_pageview",debug:ae&&V(ae==null?void 0:ae.search)&&ae.search.indexOf("__posthog_debug=true")!==-1||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:(g==null||(i=g.location)===null||i===void 0?void 0:i.protocol)==="https:",ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;k.error(t)},get_device_id:e=>e,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:wn},_onCapture:In}},ss=i=>{var e={};E(i.process_person)||(e.person_profiles=i.process_person),E(i.xhr_headers)||(e.request_headers=i.xhr_headers),E(i.cookie_name)||(e.persistence_name=i.cookie_name),E(i.disable_cookie)||(e.disable_persistence=i.disable_cookie),E(i.store_google)||(e.save_campaign_params=i.store_google),E(i.verbose)||(e.debug=i.verbose);var t=G({},e,i);return q(i.property_blacklist)&&(E(i.property_denylist)?t.property_denylist=i.property_blacklist:q(i.property_denylist)?t.property_denylist=[...i.property_blacklist,...i.property_denylist]:k.error("Invalid value for property_denylist config: "+i.property_denylist)),t};class Ml{constructor(){y(this,"__forceAllowLocalhost",!1)}get _forceAllowLocalhost(){return this.__forceAllowLocalhost}set _forceAllowLocalhost(e){k.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class Pi{get decideEndpointWasHit(){var e,t;return(e=(t=this.featureFlags)===null||t===void 0?void 0:t.hasLoadedFlags)!==null&&e!==void 0&&e}constructor(){y(this,"webPerformance",new Ml),y(this,"_personProcessingSetOncePropertiesSent",!1),y(this,"version",Ce.LIB_VERSION),y(this,"_internalEventEmitter",new Eo),this.config=rs(),this.SentryIntegration=tl,this.sentryIntegration=e=>function(t,n){var r=ho(t,n);return{name:uo,processEvent:s=>r(s)}}(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this._initialPageviewCaptured=!1,this._initialPersonProfilesConfig=null,this._cachedIdentify=null,this.featureFlags=new Qo(this),this.toolbar=new Va(this),this.scrollManager=new bl(this),this.pageViewManager=new nl(this),this.surveys=new dl(this),this.experiments=new Y(this),this.exceptions=new Fl(this),this.rateLimiter=new hl(this),this.requestRouter=new el(this),this.consent=new Pl(this),this.people={set:(e,t,n)=>{var r=V(e)?{[e]:t}:e;this.setPersonProperties(r),n==null||n({})},set_once:(e,t,n)=>{var r=V(e)?{[e]:t}:e;this.setPersonProperties(void 0,r),n==null||n({})}},this.on("eventCaptured",e=>k.info('send "'.concat(e==null?void 0:e.event,'"'),e))}init(e,t,n){if(n&&n!==lt){var r,s=(r=Dt[n])!==null&&r!==void 0?r:new Pi;return s._init(e,t,n),Dt[n]=s,Dt[lt][n]=s,s}return this._init(e,t,n)}_init(e){var t,n,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;if(E(e)||ar(e))return k.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return k.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this._triggered_notifs=[],r.person_profiles&&(this._initialPersonProfilesConfig=r.person_profiles),this.set_config(G({},rs(),ss(r),{name:s,token:e})),this.config.on_xhr_error&&k.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=r.disable_compression?void 0:fe.GZipJS,this.persistence=new ji(this.config),this.sessionPersistence=this.config.persistence==="sessionStorage"||this.config.persistence==="memory"?this.persistence:new ji(v(v({},this.config),{},{persistence:"sessionStorage"}));var o=v({},this.persistence.props),a=v({},this.sessionPersistence.props);if(this._requestQueue=new Ja(m=>this._send_retriable_request(m),this.config.request_queue_config),this._retryQueue=new Qa(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Za(this),this.sessionPropsManager=new _l(this,this.sessionManager,this.persistence)),new Il(this).startIfEnabledOrStop(),this.siteApps=new Al(this),(t=this.siteApps)===null||t===void 0||t.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new za(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new kl(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new yl(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Tl(this),this.exceptionObserver=new Cl(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Io(this,vl),this.deadClicksAutocapture.startIfEnabled(),Ce.DEBUG=Ce.DEBUG||this.config.debug,Ce.DEBUG&&k.info("Starting in debug mode",{this:this,config:r,thisC:v({},this.config),p:o,s:a}),this._sync_opt_out_with_persistence(),((n=r.bootstrap)===null||n===void 0?void 0:n.distinctID)!==void 0){var l,u,d=this.config.get_device_id(Le()),c=(l=r.bootstrap)!==null&&l!==void 0&&l.isIdentifiedID?d:r.bootstrap.distinctID;this.persistence.set_property(ke,(u=r.bootstrap)!==null&&u!==void 0&&u.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:r.bootstrap.distinctID,$device_id:c})}if(this._hasBootstrappedFeatureFlags()){var p,h,_=Object.keys(((p=r.bootstrap)===null||p===void 0?void 0:p.featureFlags)||{}).filter(m=>{var w,x;return!((w=r.bootstrap)===null||w===void 0||(x=w.featureFlags)===null||x===void 0||!x[m])}).reduce((m,w)=>{var x,I;return m[w]=((x=r.bootstrap)===null||x===void 0||(I=x.featureFlags)===null||I===void 0?void 0:I[w])||!1,m},{}),f=Object.keys(((h=r.bootstrap)===null||h===void 0?void 0:h.featureFlagPayloads)||{}).filter(m=>_[m]).reduce((m,w)=>{var x,I,R,O;return(x=r.bootstrap)!==null&&x!==void 0&&(I=x.featureFlagPayloads)!==null&&I!==void 0&&I[w]&&(m[w]=(R=r.bootstrap)===null||R===void 0||(O=R.featureFlagPayloads)===null||O===void 0?void 0:O[w]),m},{});this.featureFlags.receivedFeatureFlags({featureFlags:_,featureFlagPayloads:f})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:br,$device_id:null},"");else if(!this.get_distinct_id()){var b=this.config.get_device_id(Le());this.register_once({distinct_id:b,$device_id:b},""),this.persistence.set_property(ke,"anonymous")}return U(g,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),r.segment?il(this,()=>this._loaded()):this._loaded(),le(this.config._onCapture)&&this.config._onCapture!==In&&(k.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",m=>this.config._onCapture(m.event,m))),this}_onRemoteConfig(e){var t,n,r,s,o,a,l,u;if(!S||!S.body)return k.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this._onRemoteConfig(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=T(e.supportedCompression,fe.GZipJS)?fe.GZipJS:T(e.supportedCompression,fe.Base64)?fe.Base64:void 0),(t=e.analytics)!==null&&t!==void 0&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this._initialPersonProfilesConfig?this._initialPersonProfilesConfig:"identified_only"}),(n=this.siteApps)===null||n===void 0||n.onRemoteConfig(e),(r=this.sessionRecording)===null||r===void 0||r.onRemoteConfig(e),(s=this.autocapture)===null||s===void 0||s.onRemoteConfig(e),(o=this.heatmaps)===null||o===void 0||o.onRemoteConfig(e),this.surveys.onRemoteConfig(e),(a=this.webVitalsAutocapture)===null||a===void 0||a.onRemoteConfig(e),(l=this.exceptionObserver)===null||l===void 0||l.onRemoteConfig(e),(u=this.deadClicksAutocapture)===null||u===void 0||u.onRemoteConfig(e)}_loaded(){try{this.config.loaded(this)}catch(e){k.critical("`loaded` function failed",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this._captureInitialPageview()},1),new Ga(this).load(),this.featureFlags.decide()}_start_queue_if_opted_in(){var e;this.has_opted_out_capturing()||this.config.request_batching&&((e=this._requestQueue)===null||e===void 0||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||qe(this.__request_queue,e=>this._send_retriable_request(e)),this.__request_queue=[],this._start_queue_if_opted_in()}_handle_unload(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture("$pageleave"),(e=this._requestQueue)===null||e===void 0||e.unload(),(t=this._retryQueue)===null||t===void 0||t.unload()):this._shouldCapturePageleave()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}_send_request(e){this.__loaded&&(Po?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=mi(e.url,{ip:this.config.ip?1:0}),e.headers=v({},this.config.request_headers),e.compression=e.compression==="best-available"?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(t=>{var n,r,s,o=v({},t);o.timeout=o.timeout||6e4,o.url=mi(o.url,{_:new Date().getTime().toString(),ver:Ce.LIB_VERSION,compression:o.compression});var a=(n=o.transport)!==null&&n!==void 0?n:"fetch",l=(r=(s=Es($t,u=>u.transport===a))===null||s===void 0?void 0:s.method)!==null&&r!==void 0?r:$t[0].method;if(!l)throw new Error("No available transport method");l(o)})(v(v({},e),{},{callback:t=>{var n,r,s;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&((r=(s=this.config).on_request_error)===null||r===void 0||r.call(s,t)),(n=e.callback)===null||n===void 0||n.call(e,t)}}))))}_send_retriable_request(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)}_execute_array(e){var t,n=[],r=[],s=[];qe(e,a=>{a&&(t=a[0],q(t)?s.push(a):le(a)?a.call(this):q(a)&&t==="alias"?n.push(a):q(a)&&t.indexOf("capture")!==-1&&le(this[t])?s.push(a):r.push(a))});var o=function(a,l){qe(a,function(u){if(q(u[0])){var d=l;A(u,function(c){d=d[c[0]].apply(d,c.slice(1))})}else this[u[0]].apply(this,u.slice(1))},l)};o(n,this),o(r,this),o(s,this)}_hasBootstrappedFeatureFlags(){var e,t;return((e=this.config.bootstrap)===null||e===void 0?void 0:e.featureFlags)&&Object.keys((t=this.config.bootstrap)===null||t===void 0?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,n){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut())if(!E(e)&&V(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var s=n!=null&&n.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(s==null||!s.isRateLimited){this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var o=new Date,a=(n==null?void 0:n.timestamp)||o,l=Le(),u={uuid:l,event:e,properties:this._calculate_event_properties(e,t||{},a,l)};s&&(u.properties.$lib_rate_limit_remaining_tokens=s.remainingTokens),n!=null&&n.$set&&(u.$set=n==null?void 0:n.$set);var d=this._calculate_set_once_properties(n==null?void 0:n.$set_once);d&&(u.$set_once=d),(u=zo(u,n!=null&&n._noTruncate?null:this.config.properties_string_max_length)).timestamp=a,E(n==null?void 0:n.timestamp)||(u.properties.$event_time_override_provided=!0,u.properties.$event_time_override_system_time=o);var c=v(v({},u.properties.$set),u.$set);if(ct(c)||this.setPersonPropertiesForFlags(c),!F(this.config.before_send)){var p=this._runBeforeSend(u);if(!p)return;u=p}this._internalEventEmitter.emit("eventCaptured",u);var h={method:"POST",url:(r=n==null?void 0:n._url)!==null&&r!==void 0?r:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:u,compression:"best-available",batchKey:n==null?void 0:n._batchKey};return!this.config.request_batching||n&&(n==null||!n._batchKey)||n!=null&&n.send_instantly?this._send_retriable_request(h):this._requestQueue.enqueue(h),u}k.critical("This capture call is ignored due to client rate limiting.")}}else k.error("No event name provided to posthog.capture")}else k.uninitializedWarning("posthog.capture")}_addCaptureHook(e){return this.on("eventCaptured",t=>e(t.event,t))}_calculate_event_properties(e,t,n,r){if(n=n||new Date,!this.persistence||!this.sessionPersistence)return t;var s=this.persistence.remove_event_timer(e),o=v({},t);if(o.token=this.config.token,this.config.__preview_experimental_cookieless_mode&&(o.$cookieless_mode=!0),e==="$snapshot"){var a=v(v({},this.persistence.properties()),this.sessionPersistence.properties());return o.distinct_id=a.distinct_id,(!V(o.distinct_id)&&!Z(o.distinct_id)||ar(o.distinct_id))&&k.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),o}var l,u=D.properties({maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});if(this.sessionManager){var{sessionId:d,windowId:c}=this.sessionManager.checkAndGetSessionAndWindowId();o.$session_id=d,o.$window_id=c}try{var p,h;this.sessionRecording&&(o.$recording_status=this.sessionRecording.status,o.$sdk_debug_replay_internal_buffer_length=this.sessionRecording.buffer.data.length,o.$sdk_debug_replay_internal_buffer_size=this.sessionRecording.buffer.size),o.$sdk_debug_retry_queue_size=(p=this._retryQueue)===null||p===void 0||(h=p.queue)===null||h===void 0?void 0:h.length}catch(m){o.$sdk_debug_error_capturing_properties=String(m)}if(this.requestRouter.region===tt.CUSTOM&&(o.$lib_custom_api_host=this.config.api_host),l=e==="$pageview"?this.pageViewManager.doPageView(n,r):e==="$pageleave"?this.pageViewManager.doPageLeave(n):this.pageViewManager.doEvent(),o=G(o,l),e==="$pageview"&&S&&(o.title=S.title),!E(s)){var _=n.getTime()-s;o.$duration=parseFloat((_/1e3).toFixed(3))}H&&this.config.opt_out_useragent_filter&&(o.$browser_type=this._is_bot()?"bot":"browser"),(o=G({},u,this.persistence.properties(),this.sessionPersistence.properties(),o)).$is_identified=this._isIdentified(),q(this.config.property_denylist)?A(this.config.property_denylist,function(m){delete o[m]}):k.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var f=this.config.sanitize_properties;f&&(k.error("sanitize_properties is deprecated. Use before_send instead"),o=f(o,e));var b=this._hasPersonProcessing();return o.$process_person_profile=b,b&&this._requirePersonProcessing("_calculate_event_properties"),o}_calculate_set_once_properties(e){var t;if(!this.persistence||!this._hasPersonProcessing()||this._personProcessingSetOncePropertiesSent)return e;var n=this.persistence.get_initial_props(),r=(t=this.sessionPropsManager)===null||t===void 0?void 0:t.getSetOnceInitialSessionPropsProps(),s=G({},n,r||{},e||{}),o=this.config.sanitize_properties;return o&&(k.error("sanitize_properties is deprecated. Use before_send instead"),s=o(s,"$set_once")),this._personProcessingSetOncePropertiesSent=!0,ct(s)?void 0:s}register(e,t){var n;(n=this.persistence)===null||n===void 0||n.register(e,t)}register_once(e,t,n){var r;(r=this.persistence)===null||r===void 0||r.register_once(e,t,n)}register_for_session(e){var t;(t=this.sessionPersistence)===null||t===void 0||t.register(e)}unregister(e){var t;(t=this.persistence)===null||t===void 0||t.unregister(e)}unregister_for_session(e){var t;(t=this.sessionPersistence)===null||t===void 0||t.unregister(e)}_register_single(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch{return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}getEarlyAccessFeatures(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return this.featureFlags.getEarlyAccessFeatures(e,t)}on(e,t){return this._internalEventEmitter.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSessionId(e){var t,n;return(t=(n=this.sessionManager)===null||n===void 0?void 0:n.onSessionId(e))!==null&&t!==void 0?t:()=>{}}getSurveys(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e){var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){this.surveys.canRenderSurvey(e)}getNextSurveyStep(e,t,n){return this.surveys.getNextSurveyStep(e,t,n)}identify(e,t,n){if(!this.__loaded||!this.persistence)return k.uninitializedWarning("posthog.identify");if(Z(e)&&(e=e.toString(),k.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e){if(["distinct_id","distinctid"].includes(e.toLowerCase()))k.critical('The string "'.concat(e,'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.'));else if(this._requirePersonProcessing("posthog.identify")){var r=this.get_distinct_id();if(this.register({$user_id:e}),!this.get_property("$device_id")){var s=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:s},"")}e!==r&&e!==this.get_property(Pt)&&(this.unregister(Pt),this.register({distinct_id:e}));var o=(this.persistence.get_property(ke)||"anonymous")==="anonymous";e!==r&&o?(this.persistence.set_property(ke,"identified"),this.setPersonPropertiesForFlags(v(v({},n||{}),t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:r},{$set:t||{},$set_once:n||{}}),this.featureFlags.setAnonymousDistinctId(r),this._cachedIdentify=Yi(e,t,n)):(t||n)&&(this._cachedIdentify!==Yi(e,t,n)?(this.setPersonProperties(t,n),this._cachedIdentify=Yi(e,t,n)):k.info("A duplicate posthog.identify call was made with the same properties. It has been ignored.")),e!==r&&(this.reloadFeatureFlags(),this.unregister(ci))}}else k.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){(e||t)&&this._requirePersonProcessing("posthog.setPersonProperties")&&(this.setPersonPropertiesForFlags(v(v({},t||{}),e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}))}group(e,t,n){if(e&&t){if(this._requirePersonProcessing("posthog.group")){var r=this.getGroups();r[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:v(v({},r),{},{[e]:t})}),n&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:n}),this.setGroupPropertiesForFlags({[e]:n})),r[e]===t||n||this.reloadFeatureFlags()}}else k.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];this._requirePersonProcessing("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){var t,n,r,s;if(k.info("reset"),!this.__loaded)return k.uninitializedWarning("posthog.reset");var o=this.get_property("$device_id");if(this.consent.reset(),(t=this.persistence)===null||t===void 0||t.clear(),(n=this.sessionPersistence)===null||n===void 0||n.clear(),this.surveys.reset(),(r=this.persistence)===null||r===void 0||r.set_property(ke,"anonymous"),(s=this.sessionManager)===null||s===void 0||s.resetSessionId(),this._cachedIdentify=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:br,$device_id:null},"");else{var a=this.config.get_device_id(Le());this.register_once({distinct_id:a,$device_id:e?a:o},"")}this.register({$last_posthog_reset:new Date().toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return(e=(t=this.sessionManager)===null||t===void 0?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)!==null&&e!==void 0?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:n}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor("ui","/project/".concat(this.config.token,"/replay/").concat(t));if(e!=null&&e.withTimestamp&&n){var s,o=(s=e.timestampLookBack)!==null&&s!==void 0?s:10;if(!n)return r;var a=Math.max(Math.floor((new Date().getTime()-n)/1e3)-o,0);r+="?t=".concat(a)}return r}alias(e,t){return e===this.get_property(Ts)?(k.critical("Attempting to create alias for existing People user - aborting."),-2):this._requirePersonProcessing("posthog.alias")?(E(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(Pt,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(k.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t,n,r,s,o=v({},this.config);B(e)&&(G(this.config,ss(e)),(t=this.persistence)===null||t===void 0||t.update_config(this.config,o),this.sessionPersistence=this.config.persistence==="sessionStorage"||this.config.persistence==="memory"?this.persistence:new ji(v(v({},this.config),{},{persistence:"sessionStorage"})),j.is_supported()&&j.get("ph_debug")==="true"&&(this.config.debug=!0),this.config.debug&&(Ce.DEBUG=!0,k.info("set_config",{config:e,oldConfig:o,newConfig:v({},this.config)})),(n=this.sessionRecording)===null||n===void 0||n.startIfEnabledOrStop(),(r=this.autocapture)===null||r===void 0||r.startIfEnabled(),(s=this.heatmaps)===null||s===void 0||s.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())}startSessionRecording(e){var t=e===!0,n={sampling:t||!(e==null||!e.sampling),linked_flag:t||!(e==null||!e.linked_flag),url_trigger:t||!(e==null||!e.url_trigger),event_trigger:t||!(e==null||!e.event_trigger)};if(Object.values(n).some(Boolean)){var r,s,o,a,l;(r=this.sessionManager)===null||r===void 0||r.checkAndGetSessionAndWindowId(),n.sampling&&((s=this.sessionRecording)===null||s===void 0||s.overrideSampling()),n.linked_flag&&((o=this.sessionRecording)===null||o===void 0||o.overrideLinkedFlag()),n.url_trigger&&((a=this.sessionRecording)===null||a===void 0||a.overrideTrigger("url")),n.event_trigger&&((l=this.sessionRecording)===null||l===void 0||l.overrideTrigger("event"))}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!((e=this.sessionRecording)===null||e===void 0||!e.started)}captureException(e,t){var n,r=new Error("PostHog syntheticException"),s=le((n=P.__PosthogExtensions__)===null||n===void 0?void 0:n.parseErrorAsProperties)?v(v({},P.__PosthogExtensions__.parseErrorAsProperties($i(e)?{error:e,event:e.message}:{event:e},{syntheticException:r})),t):v({$exception_level:"error",$exception_list:[{type:$i(e)?e.name:"Error",value:$i(e)?e.message:e,mechanism:{handled:!0,synthetic:!1}}]},t);this.exceptions.sendExceptionEvent(s)}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return(t=this.persistence)===null||t===void 0?void 0:t.props[e]}getSessionProperty(e){var t;return(t=this.sessionPersistence)===null||t===void 0?void 0:t.props[e]}toString(){var e,t=(e=this.config.name)!==null&&e!==void 0?e:lt;return t!==lt&&(t=lt+"."+t),t}_isIdentified(){var e,t;return((e=this.persistence)===null||e===void 0?void 0:e.get_property(ke))==="identified"||((t=this.sessionPersistence)===null||t===void 0?void 0:t.get_property(ke))==="identified"}_hasPersonProcessing(){var e,t,n,r;return!(this.config.person_profiles==="never"||this.config.person_profiles==="identified_only"&&!this._isIdentified()&&ct(this.getGroups())&&((e=this.persistence)===null||e===void 0||(t=e.props)===null||t===void 0||!t[Pt])&&((n=this.persistence)===null||n===void 0||(r=n.props)===null||r===void 0||!r[ui]))}_shouldCapturePageleave(){return this.config.capture_pageleave===!0||this.config.capture_pageleave==="if_capture_pageview"&&this.config.capture_pageview}createPersonProfile(){this._hasPersonProcessing()||this._requirePersonProcessing("posthog.createPersonProfile")&&this.setPersonProperties({},{})}_requirePersonProcessing(e){return this.config.person_profiles==="never"?(k.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this._register_single(ui,!0),!0)}_sync_opt_out_with_persistence(){var e,t,n,r,s=this.consent.isOptedOut(),o=this.config.opt_out_persistence_by_default,a=this.config.disable_persistence||s&&!!o;((e=this.persistence)===null||e===void 0?void 0:e.disabled)!==a&&((n=this.persistence)===null||n===void 0||n.set_disabled(a)),((t=this.sessionPersistence)===null||t===void 0?void 0:t.disabled)!==a&&((r=this.sessionPersistence)===null||r===void 0||r.set_disabled(a))}opt_in_capturing(e){var t;this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),(E(e==null?void 0:e.captureEventName)||e!=null&&e.captureEventName)&&this.capture((t=e==null?void 0:e.captureEventName)!==null&&t!==void 0?t:"$opt_in",e==null?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this._captureInitialPageview()}opt_out_capturing(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this._sync_opt_out_with_persistence()}_is_bot(){return ne?ko(ne,this.config.custom_blocked_useragents):void 0}_captureInitialPageview(){S&&!this._initialPageviewCaptured&&(this._initialPageviewCaptured=!0,this.capture("$pageview",{title:S.title},{send_instantly:!0}))}debug(e){e===!1?(g==null||g.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(g==null||g.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}_runBeforeSend(e){if(F(this.config.before_send))return e;var t=q(this.config.before_send)?this.config.before_send:[this.config.before_send],n=e;for(var r of t){if(n=r(n),F(n)){var s="Event '".concat(e.event,"' was rejected in beforeSend function");return Yo(e.event)?k.warn("".concat(s,". This can cause unexpected behavior.")):k.info(s),null}n.properties&&!ct(n.properties)||k.warn("Event '".concat(e.event,"' has no properties after beforeSend function, this is likely an error."))}return n}getPageViewId(){var e;return(e=this.pageViewManager._currentPageview)===null||e===void 0?void 0:e.pageViewId}}(function(i,e){for(var t=0;t<e.length;t++)i.prototype[e[t]]=jo(i.prototype[e[t]])})(Pi,["identify"]);var os,su=(os=Dt[lt]=new Pi,function(){function i(){i.done||(i.done=!0,Po=!1,A(Dt,function(e){e._dom_loaded()}))}S!=null&&S.addEventListener?S.readyState==="complete"?i():U(S,"DOMContentLoaded",i,{capture:!1}):g&&k.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),os);const Co=Object.prototype.toString;function $l(i){switch(Co.call(i)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ci(i,Error)}}function bt(i,e){return Co.call(i)===`[object ${e}]`}function ou(i){return bt(i,"ErrorEvent")}function au(i){return bt(i,"DOMError")}function lu(i){return bt(i,"DOMException")}function wi(i){return bt(i,"String")}function Dl(i){return typeof i=="object"&&i!==null&&"__sentry_template_string__"in i&&"__sentry_template_values__"in i}function cu(i){return i===null||Dl(i)||typeof i!="object"&&typeof i!="function"}function Un(i){return bt(i,"Object")}function Ll(i){return typeof Event<"u"&&Ci(i,Event)}function Nl(i){return typeof Element<"u"&&Ci(i,Element)}function ql(i){return bt(i,"RegExp")}function Hn(i){return!!(i&&i.then&&typeof i.then=="function")}function Bl(i){return Un(i)&&"nativeEvent"in i&&"preventDefault"in i&&"stopPropagation"in i}function Ci(i,e){try{return i instanceof e}catch{return!1}}function To(i){return!!(typeof i=="object"&&i!==null&&(i.__isVue||i._isVue))}function Lt(i,e=0){return typeof i!="string"||e===0||i.length<=e?i:`${i.slice(0,e)}...`}function uu(i,e){if(!Array.isArray(i))return"";const t=[];for(let n=0;n<i.length;n++){const r=i[n];try{To(r)?t.push("[VueViewModel]"):t.push(String(r))}catch{t.push("[value cannot be serialized]")}}return t.join(e)}function Ul(i,e,t=!1){return wi(i)?ql(e)?e.test(i):wi(e)?t?i===e:i.includes(e):!1:!1}function du(i,e=[],t=!1){return e.some(n=>Ul(i,n,t))}const Nt="8.27.0",he=globalThis;function jn(i,e,t){const n=t||he,r=n.__SENTRY__=n.__SENTRY__||{},s=r[Nt]=r[Nt]||{};return s[i]||(s[i]=e())}const zn=he,Hl=80;function jl(i,e={}){if(!i)return"<unknown>";try{let t=i;const n=5,r=[];let s=0,o=0;const a=" > ",l=a.length;let u;const d=Array.isArray(e)?e:e.keyAttrs,c=!Array.isArray(e)&&e.maxStringLength||Hl;for(;t&&s++<n&&(u=zl(t,d),!(u==="html"||s>1&&o+r.length*l+u.length>=c));)r.push(u),o+=u.length,t=t.parentNode;return r.reverse().join(a)}catch{return"<unknown>"}}function zl(i,e){const t=i,n=[];if(!t||!t.tagName)return"";if(zn.HTMLElement&&t instanceof HTMLElement&&t.dataset){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}n.push(t.tagName.toLowerCase());const r=e&&e.length?e.filter(o=>t.getAttribute(o)).map(o=>[o,t.getAttribute(o)]):null;if(r&&r.length)r.forEach(o=>{n.push(`[${o[0]}="${o[1]}"]`)});else{t.id&&n.push(`#${t.id}`);const o=t.className;if(o&&wi(o)){const a=o.split(/\s+/);for(const l of a)n.push(`.${l}`)}}const s=["aria-label","type","name","title","alt"];for(const o of s){const a=t.getAttribute(o);a&&n.push(`[${o}="${a}"]`)}return n.join("")}function hu(){try{return zn.document.location.href}catch{return""}}function pu(i){if(!zn.HTMLElement)return null;let e=i;const t=5;for(let n=0;n<t;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const Ro=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Gl="Sentry Logger ",as=["debug","info","warn","error","log","assert","trace"],ls={};function Wl(i){if(!("console"in he))return i();const e=he.console,t={},n=Object.keys(ls);n.forEach(r=>{const s=ls[r];t[r]=e[r],e[r]=s});try{return i()}finally{n.forEach(r=>{e[r]=t[r]})}}function Vl(){let i=!1;const e={enable:()=>{i=!0},disable:()=>{i=!1},isEnabled:()=>i};return Ro?as.forEach(t=>{e[t]=(...n)=>{i&&Wl(()=>{he.console[t](`${Gl}[${t}]:`,...n)})}}):as.forEach(t=>{e[t]=()=>{}}),e}const qt=Vl();function _u(i,e,t){if(!(e in i))return;const n=i[e],r=t(n);typeof r=="function"&&Jl(r,n),i[e]=r}function Gn(i,e,t){try{Object.defineProperty(i,e,{value:t,writable:!0,configurable:!0})}catch{Ro&&qt.log(`Failed to add non-enumerable property "${e}" to object`,i)}}function Jl(i,e){try{const t=e.prototype||{};i.prototype=e.prototype=t,Gn(i,"__sentry_original__",e)}catch{}}function gu(i){return i.__sentry_original__}function fu(i){return Object.keys(i).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(i[e])}`).join("&")}function Fo(i){if($l(i))return{message:i.message,name:i.name,stack:i.stack,...us(i)};if(Ll(i)){const e={type:i.type,target:cs(i.target),currentTarget:cs(i.currentTarget),...us(i)};return typeof CustomEvent<"u"&&Ci(i,CustomEvent)&&(e.detail=i.detail),e}else return i}function cs(i){try{return Nl(i)?jl(i):Object.prototype.toString.call(i)}catch{return"<unknown>"}}function us(i){if(typeof i=="object"&&i!==null){const e={};for(const t in i)Object.prototype.hasOwnProperty.call(i,t)&&(e[t]=i[t]);return e}else return{}}function vu(i,e=40){const t=Object.keys(Fo(i));t.sort();const n=t[0];if(!n)return"[object has no keys]";if(n.length>=e)return Lt(n,e);for(let r=t.length;r>0;r--){const s=t.slice(0,r).join(", ");if(!(s.length>e))return r===t.length?s:Lt(s,e)}return""}function Te(i){return Pn(i,new Map)}function Pn(i,e){if(Yl(i)){const t=e.get(i);if(t!==void 0)return t;const n={};e.set(i,n);for(const r of Object.keys(i))typeof i[r]<"u"&&(n[r]=Pn(i[r],e));return n}if(Array.isArray(i)){const t=e.get(i);if(t!==void 0)return t;const n=[];return e.set(i,n),i.forEach(r=>{n.push(Pn(r,e))}),n}return i}function Yl(i){if(!Un(i))return!1;try{const e=Object.getPrototypeOf(i).constructor.name;return!e||e==="Object"}catch{return!0}}const Oo=50,Kl="?",ds=/\(error: (.*)\)/,hs=/captureMessage|captureException/;function Xl(...i){const e=i.sort((t,n)=>t[0]-n[0]).map(t=>t[1]);return(t,n=0,r=0)=>{const s=[],o=t.split(`
`);for(let a=n;a<o.length;a++){const l=o[a];if(l.length>1024)continue;const u=ds.test(l)?l.replace(ds,"$1"):l;if(!u.match(/\S*Error: /)){for(const d of e){const c=d(u);if(c){s.push(c);break}}if(s.length>=Oo+r)break}}return Ql(s.slice(r))}}function mu(i){return Array.isArray(i)?Xl(...i):i}function Ql(i){if(!i.length)return[];const e=Array.from(i);return/sentryWrapped/.test(ei(e).function||"")&&e.pop(),e.reverse(),hs.test(ei(e).function||"")&&(e.pop(),hs.test(ei(e).function||"")&&e.pop()),e.slice(0,Oo).map(t=>({...t,filename:t.filename||ei(e).filename,function:t.function||Kl}))}function ei(i){return i[i.length-1]||{}}const Ki="<anonymous>";function Zl(i){try{return!i||typeof i!="function"?Ki:i.name||Ki}catch{return Ki}}function yu(i){const e=i.exception;if(e){const t=[];try{return e.values.forEach(n=>{n.stacktrace.frames&&t.push(...n.stacktrace.frames)}),t}catch{return}}}const Ao=1e3;function Wn(){return Date.now()/Ao}function ec(){const{performance:i}=he;if(!i||!i.now)return Wn;const e=Date.now()-i.now(),t=i.timeOrigin==null?e:i.timeOrigin;return()=>(t+i.now())/Ao}const Vn=ec();(()=>{const{performance:i}=he;if(!i||!i.now)return;const e=3600*1e3,t=i.now(),n=Date.now(),r=i.timeOrigin?Math.abs(i.timeOrigin+t-n):e,s=r<e,o=i.timing&&i.timing.navigationStart,l=typeof o=="number"?Math.abs(o+t-n):e,u=l<e;return s||u?r<=l?i.timeOrigin:o:n})();function tc(){const i=typeof WeakSet=="function",e=i?new WeakSet:[];function t(r){if(i)return e.has(r)?!0:(e.add(r),!1);for(let s=0;s<e.length;s++)if(e[s]===r)return!0;return e.push(r),!1}function n(r){if(i)e.delete(r);else for(let s=0;s<e.length;s++)if(e[s]===r){e.splice(s,1);break}}return[t,n]}function Ue(){const i=he,e=i.crypto||i.msCrypto;let t=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(t=()=>{const n=new Uint8Array(1);return e.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(t()&15)>>n/4).toString(16))}function Mo(i){return i.exception&&i.exception.values?i.exception.values[0]:void 0}function bu(i){const{message:e,event_id:t}=i;if(e)return e;const n=Mo(i);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||t||"<unknown>":t||"<unknown>"}function wu(i,e,t){const n=i.exception=i.exception||{},r=n.values=n.values||[],s=r[0]=r[0]||{};s.value||(s.value=e||""),s.type||(s.type=t||"Error")}function ic(i,e){const t=Mo(i);if(!t)return;const n={type:"generic",handled:!0},r=t.mechanism;if(t.mechanism={...n,...r,...e},e&&"data"in e){const s={...r&&r.data,...e.data};t.mechanism.data=s}}function Su(i){if(i&&i.__sentry_captured__)return!0;try{Gn(i,"__sentry_captured__",!0)}catch{}return!1}function nc(i){return Array.isArray(i)?i:[i]}function Qe(i,e=100,t=1/0){try{return Cn("",i,e,t)}catch(n){return{ERROR:`**non-serializable** (${n})`}}}function rc(i,e=3,t=100*1024){const n=Qe(i,e);return lc(n)>t?rc(i,e-1,t):n}function Cn(i,e,t=1/0,n=1/0,r=tc()){const[s,o]=r;if(e==null||["number","boolean","string"].includes(typeof e)&&!Number.isNaN(e))return e;const a=sc(i,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const l=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:t;if(l===0)return a.replace("object ","");if(s(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{const h=u.toJSON();return Cn("",h,l-1,n,r)}catch{}const d=Array.isArray(e)?[]:{};let c=0;const p=Fo(e);for(const h in p){if(!Object.prototype.hasOwnProperty.call(p,h))continue;if(c>=n){d[h]="[MaxProperties ~]";break}const _=p[h];d[h]=Cn(h,_,l-1,n,r),c++}return o(e),d}function sc(i,e){try{if(i==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(i==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(To(e))return"[VueViewModel]";if(Bl(e))return"[SyntheticEvent]";if(typeof e=="number"&&e!==e)return"[NaN]";if(typeof e=="function")return`[Function: ${Zl(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;const t=oc(e);return/^HTML(\w*)Element$/.test(t)?`[HTMLElement: ${t}]`:`[object ${t}]`}catch(t){return`**non-serializable** (${t})`}}function oc(i){const e=Object.getPrototypeOf(i);return e?e.constructor.name:"null prototype"}function ac(i){return~-encodeURI(i).split(/%..|./).length}function lc(i){return ac(JSON.stringify(i))}var Pe;(function(i){i[i.PENDING=0]="PENDING";const t=1;i[i.RESOLVED=t]="RESOLVED";const n=2;i[i.REJECTED=n]="REJECTED"})(Pe||(Pe={}));function Eu(i){return new be(e=>{e(i)})}function ku(i){return new be((e,t)=>{t(i)})}class be{constructor(e){be.prototype.__init.call(this),be.prototype.__init2.call(this),be.prototype.__init3.call(this),be.prototype.__init4.call(this),this._state=Pe.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(t){this._reject(t)}}then(e,t){return new be((n,r)=>{this._handlers.push([!1,s=>{if(!e)n(s);else try{n(e(s))}catch(o){r(o)}},s=>{if(!t)r(s);else try{n(t(s))}catch(o){r(o)}}]),this._executeHandlers()})}catch(e){return this.then(t=>t,e)}finally(e){return new be((t,n)=>{let r,s;return this.then(o=>{s=!1,r=o,e&&e()},o=>{s=!0,r=o,e&&e()}).then(()=>{if(s){n(r);return}t(r)})})}__init(){this._resolve=e=>{this._setResult(Pe.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(Pe.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{if(this._state===Pe.PENDING){if(Hn(t)){t.then(this._resolve,this._reject);return}this._state=e,this._value=t,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===Pe.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(t=>{t[0]||(this._state===Pe.RESOLVED&&t[1](this._value),this._state===Pe.REJECTED&&t[2](this._value),t[0]=!0)})}}}const cc="sentry-",uc=/^sentry-/;function dc(i){const e=hc(i);if(!e)return;const t=Object.entries(e).reduce((n,[r,s])=>{if(r.match(uc)){const o=r.slice(cc.length);n[o]=s}return n},{});if(Object.keys(t).length>0)return t}function hc(i){if(!(!i||!wi(i)&&!Array.isArray(i)))return Array.isArray(i)?i.reduce((e,t)=>{const n=ps(t);return Object.entries(n).forEach(([r,s])=>{e[r]=s}),e},{}):ps(i)}function ps(i){return i.split(",").map(e=>e.split("=").map(t=>decodeURIComponent(t.trim()))).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}function _s(){return{traceId:Ue(),spanId:Ue().substring(16)}}const pc=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Ti(){return Jn(he),he}function Jn(i){const e=i.__SENTRY__=i.__SENTRY__||{};return e.version=e.version||Nt,e[Nt]=e[Nt]||{}}function _c(i){const e=Vn(),t={sid:Ue(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>fc(t)};return i&&Ri(t,i),t}function Ri(i,e={}){if(e.user&&(!i.ipAddress&&e.user.ip_address&&(i.ipAddress=e.user.ip_address),!i.did&&!e.did&&(i.did=e.user.id||e.user.email||e.user.username)),i.timestamp=e.timestamp||Vn(),e.abnormal_mechanism&&(i.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(i.ignoreDuration=e.ignoreDuration),e.sid&&(i.sid=e.sid.length===32?e.sid:Ue()),e.init!==void 0&&(i.init=e.init),!i.did&&e.did&&(i.did=`${e.did}`),typeof e.started=="number"&&(i.started=e.started),i.ignoreDuration)i.duration=void 0;else if(typeof e.duration=="number")i.duration=e.duration;else{const t=i.timestamp-i.started;i.duration=t>=0?t:0}e.release&&(i.release=e.release),e.environment&&(i.environment=e.environment),!i.ipAddress&&e.ipAddress&&(i.ipAddress=e.ipAddress),!i.userAgent&&e.userAgent&&(i.userAgent=e.userAgent),typeof e.errors=="number"&&(i.errors=e.errors),e.status&&(i.status=e.status)}function gc(i,e){let t={};e?t={status:e}:i.status==="ok"&&(t={status:"exited"}),Ri(i,t)}function fc(i){return Te({sid:`${i.sid}`,init:i.init,started:new Date(i.started*1e3).toISOString(),timestamp:new Date(i.timestamp*1e3).toISOString(),status:i.status,errors:i.errors,did:typeof i.did=="number"||typeof i.did=="string"?`${i.did}`:void 0,duration:i.duration,abnormal_mechanism:i.abnormal_mechanism,attrs:{release:i.release,environment:i.environment,ip_address:i.ipAddress,user_agent:i.userAgent}})}const Tn="_sentrySpan";function gs(i,e){e?Gn(i,Tn,e):delete i[Tn]}function fs(i){return i[Tn]}const vc=100;class Yn{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=_s()}clone(){const e=new Yn;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,gs(e,fs(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Ri(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return t===null?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const t=typeof e=="function"?e(this):e,[n,r]=t instanceof He?[t.getScopeData(),t.getRequestSession()]:Un(t)?[e,e.requestSession]:[],{tags:s,extra:o,user:a,contexts:l,level:u,fingerprint:d=[],propagationContext:c}=n||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...l},a&&Object.keys(a).length&&(this._user=a),u&&(this._level=u),d.length&&(this._fingerprint=d),c&&(this._propagationContext=c),r&&(this._requestSession=r),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,gs(this,void 0),this._attachments=[],this._propagationContext=_s(),this._notifyScopeListeners(),this}addBreadcrumb(e,t){const n=typeof t=="number"?t:vc;if(n<=0)return this;const r={timestamp:Wn(),...e},s=this._breadcrumbs;return s.push(r),this._breadcrumbs=s.length>n?s.slice(-n):s,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:fs(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){const n=t&&t.event_id?t.event_id:Ue();if(!this._client)return qt.warn("No client configured on scope - will not capture exception!"),n;const r=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},this),n}captureMessage(e,t,n){const r=n&&n.event_id?n.event_id:Ue();if(!this._client)return qt.warn("No client configured on scope - will not capture message!"),r;const s=new Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureEvent(e,t){const n=t&&t.event_id?t.event_id:Ue();return this._client?(this._client.captureEvent(e,{...t,event_id:n},this),n):(qt.warn("No client configured on scope - will not capture event!"),n)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}const He=Yn;function mc(){return jn("defaultCurrentScope",()=>new He)}function yc(){return jn("defaultIsolationScope",()=>new He)}class bc{constructor(e,t){let n;e?n=e:n=new He;let r;t?r=t:r=new He,this._stack=[{scope:n}],this._isolationScope=r}withScope(e){const t=this._pushScope();let n;try{n=e(t)}catch(r){throw this._popScope(),r}return Hn(n)?n.then(r=>(this._popScope(),r),r=>{throw this._popScope(),r}):(this._popScope(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function yt(){const i=Ti(),e=Jn(i);return e.stack=e.stack||new bc(mc(),yc())}function wc(i){return yt().withScope(i)}function Sc(i,e){const t=yt();return t.withScope(()=>(t.getStackTop().scope=i,e(i)))}function vs(i){return yt().withScope(()=>i(yt().getIsolationScope()))}function Ec(){return{withIsolationScope:vs,withScope:wc,withSetScope:Sc,withSetIsolationScope:(i,e)=>vs(e),getCurrentScope:()=>yt().getScope(),getIsolationScope:()=>yt().getIsolationScope()}}function Kn(i){const e=Jn(i);return e.acs?e.acs:Ec()}function wt(){const i=Ti();return Kn(i).getCurrentScope()}function Fi(){const i=Ti();return Kn(i).getIsolationScope()}function kc(){return jn("globalScope",()=>new He)}function xu(...i){const e=Ti(),t=Kn(e);if(i.length===2){const[n,r]=i;return n?t.withSetScope(n,r):t.withScope(r)}return t.withScope(i[0])}function Xn(){return wt().getClient()}const xc="_sentryMetrics";function Ic(i){const e=i[xc];if(!e)return;const t={};for(const[,[n,r]]of e)(t[n]||(t[n]=[])).push(Te(r));return t}const Pc="sentry.source",Cc="sentry.sample_rate",Tc="sentry.op",Rc="sentry.origin",Fc=0,Oc=1,Ac=1;function Mc(i){const{spanId:e,traceId:t}=i.spanContext(),{parent_span_id:n}=Si(i);return Te({parent_span_id:n,span_id:e,trace_id:t})}function ms(i){return typeof i=="number"?ys(i):Array.isArray(i)?i[0]+i[1]/1e9:i instanceof Date?ys(i.getTime()):Vn()}function ys(i){return i>9999999999?i/1e3:i}function Si(i){if(Dc(i))return i.getSpanJSON();try{const{spanId:e,traceId:t}=i.spanContext();if($c(i)){const{attributes:n,startTime:r,name:s,endTime:o,parentSpanId:a,status:l}=i;return Te({span_id:e,trace_id:t,data:n,description:s,parent_span_id:a,start_timestamp:ms(r),timestamp:ms(o)||void 0,status:Nc(l),op:n[Tc],origin:n[Rc],_metrics_summary:Ic(i)})}return{span_id:e,trace_id:t}}catch{return{}}}function $c(i){const e=i;return!!e.attributes&&!!e.startTime&&!!e.name&&!!e.endTime&&!!e.status}function Dc(i){return typeof i.getSpanJSON=="function"}function Lc(i){const{traceFlags:e}=i.spanContext();return e===Ac}function Nc(i){if(!(!i||i.code===Fc))return i.code===Oc?"ok":i.message||"unknown_error"}const qc="_sentryRootSpan";function $o(i){return i[qc]||i}const Qn="production",Bc="_frozenDsc";function Uc(i,e){const t=e.getOptions(),{publicKey:n}=e.getDsn()||{},r=Te({environment:t.environment||Qn,release:t.release,public_key:n,trace_id:i});return e.emit("createDsc",r),r}function Hc(i){const e=Xn();if(!e)return{};const t=Uc(Si(i).trace_id||"",e),n=$o(i),r=n[Bc];if(r)return r;const s=n.spanContext().traceState,o=s&&s.get("sentry.dsc"),a=o&&dc(o);if(a)return a;const l=Si(n),u=l.data||{},d=u[Cc];d!=null&&(t.sample_rate=`${d}`);const c=u[Pc],p=l.description;return c!=="url"&&p&&(t.transaction=p),t.sampled=String(Lc(n)),e.emit("createDsc",t,n),t}function Rn(i,e,t,n=0){return new be((r,s)=>{const o=i[n];if(e===null||typeof o!="function")r(e);else{const a=o({...e},t);pc&&o.id&&a===null&&qt.log(`Event processor "${o.id}" dropped event`),Hn(a)?a.then(l=>Rn(i,l,t,n+1).then(r)).then(null,s):Rn(i,a,t,n+1).then(r).then(null,s)}})}function jc(i,e){const{fingerprint:t,span:n,breadcrumbs:r,sdkProcessingMetadata:s}=e;zc(i,e),n&&Vc(i,n),Jc(i,t),Gc(i,r),Wc(i,s)}function bs(i,e){const{extra:t,tags:n,user:r,contexts:s,level:o,sdkProcessingMetadata:a,breadcrumbs:l,fingerprint:u,eventProcessors:d,attachments:c,propagationContext:p,transactionName:h,span:_}=e;It(i,"extra",t),It(i,"tags",n),It(i,"user",r),It(i,"contexts",s),It(i,"sdkProcessingMetadata",a),o&&(i.level=o),h&&(i.transactionName=h),_&&(i.span=_),l.length&&(i.breadcrumbs=[...i.breadcrumbs,...l]),u.length&&(i.fingerprint=[...i.fingerprint,...u]),d.length&&(i.eventProcessors=[...i.eventProcessors,...d]),c.length&&(i.attachments=[...i.attachments,...c]),i.propagationContext={...i.propagationContext,...p}}function It(i,e,t){if(t&&Object.keys(t).length){i[e]={...i[e]};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(i[e][n]=t[n])}}function zc(i,e){const{extra:t,tags:n,user:r,contexts:s,level:o,transactionName:a}=e,l=Te(t);l&&Object.keys(l).length&&(i.extra={...l,...i.extra});const u=Te(n);u&&Object.keys(u).length&&(i.tags={...u,...i.tags});const d=Te(r);d&&Object.keys(d).length&&(i.user={...d,...i.user});const c=Te(s);c&&Object.keys(c).length&&(i.contexts={...c,...i.contexts}),o&&(i.level=o),a&&i.type!=="transaction"&&(i.transaction=a)}function Gc(i,e){const t=[...i.breadcrumbs||[],...e];i.breadcrumbs=t.length?t:void 0}function Wc(i,e){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...e}}function Vc(i,e){i.contexts={trace:Mc(e),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:Hc(e),...i.sdkProcessingMetadata};const t=$o(e),n=Si(t).description;n&&!i.transaction&&i.type==="transaction"&&(i.transaction=n)}function Jc(i,e){i.fingerprint=i.fingerprint?nc(i.fingerprint):[],e&&(i.fingerprint=i.fingerprint.concat(e)),i.fingerprint&&!i.fingerprint.length&&delete i.fingerprint}function Iu(i,e,t,n,r,s){const{normalizeDepth:o=3,normalizeMaxBreadth:a=1e3}=i,l={...e,event_id:e.event_id||t.event_id||Ue(),timestamp:e.timestamp||Wn()},u=t.integrations||i.integrations.map(b=>b.name);Yc(l,i),Qc(l,u),r&&r.emit("applyFrameMetadata",e),e.type===void 0&&Kc(l,i.stackParser);const d=eu(n,t.captureContext);t.mechanism&&ic(l,t.mechanism);const c=r?r.getEventProcessors():[],p=kc().getScopeData();if(s){const b=s.getScopeData();bs(p,b)}if(d){const b=d.getScopeData();bs(p,b)}const h=[...t.attachments||[],...p.attachments];h.length&&(t.attachments=h),jc(l,p);const _=[...c,...p.eventProcessors];return Rn(_,l,t).then(b=>(b&&Xc(b),typeof o=="number"&&o>0?Zc(b,o,a):b))}function Yc(i,e){const{environment:t,release:n,dist:r,maxValueLength:s=250}=e;"environment"in i||(i.environment="environment"in e?t:Qn),i.release===void 0&&n!==void 0&&(i.release=n),i.dist===void 0&&r!==void 0&&(i.dist=r),i.message&&(i.message=Lt(i.message,s));const o=i.exception&&i.exception.values&&i.exception.values[0];o&&o.value&&(o.value=Lt(o.value,s));const a=i.request;a&&a.url&&(a.url=Lt(a.url,s))}const ws=new WeakMap;function Kc(i,e){const t=he._sentryDebugIds;if(!t)return;let n;const r=ws.get(e);r?n=r:(n=new Map,ws.set(e,n));const s=Object.entries(t).reduce((o,[a,l])=>{let u;const d=n.get(a);d?u=d:(u=e(a),n.set(a,u));for(let c=u.length-1;c>=0;c--){const p=u[c];if(p.filename){o[p.filename]=l;break}}return o},{});try{i.exception.values.forEach(o=>{o.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=s[a.filename])})})}catch{}}function Xc(i){const e={};try{i.exception.values.forEach(n=>{n.stacktrace.frames.forEach(r=>{r.debug_id&&(r.abs_path?e[r.abs_path]=r.debug_id:r.filename&&(e[r.filename]=r.debug_id),delete r.debug_id)})})}catch{}if(Object.keys(e).length===0)return;i.debug_meta=i.debug_meta||{},i.debug_meta.images=i.debug_meta.images||[];const t=i.debug_meta.images;Object.entries(e).forEach(([n,r])=>{t.push({type:"sourcemap",code_file:n,debug_id:r})})}function Qc(i,e){e.length>0&&(i.sdk=i.sdk||{},i.sdk.integrations=[...i.sdk.integrations||[],...e])}function Zc(i,e,t){if(!i)return null;const n={...i,...i.breadcrumbs&&{breadcrumbs:i.breadcrumbs.map(r=>({...r,...r.data&&{data:Qe(r.data,e,t)}}))},...i.user&&{user:Qe(i.user,e,t)},...i.contexts&&{contexts:Qe(i.contexts,e,t)},...i.extra&&{extra:Qe(i.extra,e,t)}};return i.contexts&&i.contexts.trace&&n.contexts&&(n.contexts.trace=i.contexts.trace,i.contexts.trace.data&&(n.contexts.trace.data=Qe(i.contexts.trace.data,e,t))),i.spans&&(n.spans=i.spans.map(r=>({...r,...r.data&&{data:Qe(r.data,e,t)}}))),n}function eu(i,e){if(!e)return i;const t=i?i.clone():new He;return t.update(e),t}function tu(i){if(i)return iu(i)?{captureContext:i}:ru(i)?{captureContext:i}:i}function iu(i){return i instanceof He||typeof i=="function"}const nu=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function ru(i){return Object.keys(i).some(e=>nu.includes(e))}function Pu(i,e){return wt().captureException(i,tu(e))}function Cu(i,e){return wt().captureEvent(i,e)}function Tu(i){Fi().setUser(i)}function Ru(i){const e=Xn(),t=Fi(),n=wt(),{release:r,environment:s=Qn}=e&&e.getOptions()||{},{userAgent:o}=he.navigator||{},a=_c({release:r,environment:s,user:n.getUser()||t.getUser(),...o&&{userAgent:o},...i}),l=t.getSession();return l&&l.status==="ok"&&Ri(l,{status:"exited"}),Do(),t.setSession(a),n.setSession(a),a}function Do(){const i=Fi(),e=wt(),t=e.getSession()||i.getSession();t&&gc(t),Lo(),i.setSession(),e.setSession()}function Lo(){const i=Fi(),e=wt(),t=Xn(),n=e.getSession()||i.getSession();n&&t&&t.captureSession(n)}function Fu(i=!1){if(i){Do();return}Lo()}export{Kl as $,Hn as A,Un as B,as as C,Ro as D,Dl as E,wt as F,he as G,Nt as H,Xn as I,gu as J,bu as K,du as L,yu as M,Jl as N,xu as O,su as P,wu as Q,ic as R,be as S,Pu as T,ou as U,au as V,lu as W,Ll as X,rc as Y,vu as Z,Xl as _,Vn as a,jl as a0,pu as a1,uu as a2,Cu as a3,wi as a4,hu as a5,mu as a6,Ru as a7,Fu as a8,$l as b,Wl as c,Gn as d,Eu as e,_u as f,Zl as g,Te as h,Ci as i,Wn as j,pc as k,qt as l,nc as m,Qe as n,ls as o,Ue as p,Su as q,ku as r,Tu as s,Lt as t,fu as u,cu as v,Ri as w,Iu as x,Uc as y,Fi as z};
