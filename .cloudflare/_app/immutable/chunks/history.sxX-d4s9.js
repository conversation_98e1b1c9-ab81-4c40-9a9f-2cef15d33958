(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="13f9d2e1-cb81-462c-ad10-c53996f51a7b",e._sentryDebugIdIdentifier="sentry-dbid-13f9d2e1-cb81-462c-ad10-c53996f51a7b")}catch{}})();const r="Order history",a="Active Order",n="Completed orders",o="View All",c="Rental",s="Purchase",l="No activity to show.",i="Rental Time",d="Purchase the charger",_="Report an issue",u="Would you like to cancel your rental and buy the charger instead?",h="Once the purchase is complete, the rental will be cancelled and the charger will be yours.",y="You will be charged",b={history:r,active_order:a,completed_orders:n,view_all:o,rental:c,purchase:s,no_data:l,rental_time:i,purchase_battery:d,report_issue:_,cancel_rental_confirmation:u,purchase_explanation:h,you_will_be_charged:y};export{a as active_order,u as cancel_rental_confirmation,n as completed_orders,b as default,r as history,l as no_data,s as purchase,d as purchase_battery,h as purchase_explanation,c as rental,i as rental_time,_ as report_issue,o as view_all,y as you_will_be_charged};
