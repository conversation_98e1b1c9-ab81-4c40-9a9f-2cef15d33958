(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},o=new Error().stack;o&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[o]="116b16bc-f169-4864-9be6-58357ed14fd3",e._sentryDebugIdIdentifier="sentry-dbid-116b16bc-f169-4864-9be6-58357ed14fd3")}catch{}})();const s="Confirmer",n="Annuler",r="Êtes-vous sûr?",t="Plus d'options",i="Il n'y a rien ici",a="À venir",u="Oops!",c="<PERSON><PERSON><PERSON> chose s'est mal passé, veuillez réessayer plus tard.",l="Fermer",f="fichiers maximum",_={message:"Installez l'application pour une meilleure expérience !",install:"Installer",skip:"Me le rappeler plus tard"},d="Première heure gratuite!",m="Gratuit pour",b="de rabais",p="Gratuit",h="h",y="min",g="s",I="$/heure",x={confirm:s,cancel:n,are_you_sure:r,more_options:t,not_found:i,coming_soon:a,oops:u,something_wrong_happened:c,dismiss:l,files_maximum:f,installation:_,first_hour_free:d,free_for:m,off:b,free:p,hour_abbr:h,minute_abbr:y,second_abbr:g,hourly_rate:I};export{r as are_you_sure,n as cancel,a as coming_soon,s as confirm,x as default,l as dismiss,f as files_maximum,d as first_hour_free,p as free,m as free_for,h as hour_abbr,I as hourly_rate,_ as installation,y as minute_abbr,t as more_options,i as not_found,b as off,u as oops,g as second_abbr,c as something_wrong_happened};
