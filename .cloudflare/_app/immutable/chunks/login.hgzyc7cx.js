(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},n=new Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="23176cc5-ebec-4ce1-b1fc-84496eade0fc",e._sentryDebugIdIdentifier="sentry-dbid-23176cc5-ebec-4ce1-b1fc-84496eade0fc")}catch{}})();const o="Entrez votre numéro de téléphone pour recevoir un code de vérification",t="Numéro de téléphone",d="Suivant",c="Valider",r="Entrez le code de vérification reçu par texto",i="Ce numéro de téléphone est invalide",s="Ce code est invalide",a="Vous n'avez pas reçu de code ?",u="Réessayer",_="Restez branché",b="sans vous arrêter!",l={enter_phone_number:o,phone_number:t,send_code:d,submit:c,texted_code:r,invalid_phone_number:i,invalid_code:s,code_not_received:a,try_again:u,keep_charged:_,without_pausing:b};export{a as code_not_received,l as default,o as enter_phone_number,s as invalid_code,i as invalid_phone_number,_ as keep_charged,t as phone_number,d as send_code,c as submit,r as texted_code,u as try_again,b as without_pausing};
