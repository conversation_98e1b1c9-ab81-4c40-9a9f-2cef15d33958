import{H as l,w as a,o as y}from"./index.UaHqEmIZ.js";import"./index.RK-K-o1D.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="13e158b7-79ec-4156-90b9-4cceaf617749",n._sentryDebugIdIdentifier="sentry-dbid-13e158b7-79ec-4156-90b9-4cceaf617749")}catch{}})();let c;const p=new Uint8Array(16);function b(){if(!c&&(c=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!c))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return c(p)}const t=[];for(let n=0;n<256;++n)t.push((n+256).toString(16).slice(1));function g(n,e=0){return t[n[e+0]]+t[n[e+1]]+t[n[e+2]]+t[n[e+3]]+"-"+t[n[e+4]]+t[n[e+5]]+"-"+t[n[e+6]]+t[n[e+7]]+"-"+t[n[e+8]]+t[n[e+9]]+"-"+t[n[e+10]]+t[n[e+11]]+t[n[e+12]]+t[n[e+13]]+t[n[e+14]]+t[n[e+15]]}const m=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),s={randomUUID:m};function w(n,e,u){if(s.randomUUID&&!e&&!n)return s.randomUUID();n=n||{};const o=n.random||(n.rng||b)();if(o[6]=o[6]&15|64,o[8]=o[8]&63|128,e){u=u||0;for(let d=0;d<16;++d)e[u+d]=o[d];return e}return g(o)}const r=a([]),D=l(r,n=>n.length),h=()=>{const n=w(),e=a(!1),u=()=>{r.update(d=>[...d,n]),e.set(!0)},o=()=>{r.update(d=>d.filter(i=>i!==n)),e.set(!1)};return y(()=>{const d=e.subscribe(i=>{i?u():o()});return()=>{d(),o()}}),{isVisible:e}};export{D as o,h as u,w as v};
