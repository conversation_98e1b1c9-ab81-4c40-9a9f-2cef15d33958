(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},n=new Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="54797386-6a61-4721-9109-598e3cb600d1",e._sentryDebugIdIdentifier="sentry-dbid-54797386-6a61-4721-9109-598e3cb600d1")}catch{}})();const t="Enter your phone number to receive a verification code",o="Phone number",i="Next",d="Submit",c="Enter the verification code sent by text message",r="This is not a valid phone number",s="This is not a valid code",a="Didn't receive the code?",_="Try again",u="Keep charged",b="without pausing!",p={enter_phone_number:t,phone_number:o,send_code:i,submit:d,texted_code:c,invalid_phone_number:r,invalid_code:s,code_not_received:a,try_again:_,keep_charged:u,without_pausing:b};export{a as code_not_received,p as default,t as enter_phone_number,s as invalid_code,r as invalid_phone_number,u as keep_charged,o as phone_number,i as send_code,d as submit,c as texted_code,_ as try_again,b as without_pausing};
