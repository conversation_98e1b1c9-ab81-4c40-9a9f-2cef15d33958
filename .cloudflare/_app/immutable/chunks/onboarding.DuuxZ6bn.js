(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="2594260c-7801-4e3f-9b96-a4e663502a9b",e._sentryDebugIdIdentifier="sentry-dbid-2594260c-7801-4e3f-9b96-a4e663502a9b")}catch{}})();const r="Suivant",n="Découvrir",o="Localiser",s="À l'aide de l'application HIKO, localiser la station la plus proche de vous",c="Scanner",a="Scannez le code QR de la station et sélectionnez un type de chargeur",i="Retourner",d="Retournez le chargeur dans la station HIKO de votre choix",u="Numériser",p="À l’aide de votre appareil, numérisez le code QR affiché sur la station de recharge HIKO pour démarrer une location.",l="Louer",_="Appuyez sur « Louer un chargeur » et ajoutez votre carte de crédit pour confirmer l’achat. Prenez ensuite le chargeur qui s’éjecte de la station de recharge.",f="Retourner",h="Retournez le chargeur dans une fente libre de la station de recharge une fois terminé. Le dépôt vous sera automatiquement remboursé.",y={next:r,discover:n,locate:o,locate_description:s,scan:c,scan_description:a,return_anywhere:i,return_anywhere_description:d,step1:u,step1_description:p,step2:l,step2_description:_,step3:f,step3_description:h};export{y as default,n as discover,o as locate,s as locate_description,r as next,i as return_anywhere,d as return_anywhere_description,c as scan,a as scan_description,u as step1,p as step1_description,l as step2,_ as step2_description,f as step3,h as step3_description};
