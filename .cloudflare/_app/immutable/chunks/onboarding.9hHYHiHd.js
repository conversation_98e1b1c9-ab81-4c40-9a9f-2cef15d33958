(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8f27ccdc-a178-4c52-9283-5e6ae0b84afa",e._sentryDebugIdIdentifier="sentry-dbid-8f27ccdc-a178-4c52-9283-5e6ae0b84afa")}catch{}})();const n="Next",c="Discover",o="Locate",s="Using the HIKO app, locate the station closest to you",a="Scan",r="Scan the station's QR code and select a charger type",i="Return",d="Return the charger to the HIKO station of your choice",p="Scan",h="Using your device, scan the QR code displayed on the HIKO charging station to start a rental.",u="Rent",l='Tap "Rent a charger" and add your credit card to confirm the purchase. Then, take the charger that ejects from the charging station.',y="Return",g="Return the charger to an available slot in the charging station once you’re done. Your deposit will be automatically refunded.",f={next:n,discover:c,locate:o,locate_description:s,scan:a,scan_description:r,return_anywhere:i,return_anywhere_description:d,step1:p,step1_description:h,step2:u,step2_description:l,step3:y,step3_description:g};export{f as default,c as discover,o as locate,s as locate_description,n as next,i as return_anywhere,d as return_anywhere_description,a as scan,r as scan_description,p as step1,h as step1_description,u as step2,l as step2_description,y as step3,g as step3_description};
