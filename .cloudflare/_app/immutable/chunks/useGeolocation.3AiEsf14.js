import{s as M,B as u,J as U,K as T,L as N,a as k,M as P,d as g,N as w,i as S,r as b,C as m,e as J,c as K,V as A,E as O,W as V,X,A as H,D as j,u as D,o as z}from"./index.UaHqEmIZ.js";import{S as F,i as E,g as Q,b as y,f as Y,t as p,c as G,a as Z,m as R,d as W}from"./index.RK-K-o1D.js";import{g as I,D as $}from"./style.HZSn-yMG.js";import{s as L}from"./supabase.xRgAeO37.js";import{u as B,l as h,a as _}from"./user.oAuVK7RJ.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[t]="9a740a88-670a-4b17-8578-ce6e563a6cbe",n._sentryDebugIdIdentifier="sentry-dbid-9a740a88-670a-4b17-8578-ce6e563a6cbe")}catch{}})();function tt(n){let t,a,e='<path fill="currentColor" d="M178 34c-21 0-39.26 9.47-50 25.34C117.26 43.47 99 34 78 34a60.07 60.07 0 0 0-60 60c0 29.2 18.2 59.59 54.1 90.31a334.68 334.68 0 0 0 53.06 37a6 6 0 0 0 5.68 0a334.68 334.68 0 0 0 53.06-37C219.8 153.59 238 123.2 238 94a60.07 60.07 0 0 0-60-60m-50 175.11C111.59 199.64 30 149.72 30 94a48.05 48.05 0 0 1 48-48c20.28 0 37.31 10.83 44.45 28.27a6 6 0 0 0 11.1 0C140.69 56.83 157.72 46 178 46a48.05 48.05 0 0 1 48 48c0 55.72-81.59 105.64-98 115.11"/>',i=[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},n[0]],o={};for(let s=0;s<i.length;s+=1)o=u(o,i[s]);return{c(){t=U("svg"),a=new T(!0),this.h()},l(s){t=N(s,"svg",{viewBox:!0,width:!0,height:!0});var r=k(t);a=P(r,!0),r.forEach(g),this.h()},h(){a.a=null,w(t,o)},m(s,r){S(s,t,r),a.m(e,t)},p(s,[r]){w(t,o=I(i,[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},r&1&&s[0]]))},i:b,o:b,d(s){s&&g(t)}}}function et(n,t,a){return n.$$set=e=>{a(0,t=u(u({},t),m(e)))},t=m(t),[t]}class at extends F{constructor(t){super(),E(this,t,et,tt,M,{})}}function st(n){let t,a,e='<path fill="currentColor" d="M240 94c0 70-103.79 126.66-108.21 129a8 8 0 0 1-7.58 0C119.79 220.66 16 164 16 94a62.07 62.07 0 0 1 62-62c20.65 0 38.73 8.88 50 23.89C139.27 40.88 157.35 32 178 32a62.07 62.07 0 0 1 62 62"/>',i=[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},n[0]],o={};for(let s=0;s<i.length;s+=1)o=u(o,i[s]);return{c(){t=U("svg"),a=new T(!0),this.h()},l(s){t=N(s,"svg",{viewBox:!0,width:!0,height:!0});var r=k(t);a=P(r,!0),r.forEach(g),this.h()},h(){a.a=null,w(t,o)},m(s,r){S(s,t,r),a.m(e,t)},p(s,[r]){w(t,o=I(i,[{viewBox:"0 0 256 256"},{width:"1.2em"},{height:"1.2em"},r&1&&s[0]]))},i:b,o:b,d(s){s&&g(t)}}}function nt(n,t,a){return n.$$set=e=>{a(0,t=u(u({},t),m(e)))},t=m(t),[t]}class ot extends F{constructor(t){super(),E(this,t,nt,st,M,{})}}function rt(n){let t,a;return t=new at({props:{class:"opacity-30"}}),{c(){G(t.$$.fragment)},l(e){Z(t.$$.fragment,e)},m(e,i){R(t,e,i),a=!0},i(e){a||(p(t.$$.fragment,e),a=!0)},o(e){y(t.$$.fragment,e),a=!1},d(e){W(t,e)}}}function it(n){let t,a;return t=new ot({props:{class:"text-primary-500"}}),{c(){G(t.$$.fragment)},l(e){Z(t.$$.fragment,e)},m(e,i){R(t,e,i),a=!0},i(e){a||(p(t.$$.fragment,e),a=!0)},o(e){y(t.$$.fragment,e),a=!1},d(e){W(t,e)}}}function ct(n){let t,a,e,i,o,s;const r=[it,rt],l=[];function q(c,d){return c[0]?0:1}a=q(n),e=l[a]=r[a](n);let x=[n[2]],f={};for(let c=0;c<x.length;c+=1)f=u(f,x[c]);return{c(){t=J("button"),e.c(),this.h()},l(c){t=K(c,"BUTTON",{});var d=k(t);e.l(d),d.forEach(g),this.h()},h(){A(t,f)},m(c,d){S(c,t,d),l[a].m(t,null),t.autofocus&&t.focus(),i=!0,o||(s=O(t,"click",V(X(n[1]))),o=!0)},p(c,[d]){let C=a;a=q(c),a!==C&&(Q(),y(l[C],1,1,()=>{l[C]=null}),Y(),e=l[a],e||(e=l[a]=r[a](c),e.c()),p(e,1),e.m(t,null)),A(t,f=I(x,[d&4&&c[2]]))},i(c){i||(p(e),i=!0)},o(c){y(e),i=!1},d(c){c&&g(t),l[a].d(),o=!1,s()}}}function lt(n,t,a){const e=["value"];let i=H(t,e),{value:o}=t;const s=j(),r=()=>{s(o?"unlike":"like")};return n.$$set=l=>{t=u(u({},t),m(l)),a(2,i=H(t,e)),"value"in l&&a(0,o=l.value)},[o,r,i]}class ht extends F{constructor(t){super(),E(this,t,lt,ct,M,{value:0})}}const _t=(n,t,a,e)=>{const o=v(a-n),s=v(e-t),r=Math.sin(o/2)*Math.sin(o/2)+Math.cos(v(n))*Math.cos(v(a))*Math.sin(s/2)*Math.sin(s/2);return 6371*(2*Math.atan2(Math.sqrt(r),Math.sqrt(1-r)))*1e3},v=n=>n*(Math.PI/180),vt=(n,t={})=>{const a=t.language??$,e=t.unitDisplay??"short";return n>=1e3?new Intl.NumberFormat(a,{style:"unit",unit:"kilometer",unitDisplay:e,maximumFractionDigits:1}).format(n/1e3):new Intl.NumberFormat(a,{style:"unit",unit:"meter",unitDisplay:e,maximumFractionDigits:0}).format(n)},wt=async n=>{const t=D(B);if(!t)throw new Error("User not found");const{data:a,error:e}=await L.from("favorite_stations").select("station_id").in("station_id",n).eq("user_id",t.id);if(e)throw new Error(e.message);return n.reduce((i,o)=>(i[o]=!!a.find(s=>s.station_id===o),i),{})},bt=async n=>{const t=D(B);if(!t)throw new Error("User not found");await L.from("favorite_stations").insert({user_id:t.id,station_id:n})},yt=async n=>{const t=D(B);if(!t)throw new Error("User not found");await L.from("favorite_stations").delete().eq("user_id",t.id).eq("station_id",n)},pt="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20id='Calque_2'%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2037.88%2050'%3e%3cdefs%3e%3cstyle%3e.cls-1{fill:%2300b76d;}.cls-1,.cls-2{stroke-width:0px;}.cls-2{fill:%23fff;}%3c/style%3e%3c/defs%3e%3cg%20id='Calque_1-2'%3e%3cpath%20class='cls-1'%20d='M36.78,19.29c0,15.43-13.73,29.26-17.84,29.26S1.1,34.72,1.1,19.29C1.1,9.45,9.1,1.45,18.94,1.45s17.84,8,17.84,17.84Z'/%3e%3cpath%20class='cls-2'%20d='M28.26,19.3l-11.64,18.13c-.16.25-.43.38-.7.38-.11,0-.22-.02-.33-.07-.37-.16-.58-.57-.48-.97l2.82-11.35h-7.6c-.28,0-.53-.14-.69-.36-.16-.23-.19-.52-.09-.77l5.57-14.41c.12-.32.43-.53.78-.53h7.67c.28,0,.54.14.7.38.15.24.18.53.06.79l-3.3,7.49h6.53c.3,0,.59.17.73.43.15.27.13.59-.03.85Z'/%3e%3cpath%20class='cls-1'%20d='M18.94,50C14.57,50,0,35.32,0,18.94,0,8.5,8.5,0,18.94,0s18.94,8.5,18.94,18.94c0,16.38-14.57,31.06-18.94,31.06ZM18.94,3.04c-8.77,0-15.9,7.13-15.9,15.9,0,15.04,13.37,27.73,15.93,28.02,2.51-.29,15.88-12.99,15.88-28.02,0-8.77-7.13-15.9-15.9-15.9Z'/%3e%3c/g%3e%3c/svg%3e",xt=(n={})=>{const t=()=>{const o=localStorage.getItem("latitude"),s=localStorage.getItem("longitude");!o||!s||(h.set(+o),_.set(+s))},a=()=>(t(),new Promise((o,s)=>{navigator.geolocation.getCurrentPosition(r=>{var l;h.set(r.coords.latitude),_.set(r.coords.longitude),(l=n.onLocationLoaded)==null||l.call(n,[r.coords.latitude,r.coords.longitude]),o()},r=>{console.error(r),s(r)},{maximumAge:0,enableHighAccuracy:!0})})),e=([o,s])=>{localStorage.setItem("latitude",String(o)),localStorage.setItem("longitude",String(s))},i=()=>{const o=navigator.geolocation.watchPosition(s=>{h.set(s.coords.latitude),_.set(s.coords.longitude),e([s.coords.latitude,s.coords.longitude])},s=>{console.error(s)},{enableHighAccuracy:!0,maximumAge:0});return()=>navigator.geolocation.clearWatch(o)};return z(()=>{a();const o=i();return()=>{o()}}),{latitude:h,longitude:_}};export{ht as F,at as H,pt as S,bt as a,vt as f,_t as g,wt as l,yt as r,xt as u};
