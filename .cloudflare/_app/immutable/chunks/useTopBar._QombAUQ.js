import{o as d,w as s}from"./index.UaHqEmIZ.js";import{R as u}from"./style.HZSn-yMG.js";import"./index.RK-K-o1D.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="33ccade2-352c-40f0-bae2-b354b31effd6",e._sentryDebugIdIdentifier="sentry-dbid-33ccade2-352c-40f0-bae2-b354b31effd6")}catch{}})();const t=u.Home,a=!0,n=!0,c=s(t),o=s(!0),i=s(!0),b=s(""),B=(e={})=>{c.set(e.backUrl??t),o.set(e.isVisible??a),i.set(e.isBackButtonVisible??n),b.set(e.cssClass??""),d(()=>()=>{c.set(t),o.set(a),i.set(n),b.set("")})};export{i as a,c as b,b as c,o as i,B as u};
