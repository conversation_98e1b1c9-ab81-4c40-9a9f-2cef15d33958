import{s as m,B as f,J as b,K as p,L as _,a as B,M as k,d as o,N as d,i as y,r as c,C as u,A as g}from"./index.UaHqEmIZ.js";import{S as v,i as S,c as w,a as x,m as z,t as N,b as C,d as I}from"./index.RK-K-o1D.js";import{g as D,c as h}from"./style.HZSn-yMG.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="da462d60-9af1-42ec-95d6-6943cdaa4090",t._sentryDebugIdIdentifier="sentry-dbid-da462d60-9af1-42ec-95d6-6943cdaa4090")}catch{}})();function L(t){let e,n,s='<rect width="10" height="10" x="1" y="1" fill="currentColor" rx="1"><animate id="svgSpinnersBlocksShuffle30" fill="freeze" attributeName="x" begin="0;svgSpinnersBlocksShuffle3b.end" dur="0.2s" values="1;13"/><animate id="svgSpinnersBlocksShuffle31" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle38.end" dur="0.2s" values="1;13"/><animate id="svgSpinnersBlocksShuffle32" fill="freeze" attributeName="x" begin="svgSpinnersBlocksShuffle39.end" dur="0.2s" values="13;1"/><animate id="svgSpinnersBlocksShuffle33" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle3a.end" dur="0.2s" values="13;1"/></rect><rect width="10" height="10" x="1" y="13" fill="currentColor" rx="1"><animate id="svgSpinnersBlocksShuffle34" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle30.end" dur="0.2s" values="13;1"/><animate id="svgSpinnersBlocksShuffle35" fill="freeze" attributeName="x" begin="svgSpinnersBlocksShuffle31.end" dur="0.2s" values="1;13"/><animate id="svgSpinnersBlocksShuffle36" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle32.end" dur="0.2s" values="1;13"/><animate id="svgSpinnersBlocksShuffle37" fill="freeze" attributeName="x" begin="svgSpinnersBlocksShuffle33.end" dur="0.2s" values="13;1"/></rect><rect width="10" height="10" x="13" y="13" fill="currentColor" rx="1"><animate id="svgSpinnersBlocksShuffle38" fill="freeze" attributeName="x" begin="svgSpinnersBlocksShuffle34.end" dur="0.2s" values="13;1"/><animate id="svgSpinnersBlocksShuffle39" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle35.end" dur="0.2s" values="13;1"/><animate id="svgSpinnersBlocksShuffle3a" fill="freeze" attributeName="x" begin="svgSpinnersBlocksShuffle36.end" dur="0.2s" values="1;13"/><animate id="svgSpinnersBlocksShuffle3b" fill="freeze" attributeName="y" begin="svgSpinnersBlocksShuffle37.end" dur="0.2s" values="1;13"/></rect>',a=[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},t[0]],r={};for(let i=0;i<a.length;i+=1)r=f(r,a[i]);return{c(){e=b("svg"),n=new p(!0),this.h()},l(i){e=_(i,"svg",{viewBox:!0,width:!0,height:!0});var l=B(e);n=k(l,!0),l.forEach(o),this.h()},h(){n.a=null,d(e,r)},m(i,l){y(i,e,l),n.m(s,e)},p(i,[l]){d(e,r=D(a,[{viewBox:"0 0 24 24"},{width:"1.2em"},{height:"1.2em"},l&1&&i[0]]))},i:c,o:c,d(i){i&&o(e)}}}function E(t,e,n){return t.$$set=s=>{n(0,e=f(f({},e),u(s)))},e=u(e),[e]}class H extends v{constructor(e){super(),S(this,e,E,L,m,{})}}function q(t){let e,n;return e=new H({props:{width:t[0],height:t[0],class:h("text-primary-500/50",t[1].class)}}),{c(){w(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,a){z(e,s,a),n=!0},p(s,[a]){const r={};a&1&&(r.width=s[0]),a&1&&(r.height=s[0]),a&2&&(r.class=h("text-primary-500/50",s[1].class)),e.$set(r)},i(s){n||(N(e.$$.fragment,s),n=!0)},o(s){C(e.$$.fragment,s),n=!1},d(s){I(e,s)}}}function A(t,e,n){const s=["size"];let a=g(e,s),{size:r=50}=e;return t.$$set=i=>{e=f(f({},e),u(i)),n(1,a=g(e,s)),"size"in i&&n(0,r=i.size)},[r,a]}class P extends v{constructor(e){super(),S(this,e,A,q,m,{size:0})}}export{P as L};
