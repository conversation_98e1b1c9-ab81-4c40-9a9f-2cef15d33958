(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},o=new Error().stack;o&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[o]="0982c8e6-9bfd-42ce-9760-28e228cf5ab6",e._sentryDebugIdIdentifier="sentry-dbid-0982c8e6-9bfd-42ce-9760-28e228cf5ab6")}catch{}})();const n="Confirm",s="Cancel",r="Are you sure?",t="More options",i="There is nothing here",c="Coming soon",a="Oops!",f="Something wrong happened, please try again later.",_="Dismiss",u="files maximum",m={message:"Install this app now for a better experience!",install:"Install",skip:"Skip for now"},d="First hour is free!",l="Free for",b="OFF",p="Free",g="Hr",h="Min",y="Sec",w="/Hr",x={confirm:n,cancel:s,are_you_sure:r,more_options:t,not_found:i,coming_soon:c,oops:a,something_wrong_happened:f,dismiss:_,files_maximum:u,installation:m,first_hour_free:d,free_for:l,off:b,free:p,hour_abbr:g,minute_abbr:h,second_abbr:y,hourly_rate:w};export{r as are_you_sure,s as cancel,c as coming_soon,n as confirm,x as default,_ as dismiss,u as files_maximum,d as first_hour_free,p as free,l as free_for,g as hour_abbr,w as hourly_rate,m as installation,h as minute_abbr,t as more_options,i as not_found,b as off,a as oops,y as second_abbr,f as something_wrong_happened};
