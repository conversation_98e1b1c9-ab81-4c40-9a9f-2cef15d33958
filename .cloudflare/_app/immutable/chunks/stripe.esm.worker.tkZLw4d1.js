import"./index.RK-K-o1D.js";import{a as uo,c as co}from"./_commonjsHelpers.jVd2wRzr.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="220a133e-7c5e-47ab-bca9-54db7cf23054",t._sentryDebugIdIdentifier="sentry-dbid-220a133e-7c5e-47ab-bca9-54db7cf23054")}catch{}})();class k{getClientName(){throw new Error("getClientName not implemented.")}makeRequest(e,r,n,o,a,s,u,c){throw new Error("makeRequest not implemented.")}static makeTimeoutError(){const e=new TypeError(k.TIMEOUT_ERROR_CODE);return e.code=k.TIMEOUT_ERROR_CODE,e}}k.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"];k.TIMEOUT_ERROR_CODE="ETIMEDOUT";class $n{constructor(e,r){this._statusCode=e,this._headers=r}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw new Error("getRawResponse not implemented.")}toStream(e){throw new Error("toStream not implemented.")}toJSON(){throw new Error("toJSON not implemented.")}}class St extends k{constructor(e){if(super(),!e){if(!globalThis.fetch)throw new Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=St.makeFetchWithAbortTimeout(e):this._fetchFn=St.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(r,n,o)=>{let a;const s=new Promise((c,l)=>{a=setTimeout(()=>{a=null,l(k.makeTimeoutError())},o)}),u=e(r,n);return Promise.race([u,s]).finally(()=>{a&&clearTimeout(a)})}}static makeFetchWithAbortTimeout(e){return async(r,n,o)=>{const a=new AbortController;let s=setTimeout(()=>{s=null,a.abort(k.makeTimeoutError())},o);try{return await e(r,Object.assign(Object.assign({},n),{signal:a.signal}))}catch(u){throw u.name==="AbortError"?k.makeTimeoutError():u}finally{s&&clearTimeout(s)}}}getClientName(){return"fetch"}async makeRequest(e,r,n,o,a,s,u,c){const l=u==="http",h=new URL(n,`${l?"http":"https"}://${e}`);h.port=r;const p=o=="POST"||o=="PUT"||o=="PATCH",d=s||(p?"":void 0),f=await this._fetchFn(h.toString(),{method:o,headers:a,body:d},c);return new hr(f)}}class hr extends $n{constructor(e){super(e.status,hr._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){const r={};for(const n of e){if(!Array.isArray(n)||n.length!=2)throw new Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");r[n[0]]=n[1]}return r}}class Cn{computeHMACSignature(e,r){throw new Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,r){throw new Error("computeHMACSignatureAsync not implemented.")}}class Rn extends Error{}class ho extends Cn{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,r){throw new Rn("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,r){const n=new TextEncoder,o=await this.subtleCrypto.importKey("raw",n.encode(r),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),a=await this.subtleCrypto.sign("hmac",o,n.encode(e)),s=new Uint8Array(a),u=new Array(s.length);for(let c=0;c<s.length;c++)u[c]=rr[s[c]];return u.join("")}}const rr=new Array(256);for(let t=0;t<rr.length;t++)rr[t]=t.toString(16).padStart(2,"0");class po{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw new Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const r=Math.random()*16|0;return(e==="x"?r:r&3|8).toString(16)})}secureCompare(e,r){if(e.length!==r.length)return!1;const n=e.length;let o=0;for(let a=0;a<n;++a)o|=e.charCodeAt(a)^r.charCodeAt(a);return o===0}createEmitter(){throw new Error("createEmitter not implemented.")}tryBufferData(e){throw new Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw new Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new St(e)}createDefaultHttpClient(){throw new Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw new Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new ho(e)}createDefaultCryptoProvider(){throw new Error("createDefaultCryptoProvider not implemented.")}}class fo extends Event{constructor(e,r){super(e),this.data=r}}class mo{constructor(){this.eventTarget=new EventTarget,this.listenerMapping=new Map}on(e,r){const n=o=>{r(o.data)};return this.listenerMapping.set(r,n),this.eventTarget.addEventListener(e,n)}removeListener(e,r){const n=this.listenerMapping.get(r);return this.listenerMapping.delete(r),this.eventTarget.removeEventListener(e,n)}once(e,r){const n=o=>{r(o.data)};return this.listenerMapping.set(r,n),this.eventTarget.addEventListener(e,n,{once:!0})}emit(e,r){return this.eventTarget.dispatchEvent(new fo(e,r))}}class yo extends po{getUname(){return Promise.resolve(null)}createEmitter(){return new mo}tryBufferData(e){if(e.file.data instanceof ReadableStream)throw new Error("Uploading a file as a stream is not supported in non-Node environments. Please open or upvote an issue at github.com/stripe/stripe-node if you use this, detailing your use-case.");return Promise.resolve(e)}createNodeHttpClient(){throw new Error("Stripe: `createNodeHttpClient()` is not available in non-Node environments. Please use `createFetchHttpClient()` instead.")}createDefaultHttpClient(){return super.createFetchHttpClient()}createNodeCryptoProvider(){throw new Error("Stripe: `createNodeCryptoProvider()` is not available in non-Node environments. Please use `createSubtleCryptoProvider()` instead.")}createDefaultCryptoProvider(){return this.createSubtleCryptoProvider()}}const In=t=>{switch(t.type){case"card_error":return new Mn(t);case"invalid_request_error":return new Gn(t);case"api_error":return new dr(t);case"authentication_error":return new pr(t);case"rate_limit_error":return new fr(t);case"idempotency_error":return new Nn(t);case"invalid_grant":return new Fn(t);default:return new Un(t)}};class I extends Error{constructor(e={}){super(e.message),this.type=this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}I.generate=In;class Mn extends I{}class Gn extends I{}class dr extends I{}class pr extends I{}class Dn extends I{}class fr extends I{}class kn extends I{}class K extends I{constructor(e,r,n={}){super(n),this.header=e,this.payload=r}}class Nn extends I{}class Fn extends I{}class Un extends I{}const br=Object.freeze(Object.defineProperty({__proto__:null,StripeAPIError:dr,StripeAuthenticationError:pr,StripeCardError:Mn,StripeConnectionError:kn,StripeError:I,StripeIdempotencyError:Nn,StripeInvalidGrantError:Fn,StripeInvalidRequestError:Gn,StripePermissionError:Dn,StripeRateLimitError:fr,StripeSignatureVerificationError:K,StripeUnknownError:Un,generate:In},Symbol.toStringTag,{value:"Module"})),vo="2023-10-16";function Po(t,e){for(const r in e){const n=r[0].toLowerCase()+r.substring(1),o=new e[r](t);this[n]=o}}function A(t,e){return function(r){return new Po(r,e)}}var go=Error,To=EvalError,_o=RangeError,Eo=ReferenceError,Ln=SyntaxError,Je=TypeError,So=URIError,bo=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var o=42;e[r]=o;for(r in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var a=Object.getOwnPropertySymbols(e);if(a.length!==1||a[0]!==r||!Object.prototype.propertyIsEnumerable.call(e,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var s=Object.getOwnPropertyDescriptor(e,r);if(s.value!==o||s.enumerable!==!0)return!1}return!0},Or=typeof Symbol<"u"&&Symbol,Oo=bo,wo=function(){return typeof Or!="function"||typeof Symbol!="function"||typeof Or("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:Oo()},Ct={__proto__:null,foo:{}},xo=Object,Ao=function(){return{__proto__:Ct}.foo===Ct.foo&&!(Ct instanceof xo)},$o="Function.prototype.bind called on incompatible ",Co=Object.prototype.toString,Ro=Math.max,Io="[object Function]",wr=function(e,r){for(var n=[],o=0;o<e.length;o+=1)n[o]=e[o];for(var a=0;a<r.length;a+=1)n[a+e.length]=r[a];return n},Mo=function(e,r){for(var n=[],o=r||0,a=0;o<e.length;o+=1,a+=1)n[a]=e[o];return n},Go=function(t,e){for(var r="",n=0;n<t.length;n+=1)r+=t[n],n+1<t.length&&(r+=e);return r},Do=function(e){var r=this;if(typeof r!="function"||Co.apply(r)!==Io)throw new TypeError($o+r);for(var n=Mo(arguments,1),o,a=function(){if(this instanceof o){var h=r.apply(this,wr(n,arguments));return Object(h)===h?h:this}return r.apply(e,wr(n,arguments))},s=Ro(0,r.length-n.length),u=[],c=0;c<s;c++)u[c]="$"+c;if(o=Function("binder","return function ("+Go(u,",")+"){ return binder.apply(this,arguments); }")(a),r.prototype){var l=function(){};l.prototype=r.prototype,o.prototype=new l,l.prototype=null}return o},ko=Do,mr=Function.prototype.bind||ko,No=Function.prototype.call,Fo=Object.prototype.hasOwnProperty,Uo=mr,Lo=Uo.call(No,Fo),P,qo=go,Ho=To,Bo=_o,Wo=Eo,Te=Ln,ge=Je,zo=So,qn=Function,Rt=function(t){try{return qn('"use strict"; return ('+t+").constructor;")()}catch{}},re=Object.getOwnPropertyDescriptor;if(re)try{re({},"")}catch{re=null}var It=function(){throw new ge},jo=re?function(){try{return arguments.callee,It}catch{try{return re(arguments,"callee").get}catch{return It}}}():It,le=wo(),Vo=Ao(),x=Object.getPrototypeOf||(Vo?function(t){return t.__proto__}:null),Pe={},Ko=typeof Uint8Array>"u"||!x?P:x(Uint8Array),ne={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?P:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?P:ArrayBuffer,"%ArrayIteratorPrototype%":le&&x?x([][Symbol.iterator]()):P,"%AsyncFromSyncIteratorPrototype%":P,"%AsyncFunction%":Pe,"%AsyncGenerator%":Pe,"%AsyncGeneratorFunction%":Pe,"%AsyncIteratorPrototype%":Pe,"%Atomics%":typeof Atomics>"u"?P:Atomics,"%BigInt%":typeof BigInt>"u"?P:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?P:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?P:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?P:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":qo,"%eval%":eval,"%EvalError%":Ho,"%Float32Array%":typeof Float32Array>"u"?P:Float32Array,"%Float64Array%":typeof Float64Array>"u"?P:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?P:FinalizationRegistry,"%Function%":qn,"%GeneratorFunction%":Pe,"%Int8Array%":typeof Int8Array>"u"?P:Int8Array,"%Int16Array%":typeof Int16Array>"u"?P:Int16Array,"%Int32Array%":typeof Int32Array>"u"?P:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":le&&x?x(x([][Symbol.iterator]())):P,"%JSON%":typeof JSON=="object"?JSON:P,"%Map%":typeof Map>"u"?P:Map,"%MapIteratorPrototype%":typeof Map>"u"||!le||!x?P:x(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?P:Promise,"%Proxy%":typeof Proxy>"u"?P:Proxy,"%RangeError%":Bo,"%ReferenceError%":Wo,"%Reflect%":typeof Reflect>"u"?P:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?P:Set,"%SetIteratorPrototype%":typeof Set>"u"||!le||!x?P:x(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?P:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":le&&x?x(""[Symbol.iterator]()):P,"%Symbol%":le?Symbol:P,"%SyntaxError%":Te,"%ThrowTypeError%":jo,"%TypedArray%":Ko,"%TypeError%":ge,"%Uint8Array%":typeof Uint8Array>"u"?P:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?P:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?P:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?P:Uint32Array,"%URIError%":zo,"%WeakMap%":typeof WeakMap>"u"?P:WeakMap,"%WeakRef%":typeof WeakRef>"u"?P:WeakRef,"%WeakSet%":typeof WeakSet>"u"?P:WeakSet};if(x)try{null.error}catch(t){var Qo=x(x(t));ne["%Error.prototype%"]=Qo}var Jo=function t(e){var r;if(e==="%AsyncFunction%")r=Rt("async function () {}");else if(e==="%GeneratorFunction%")r=Rt("function* () {}");else if(e==="%AsyncGeneratorFunction%")r=Rt("async function* () {}");else if(e==="%AsyncGenerator%"){var n=t("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if(e==="%AsyncIteratorPrototype%"){var o=t("%AsyncGenerator%");o&&x&&(r=x(o.prototype))}return ne[e]=r,r},xr={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Ye=mr,bt=Lo,Yo=Ye.call(Function.call,Array.prototype.concat),Xo=Ye.call(Function.apply,Array.prototype.splice),Ar=Ye.call(Function.call,String.prototype.replace),Ot=Ye.call(Function.call,String.prototype.slice),Zo=Ye.call(Function.call,RegExp.prototype.exec),ea=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,ta=/\\(\\)?/g,ra=function(e){var r=Ot(e,0,1),n=Ot(e,-1);if(r==="%"&&n!=="%")throw new Te("invalid intrinsic syntax, expected closing `%`");if(n==="%"&&r!=="%")throw new Te("invalid intrinsic syntax, expected opening `%`");var o=[];return Ar(e,ea,function(a,s,u,c){o[o.length]=u?Ar(c,ta,"$1"):s||a}),o},na=function(e,r){var n=e,o;if(bt(xr,n)&&(o=xr[n],n="%"+o[0]+"%"),bt(ne,n)){var a=ne[n];if(a===Pe&&(a=Jo(n)),typeof a>"u"&&!r)throw new ge("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:o,name:n,value:a}}throw new Te("intrinsic "+e+" does not exist!")},Se=function(e,r){if(typeof e!="string"||e.length===0)throw new ge("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof r!="boolean")throw new ge('"allowMissing" argument must be a boolean');if(Zo(/^%?[^%]*%?$/,e)===null)throw new Te("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=ra(e),o=n.length>0?n[0]:"",a=na("%"+o+"%",r),s=a.name,u=a.value,c=!1,l=a.alias;l&&(o=l[0],Xo(n,Yo([0,1],l)));for(var h=1,p=!0;h<n.length;h+=1){var d=n[h],f=Ot(d,0,1),m=Ot(d,-1);if((f==='"'||f==="'"||f==="`"||m==='"'||m==="'"||m==="`")&&f!==m)throw new Te("property names with quotes must have matching quotes");if((d==="constructor"||!p)&&(c=!0),o+="."+d,s="%"+o+"%",bt(ne,s))u=ne[s];else if(u!=null){if(!(d in u)){if(!r)throw new ge("base intrinsic for "+e+" exists, but the property is not available.");return}if(re&&h+1>=n.length){var v=re(u,d);p=!!v,p&&"get"in v&&!("originalValue"in v.get)?u=v.get:u=u[d]}else p=bt(u,d),u=u[d];p&&!c&&(ne[s]=u)}}return u},Hn={exports:{}},Mt,$r;function yr(){if($r)return Mt;$r=1;var t=Se,e=t("%Object.defineProperty%",!0)||!1;if(e)try{e({},"a",{value:1})}catch{e=!1}return Mt=e,Mt}var oa=Se,_t=oa("%Object.getOwnPropertyDescriptor%",!0);if(_t)try{_t([],"length")}catch{_t=null}var Bn=_t,Cr=yr(),aa=Ln,ue=Je,Rr=Bn,ia=function(e,r,n){if(!e||typeof e!="object"&&typeof e!="function")throw new ue("`obj` must be an object or a function`");if(typeof r!="string"&&typeof r!="symbol")throw new ue("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new ue("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new ue("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new ue("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new ue("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,a=arguments.length>4?arguments[4]:null,s=arguments.length>5?arguments[5]:null,u=arguments.length>6?arguments[6]:!1,c=!!Rr&&Rr(e,r);if(Cr)Cr(e,r,{configurable:s===null&&c?c.configurable:!s,enumerable:o===null&&c?c.enumerable:!o,value:n,writable:a===null&&c?c.writable:!a});else if(u||!o&&!a&&!s)e[r]=n;else throw new aa("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},nr=yr(),Wn=function(){return!!nr};Wn.hasArrayLengthDefineBug=function(){if(!nr)return null;try{return nr([],"length",{value:1}).length!==1}catch{return!0}};var sa=Wn,la=Se,Ir=ia,ua=sa(),Mr=Bn,Gr=Je,ca=la("%Math.floor%"),ha=function(e,r){if(typeof e!="function")throw new Gr("`fn` is not a function");if(typeof r!="number"||r<0||r>4294967295||ca(r)!==r)throw new Gr("`length` must be a positive 32-bit integer");var n=arguments.length>2&&!!arguments[2],o=!0,a=!0;if("length"in e&&Mr){var s=Mr(e,"length");s&&!s.configurable&&(o=!1),s&&!s.writable&&(a=!1)}return(o||a||!n)&&(ua?Ir(e,"length",r,!0,!0):Ir(e,"length",r)),e};(function(t){var e=mr,r=Se,n=ha,o=Je,a=r("%Function.prototype.apply%"),s=r("%Function.prototype.call%"),u=r("%Reflect.apply%",!0)||e.call(s,a),c=yr(),l=r("%Math.max%");t.exports=function(d){if(typeof d!="function")throw new o("a function is required");var f=u(e,s,arguments);return n(f,1+l(0,d.length-(arguments.length-1)),!0)};var h=function(){return u(e,a,arguments)};c?c(t.exports,"apply",{value:h}):t.exports.apply=h})(Hn);var da=Hn.exports,zn=Se,jn=da,pa=jn(zn("String.prototype.indexOf")),fa=function(e,r){var n=zn(e,!!r);return typeof n=="function"&&pa(e,".prototype.")>-1?jn(n):n};const ma={},ya=Object.freeze(Object.defineProperty({__proto__:null,default:ma},Symbol.toStringTag,{value:"Module"})),va=uo(ya);var vr=typeof Map=="function"&&Map.prototype,Gt=Object.getOwnPropertyDescriptor&&vr?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,wt=vr&&Gt&&typeof Gt.get=="function"?Gt.get:null,Dr=vr&&Map.prototype.forEach,Pr=typeof Set=="function"&&Set.prototype,Dt=Object.getOwnPropertyDescriptor&&Pr?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,xt=Pr&&Dt&&typeof Dt.get=="function"?Dt.get:null,kr=Pr&&Set.prototype.forEach,Pa=typeof WeakMap=="function"&&WeakMap.prototype,je=Pa?WeakMap.prototype.has:null,ga=typeof WeakSet=="function"&&WeakSet.prototype,Ve=ga?WeakSet.prototype.has:null,Ta=typeof WeakRef=="function"&&WeakRef.prototype,Nr=Ta?WeakRef.prototype.deref:null,_a=Boolean.prototype.valueOf,Ea=Object.prototype.toString,Sa=Function.prototype.toString,ba=String.prototype.match,gr=String.prototype.slice,J=String.prototype.replace,Oa=String.prototype.toUpperCase,Fr=String.prototype.toLowerCase,Vn=RegExp.prototype.test,Ur=Array.prototype.concat,H=Array.prototype.join,wa=Array.prototype.slice,Lr=Math.floor,or=typeof BigInt=="function"?BigInt.prototype.valueOf:null,kt=Object.getOwnPropertySymbols,ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,_e=typeof Symbol=="function"&&typeof Symbol.iterator=="object",C=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===_e||!0)?Symbol.toStringTag:null,Kn=Object.prototype.propertyIsEnumerable,qr=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function Hr(t,e){if(t===1/0||t===-1/0||t!==t||t&&t>-1e3&&t<1e3||Vn.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof t=="number"){var n=t<0?-Lr(-t):Lr(t);if(n!==t){var o=String(n),a=gr.call(e,o.length+1);return J.call(o,r,"$&_")+"."+J.call(J.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return J.call(e,r,"$&_")}var ir=va,Br=ir.custom,Wr=Jn(Br)?Br:null,xa=function t(e,r,n,o){var a=r||{};if(Q(a,"quoteStyle")&&a.quoteStyle!=="single"&&a.quoteStyle!=="double")throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Q(a,"maxStringLength")&&(typeof a.maxStringLength=="number"?a.maxStringLength<0&&a.maxStringLength!==1/0:a.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var s=Q(a,"customInspect")?a.customInspect:!0;if(typeof s!="boolean"&&s!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Q(a,"indent")&&a.indent!==null&&a.indent!=="	"&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Q(a,"numericSeparator")&&typeof a.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var u=a.numericSeparator;if(typeof e>"u")return"undefined";if(e===null)return"null";if(typeof e=="boolean")return e?"true":"false";if(typeof e=="string")return Xn(e,a);if(typeof e=="number"){if(e===0)return 1/0/e>0?"0":"-0";var c=String(e);return u?Hr(e,c):c}if(typeof e=="bigint"){var l=String(e)+"n";return u?Hr(e,l):l}var h=typeof a.depth>"u"?5:a.depth;if(typeof n>"u"&&(n=0),n>=h&&h>0&&typeof e=="object")return sr(e)?"[Array]":"[Object]";var p=za(a,n);if(typeof o>"u")o=[];else if(Yn(o,e)>=0)return"[Circular]";function d(B,ie,Xe){if(ie&&(o=wa.call(o),o.push(ie)),Xe){var se={depth:a.depth};return Q(a,"quoteStyle")&&(se.quoteStyle=a.quoteStyle),t(B,se,n+1,o)}return t(B,a,n+1,o)}if(typeof e=="function"&&!zr(e)){var f=ka(e),m=Ze(e,d);return"[Function"+(f?": "+f:" (anonymous)")+"]"+(m.length>0?" { "+H.call(m,", ")+" }":"")}if(Jn(e)){var v=_e?J.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):ar.call(e);return typeof e=="object"&&!_e?we(v):v}if(Ha(e)){for(var g="<"+Fr.call(String(e.nodeName)),y=e.attributes||[],_=0;_<y.length;_++)g+=" "+y[_].name+"="+Qn(Aa(y[_].value),"double",a);return g+=">",e.childNodes&&e.childNodes.length&&(g+="..."),g+="</"+Fr.call(String(e.nodeName))+">",g}if(sr(e)){if(e.length===0)return"[]";var E=Ze(e,d);return p&&!Wa(E)?"["+lr(E,p)+"]":"[ "+H.call(E,", ")+" ]"}if(Ca(e)){var O=Ze(e,d);return!("cause"in Error.prototype)&&"cause"in e&&!Kn.call(e,"cause")?"{ ["+String(e)+"] "+H.call(Ur.call("[cause]: "+d(e.cause),O),", ")+" }":O.length===0?"["+String(e)+"]":"{ ["+String(e)+"] "+H.call(O,", ")+" }"}if(typeof e=="object"&&s){if(Wr&&typeof e[Wr]=="function"&&ir)return ir(e,{depth:h-n});if(s!=="symbol"&&typeof e.inspect=="function")return e.inspect()}if(Na(e)){var D=[];return Dr&&Dr.call(e,function(B,ie){D.push(d(ie,e,!0)+" => "+d(B,e))}),jr("Map",wt.call(e),D,p)}if(La(e)){var M=[];return kr&&kr.call(e,function(B){M.push(d(B,e))}),jr("Set",xt.call(e),M,p)}if(Fa(e))return Nt("WeakMap");if(qa(e))return Nt("WeakSet");if(Ua(e))return Nt("WeakRef");if(Ia(e))return we(d(Number(e)));if(Ga(e))return we(d(or.call(e)));if(Ma(e))return we(_a.call(e));if(Ra(e))return we(d(String(e)));if(typeof window<"u"&&e===window)return"{ [object Window] }";if(e===co)return"{ [object globalThis] }";if(!$a(e)&&!zr(e)){var b=Ze(e,d),G=qr?qr(e)===Object.prototype:e instanceof Object||e.constructor===Object,oe=e instanceof Object?"":"null prototype",ae=!G&&C&&Object(e)===e&&C in e?gr.call(Y(e),8,-1):oe?"Object":"",Oe=G||typeof e.constructor!="function"?"":e.constructor.name?e.constructor.name+" ":"",N=Oe+(ae||oe?"["+H.call(Ur.call([],ae||[],oe||[]),": ")+"] ":"");return b.length===0?N+"{}":p?N+"{"+lr(b,p)+"}":N+"{ "+H.call(b,", ")+" }"}return String(e)};function Qn(t,e,r){var n=(r.quoteStyle||e)==="double"?'"':"'";return n+t+n}function Aa(t){return J.call(String(t),/"/g,"&quot;")}function sr(t){return Y(t)==="[object Array]"&&(!C||!(typeof t=="object"&&C in t))}function $a(t){return Y(t)==="[object Date]"&&(!C||!(typeof t=="object"&&C in t))}function zr(t){return Y(t)==="[object RegExp]"&&(!C||!(typeof t=="object"&&C in t))}function Ca(t){return Y(t)==="[object Error]"&&(!C||!(typeof t=="object"&&C in t))}function Ra(t){return Y(t)==="[object String]"&&(!C||!(typeof t=="object"&&C in t))}function Ia(t){return Y(t)==="[object Number]"&&(!C||!(typeof t=="object"&&C in t))}function Ma(t){return Y(t)==="[object Boolean]"&&(!C||!(typeof t=="object"&&C in t))}function Jn(t){if(_e)return t&&typeof t=="object"&&t instanceof Symbol;if(typeof t=="symbol")return!0;if(!t||typeof t!="object"||!ar)return!1;try{return ar.call(t),!0}catch{}return!1}function Ga(t){if(!t||typeof t!="object"||!or)return!1;try{return or.call(t),!0}catch{}return!1}var Da=Object.prototype.hasOwnProperty||function(t){return t in this};function Q(t,e){return Da.call(t,e)}function Y(t){return Ea.call(t)}function ka(t){if(t.name)return t.name;var e=ba.call(Sa.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}function Yn(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}function Na(t){if(!wt||!t||typeof t!="object")return!1;try{wt.call(t);try{xt.call(t)}catch{return!0}return t instanceof Map}catch{}return!1}function Fa(t){if(!je||!t||typeof t!="object")return!1;try{je.call(t,je);try{Ve.call(t,Ve)}catch{return!0}return t instanceof WeakMap}catch{}return!1}function Ua(t){if(!Nr||!t||typeof t!="object")return!1;try{return Nr.call(t),!0}catch{}return!1}function La(t){if(!xt||!t||typeof t!="object")return!1;try{xt.call(t);try{wt.call(t)}catch{return!0}return t instanceof Set}catch{}return!1}function qa(t){if(!Ve||!t||typeof t!="object")return!1;try{Ve.call(t,Ve);try{je.call(t,je)}catch{return!0}return t instanceof WeakSet}catch{}return!1}function Ha(t){return!t||typeof t!="object"?!1:typeof HTMLElement<"u"&&t instanceof HTMLElement?!0:typeof t.nodeName=="string"&&typeof t.getAttribute=="function"}function Xn(t,e){if(t.length>e.maxStringLength){var r=t.length-e.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return Xn(gr.call(t,0,e.maxStringLength),e)+n}var o=J.call(J.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Ba);return Qn(o,"single",e)}function Ba(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+Oa.call(e.toString(16))}function we(t){return"Object("+t+")"}function Nt(t){return t+" { ? }"}function jr(t,e,r,n){var o=n?lr(r,n):H.call(r,", ");return t+" ("+e+") {"+o+"}"}function Wa(t){for(var e=0;e<t.length;e++)if(Yn(t[e],`
`)>=0)return!1;return!0}function za(t,e){var r;if(t.indent==="	")r="	";else if(typeof t.indent=="number"&&t.indent>0)r=H.call(Array(t.indent+1)," ");else return null;return{base:r,prev:H.call(Array(e+1),r)}}function lr(t,e){if(t.length===0)return"";var r=`
`+e.prev+e.base;return r+H.call(t,","+r)+`
`+e.prev}function Ze(t,e){var r=sr(t),n=[];if(r){n.length=t.length;for(var o=0;o<t.length;o++)n[o]=Q(t,o)?e(t[o],t):""}var a=typeof kt=="function"?kt(t):[],s;if(_e){s={};for(var u=0;u<a.length;u++)s["$"+a[u]]=a[u]}for(var c in t)Q(t,c)&&(r&&String(Number(c))===c&&c<t.length||_e&&s["$"+c]instanceof Symbol||(Vn.call(/[^\w$]/,c)?n.push(e(c,t)+": "+e(t[c],t)):n.push(c+": "+e(t[c],t))));if(typeof kt=="function")for(var l=0;l<a.length;l++)Kn.call(t,a[l])&&n.push("["+e(a[l])+"]: "+e(t[a[l]],t));return n}var Zn=Se,be=fa,ja=xa,Va=Je,et=Zn("%WeakMap%",!0),tt=Zn("%Map%",!0),Ka=be("WeakMap.prototype.get",!0),Qa=be("WeakMap.prototype.set",!0),Ja=be("WeakMap.prototype.has",!0),Ya=be("Map.prototype.get",!0),Xa=be("Map.prototype.set",!0),Za=be("Map.prototype.has",!0),Tr=function(t,e){for(var r=t,n;(n=r.next)!==null;r=n)if(n.key===e)return r.next=n.next,n.next=t.next,t.next=n,n},ei=function(t,e){var r=Tr(t,e);return r&&r.value},ti=function(t,e,r){var n=Tr(t,e);n?n.value=r:t.next={key:e,next:t.next,value:r}},ri=function(t,e){return!!Tr(t,e)},ni=function(){var e,r,n,o={assert:function(a){if(!o.has(a))throw new Va("Side channel does not contain "+ja(a))},get:function(a){if(et&&a&&(typeof a=="object"||typeof a=="function")){if(e)return Ka(e,a)}else if(tt){if(r)return Ya(r,a)}else if(n)return ei(n,a)},has:function(a){if(et&&a&&(typeof a=="object"||typeof a=="function")){if(e)return Ja(e,a)}else if(tt){if(r)return Za(r,a)}else if(n)return ri(n,a);return!1},set:function(a,s){et&&a&&(typeof a=="object"||typeof a=="function")?(e||(e=new et),Qa(e,a,s)):tt?(r||(r=new tt),Xa(r,a,s)):(n||(n={key:{},next:null}),ti(n,a,s))}};return o},oi=String.prototype.replace,ai=/%20/g,Ft={RFC1738:"RFC1738",RFC3986:"RFC3986"},_r={default:Ft.RFC3986,formatters:{RFC1738:function(t){return oi.call(t,ai,"+")},RFC3986:function(t){return String(t)}},RFC1738:Ft.RFC1738,RFC3986:Ft.RFC3986},ii=_r,Ut=Object.prototype.hasOwnProperty,te=Array.isArray,U=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),si=function(e){for(;e.length>1;){var r=e.pop(),n=r.obj[r.prop];if(te(n)){for(var o=[],a=0;a<n.length;++a)typeof n[a]<"u"&&o.push(n[a]);r.obj[r.prop]=o}}},eo=function(e,r){for(var n=r&&r.plainObjects?Object.create(null):{},o=0;o<e.length;++o)typeof e[o]<"u"&&(n[o]=e[o]);return n},li=function t(e,r,n){if(!r)return e;if(typeof r!="object"){if(te(e))e.push(r);else if(e&&typeof e=="object")(n&&(n.plainObjects||n.allowPrototypes)||!Ut.call(Object.prototype,r))&&(e[r]=!0);else return[e,r];return e}if(!e||typeof e!="object")return[e].concat(r);var o=e;return te(e)&&!te(r)&&(o=eo(e,n)),te(e)&&te(r)?(r.forEach(function(a,s){if(Ut.call(e,s)){var u=e[s];u&&typeof u=="object"&&a&&typeof a=="object"?e[s]=t(u,a,n):e.push(a)}else e[s]=a}),e):Object.keys(r).reduce(function(a,s){var u=r[s];return Ut.call(a,s)?a[s]=t(a[s],u,n):a[s]=u,a},o)},ui=function(e,r){return Object.keys(r).reduce(function(n,o){return n[o]=r[o],n},e)},ci=function(t,e,r){var n=t.replace(/\+/g," ");if(r==="iso-8859-1")return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch{return n}},hi=function(e,r,n,o,a){if(e.length===0)return e;var s=e;if(typeof e=="symbol"?s=Symbol.prototype.toString.call(e):typeof e!="string"&&(s=String(e)),n==="iso-8859-1")return escape(s).replace(/%u[0-9a-f]{4}/gi,function(h){return"%26%23"+parseInt(h.slice(2),16)+"%3B"});for(var u="",c=0;c<s.length;++c){var l=s.charCodeAt(c);if(l===45||l===46||l===95||l===126||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||a===ii.RFC1738&&(l===40||l===41)){u+=s.charAt(c);continue}if(l<128){u=u+U[l];continue}if(l<2048){u=u+(U[192|l>>6]+U[128|l&63]);continue}if(l<55296||l>=57344){u=u+(U[224|l>>12]+U[128|l>>6&63]+U[128|l&63]);continue}c+=1,l=65536+((l&1023)<<10|s.charCodeAt(c)&1023),u+=U[240|l>>18]+U[128|l>>12&63]+U[128|l>>6&63]+U[128|l&63]}return u},di=function(e){for(var r=[{obj:{o:e},prop:"o"}],n=[],o=0;o<r.length;++o)for(var a=r[o],s=a.obj[a.prop],u=Object.keys(s),c=0;c<u.length;++c){var l=u[c],h=s[l];typeof h=="object"&&h!==null&&n.indexOf(h)===-1&&(r.push({obj:s,prop:l}),n.push(h))}return si(r),e},pi=function(e){return Object.prototype.toString.call(e)==="[object RegExp]"},fi=function(e){return!e||typeof e!="object"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},mi=function(e,r){return[].concat(e,r)},yi=function(e,r){if(te(e)){for(var n=[],o=0;o<e.length;o+=1)n.push(r(e[o]));return n}return r(e)},to={arrayToObject:eo,assign:ui,combine:mi,compact:di,decode:ci,encode:hi,isBuffer:fi,isRegExp:pi,maybeMap:yi,merge:li},ro=ni,Et=to,Ke=_r,vi=Object.prototype.hasOwnProperty,Vr={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,r){return e+"["+r+"]"},repeat:function(e){return e}},W=Array.isArray,Pi=Array.prototype.push,no=function(t,e){Pi.apply(t,W(e)?e:[e])},gi=Date.prototype.toISOString,Kr=Ke.default,$={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:Et.encode,encodeValuesOnly:!1,format:Kr,formatter:Ke.formatters[Kr],indices:!1,serializeDate:function(e){return gi.call(e)},skipNulls:!1,strictNullHandling:!1},Ti=function(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e=="symbol"||typeof e=="bigint"},Lt={},_i=function t(e,r,n,o,a,s,u,c,l,h,p,d,f,m,v,g){for(var y=e,_=g,E=0,O=!1;(_=_.get(Lt))!==void 0&&!O;){var D=_.get(e);if(E+=1,typeof D<"u"){if(D===E)throw new RangeError("Cyclic object value");O=!0}typeof _.get(Lt)>"u"&&(E=0)}if(typeof c=="function"?y=c(r,y):y instanceof Date?y=p(y):n==="comma"&&W(y)&&(y=Et.maybeMap(y,function(se){return se instanceof Date?p(se):se})),y===null){if(a)return u&&!m?u(r,$.encoder,v,"key",d):r;y=""}if(Ti(y)||Et.isBuffer(y)){if(u){var M=m?r:u(r,$.encoder,v,"key",d);return[f(M)+"="+f(u(y,$.encoder,v,"value",d))]}return[f(r)+"="+f(String(y))]}var b=[];if(typeof y>"u")return b;var G;if(n==="comma"&&W(y))m&&u&&(y=Et.maybeMap(y,u)),G=[{value:y.length>0?y.join(",")||null:void 0}];else if(W(c))G=c;else{var oe=Object.keys(y);G=l?oe.sort(l):oe}for(var ae=o&&W(y)&&y.length===1?r+"[]":r,Oe=0;Oe<G.length;++Oe){var N=G[Oe],B=typeof N=="object"&&typeof N.value<"u"?N.value:y[N];if(!(s&&B===null)){var ie=W(y)?typeof n=="function"?n(ae,N):ae:ae+(h?"."+N:"["+N+"]");g.set(e,E);var Xe=ro();Xe.set(Lt,g),no(b,t(B,ie,n,o,a,s,n==="comma"&&m&&W(y)?null:u,c,l,h,p,d,f,m,v,Xe))}}return b},Ei=function(e){if(!e)return $;if(e.encoder!==null&&typeof e.encoder<"u"&&typeof e.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=e.charset||$.charset;if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=Ke.default;if(typeof e.format<"u"){if(!vi.call(Ke.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var o=Ke.formatters[n],a=$.filter;return(typeof e.filter=="function"||W(e.filter))&&(a=e.filter),{addQueryPrefix:typeof e.addQueryPrefix=="boolean"?e.addQueryPrefix:$.addQueryPrefix,allowDots:typeof e.allowDots>"u"?$.allowDots:!!e.allowDots,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:$.charsetSentinel,delimiter:typeof e.delimiter>"u"?$.delimiter:e.delimiter,encode:typeof e.encode=="boolean"?e.encode:$.encode,encoder:typeof e.encoder=="function"?e.encoder:$.encoder,encodeValuesOnly:typeof e.encodeValuesOnly=="boolean"?e.encodeValuesOnly:$.encodeValuesOnly,filter:a,format:n,formatter:o,serializeDate:typeof e.serializeDate=="function"?e.serializeDate:$.serializeDate,skipNulls:typeof e.skipNulls=="boolean"?e.skipNulls:$.skipNulls,sort:typeof e.sort=="function"?e.sort:null,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:$.strictNullHandling}},Si=function(t,e){var r=t,n=Ei(e),o,a;typeof n.filter=="function"?(a=n.filter,r=a("",r)):W(n.filter)&&(a=n.filter,o=a);var s=[];if(typeof r!="object"||r===null)return"";var u;e&&e.arrayFormat in Vr?u=e.arrayFormat:e&&"indices"in e?u=e.indices?"indices":"repeat":u="indices";var c=Vr[u];if(e&&"commaRoundTrip"in e&&typeof e.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var l=c==="comma"&&e&&e.commaRoundTrip;o||(o=Object.keys(r)),n.sort&&o.sort(n.sort);for(var h=ro(),p=0;p<o.length;++p){var d=o[p];n.skipNulls&&r[d]===null||no(s,_i(r[d],d,c,l,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,h))}var f=s.join(n.delimiter),m=n.addQueryPrefix===!0?"?":"";return n.charsetSentinel&&(n.charset==="iso-8859-1"?m+="utf8=%26%2310003%3B&":m+="utf8=%E2%9C%93&"),f.length>0?m+f:""},Ee=to,ur=Object.prototype.hasOwnProperty,bi=Array.isArray,w={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:Ee.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},Oi=function(t){return t.replace(/&#(\d+);/g,function(e,r){return String.fromCharCode(parseInt(r,10))})},oo=function(t,e){return t&&typeof t=="string"&&e.comma&&t.indexOf(",")>-1?t.split(","):t},wi="utf8=%26%2310003%3B",xi="utf8=%E2%9C%93",Ai=function(e,r){var n={__proto__:null},o=r.ignoreQueryPrefix?e.replace(/^\?/,""):e,a=r.parameterLimit===1/0?void 0:r.parameterLimit,s=o.split(r.delimiter,a),u=-1,c,l=r.charset;if(r.charsetSentinel)for(c=0;c<s.length;++c)s[c].indexOf("utf8=")===0&&(s[c]===xi?l="utf-8":s[c]===wi&&(l="iso-8859-1"),u=c,c=s.length);for(c=0;c<s.length;++c)if(c!==u){var h=s[c],p=h.indexOf("]="),d=p===-1?h.indexOf("="):p+1,f,m;d===-1?(f=r.decoder(h,w.decoder,l,"key"),m=r.strictNullHandling?null:""):(f=r.decoder(h.slice(0,d),w.decoder,l,"key"),m=Ee.maybeMap(oo(h.slice(d+1),r),function(v){return r.decoder(v,w.decoder,l,"value")})),m&&r.interpretNumericEntities&&l==="iso-8859-1"&&(m=Oi(m)),h.indexOf("[]=")>-1&&(m=bi(m)?[m]:m),ur.call(n,f)?n[f]=Ee.combine(n[f],m):n[f]=m}return n},$i=function(t,e,r,n){for(var o=n?e:oo(e,r),a=t.length-1;a>=0;--a){var s,u=t[a];if(u==="[]"&&r.parseArrays)s=[].concat(o);else{s=r.plainObjects?Object.create(null):{};var c=u.charAt(0)==="["&&u.charAt(u.length-1)==="]"?u.slice(1,-1):u,l=parseInt(c,10);!r.parseArrays&&c===""?s={0:o}:!isNaN(l)&&u!==c&&String(l)===c&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(s=[],s[l]=o):c!=="__proto__"&&(s[c]=o)}o=s}return o},Ci=function(e,r,n,o){if(e){var a=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,s=/(\[[^[\]]*])/,u=/(\[[^[\]]*])/g,c=n.depth>0&&s.exec(a),l=c?a.slice(0,c.index):a,h=[];if(l){if(!n.plainObjects&&ur.call(Object.prototype,l)&&!n.allowPrototypes)return;h.push(l)}for(var p=0;n.depth>0&&(c=u.exec(a))!==null&&p<n.depth;){if(p+=1,!n.plainObjects&&ur.call(Object.prototype,c[1].slice(1,-1))&&!n.allowPrototypes)return;h.push(c[1])}return c&&h.push("["+a.slice(c.index)+"]"),$i(h,r,n,o)}},Ri=function(e){if(!e)return w;if(e.decoder!==null&&e.decoder!==void 0&&typeof e.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof e.charset>"u"?w.charset:e.charset;return{allowDots:typeof e.allowDots>"u"?w.allowDots:!!e.allowDots,allowPrototypes:typeof e.allowPrototypes=="boolean"?e.allowPrototypes:w.allowPrototypes,allowSparse:typeof e.allowSparse=="boolean"?e.allowSparse:w.allowSparse,arrayLimit:typeof e.arrayLimit=="number"?e.arrayLimit:w.arrayLimit,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:w.charsetSentinel,comma:typeof e.comma=="boolean"?e.comma:w.comma,decoder:typeof e.decoder=="function"?e.decoder:w.decoder,delimiter:typeof e.delimiter=="string"||Ee.isRegExp(e.delimiter)?e.delimiter:w.delimiter,depth:typeof e.depth=="number"||e.depth===!1?+e.depth:w.depth,ignoreQueryPrefix:e.ignoreQueryPrefix===!0,interpretNumericEntities:typeof e.interpretNumericEntities=="boolean"?e.interpretNumericEntities:w.interpretNumericEntities,parameterLimit:typeof e.parameterLimit=="number"?e.parameterLimit:w.parameterLimit,parseArrays:e.parseArrays!==!1,plainObjects:typeof e.plainObjects=="boolean"?e.plainObjects:w.plainObjects,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:w.strictNullHandling}},Ii=function(t,e){var r=Ri(e);if(t===""||t===null||typeof t>"u")return r.plainObjects?Object.create(null):{};for(var n=typeof t=="string"?Ai(t,r):t,o=r.plainObjects?Object.create(null):{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],c=Ci(u,n[u],r,typeof t=="string");o=Ee.merge(o,c,r)}return r.allowSparse===!0?o:Ee.compact(o)},Mi=Si,Gi=Ii,Di=_r,ki={formats:Di,parse:Gi,stringify:Mi};const Er=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host"];function ao(t){return t&&typeof t=="object"&&Er.some(e=>Object.prototype.hasOwnProperty.call(t,e))}function $t(t){return ki.stringify(t,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString()}).replace(/%5B/g,"[").replace(/%5D/g,"]")}const cr=(()=>{const t={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return e=>{const r=e.replace(/["\n\r\u2028\u2029]/g,n=>t[n]);return n=>r.replace(/\{([\s\S]+?)\}/g,(o,a)=>encodeURIComponent(n[a]||""))}})();function Ni(t){const e=t.match(/\{\w+\}/g);return e?e.map(r=>r.replace(/[{}]/g,"")):[]}function io(t){if(!Array.isArray(t)||!t[0]||typeof t[0]!="object")return{};if(!ao(t[0]))return t.shift();const e=Object.keys(t[0]),r=e.filter(n=>Er.includes(n));return r.length>0&&r.length!==e.length&&At(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function Fi(t){const e={auth:null,host:null,headers:{},settings:{}};if(t.length>0){const r=t[t.length-1];if(typeof r=="string")e.auth=t.pop();else if(ao(r)){const n=Object.assign({},t.pop()),o=Object.keys(n).filter(a=>!Er.includes(a));o.length&&At(`Invalid options found (${o.join(", ")}); ignoring.`),n.apiKey&&(e.auth=n.apiKey),n.idempotencyKey&&(e.headers["Idempotency-Key"]=n.idempotencyKey),n.stripeAccount&&(e.headers["Stripe-Account"]=n.stripeAccount),n.apiVersion&&(e.headers["Stripe-Version"]=n.apiVersion),Number.isInteger(n.maxNetworkRetries)&&(e.settings.maxNetworkRetries=n.maxNetworkRetries),Number.isInteger(n.timeout)&&(e.settings.timeout=n.timeout),n.host&&(e.host=n.host)}}return e}function Ui(t){const e=this,r=Object.prototype.hasOwnProperty.call(t,"constructor")?t.constructor:function(...n){e.apply(this,n)};return Object.assign(r,e),r.prototype=Object.create(e.prototype),Object.assign(r.prototype,t),r}function qt(t){if(typeof t!="object")throw new Error("Argument must be an object");return Object.keys(t).reduce((e,r)=>(t[r]!=null&&(e[r]=t[r]),e),{})}function Li(t){return t&&typeof t=="object"?Object.keys(t).reduce((e,r)=>(e[qi(r)]=t[r],e),{}):t}function qi(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")}function Sr(t,e){return e?t.then(r=>{setTimeout(()=>{e(null,r)},0)},r=>{setTimeout(()=>{e(r,null)},0)}):t}function Hi(t){return t==="OAuth"?"oauth":t[0].toLowerCase()+t.substring(1)}function At(t){return typeof process.emitWarning!="function"?console.warn(`Stripe: ${t}`):process.emitWarning(t,"Stripe")}function Bi(t){const e=typeof t;return(e==="function"||e==="object")&&!!t}function Wi(t){const e={},r=(n,o)=>{Object.keys(n).forEach(a=>{const s=n[a],u=o?`${o}[${a}]`:a;if(Bi(s)){if(!(s instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(s,"data"))return r(s,u);e[u]=s}else e[u]=String(s)})};return r(t,null),e}function Ht(t,e,r){if(!Number.isInteger(e)){if(r!==void 0)return r;throw new Error(`${t} must be an integer`)}return e}function zi(){return typeof process>"u"?{}:{lang_version:process.version,platform:process.platform}}class so{constructor(e,r,n,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=r,this.spec=n,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&typeof e.data.length=="number"))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");const r=lo(this.requestArgs);if(this.index<e.data.length){const n=r?e.data.length-1-this.index:this.index,o=e.data[n];return this.index+=1,{value:o,done:!1}}else if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);const n=await this.pagePromise;return this.iterate(n)}return{done:!0,value:void 0}}getNextPage(e){throw new Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;const e=(async()=>{const r=await this._next();return this.promiseCache.currentPromise=null,r})();return this.promiseCache.currentPromise=e,e}}class ji extends so{getNextPage(e){const r=lo(this.requestArgs),n=Xi(e,r);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[r?"ending_before":"starting_after"]:n})}}class Vi extends so{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}const Ki=(t,e,r,n)=>r.methodType==="search"?Qr(new Vi(n,e,r,t)):r.methodType==="list"?Qr(new ji(n,e,r,t)):null,Qr=t=>{const e=Zi((...o)=>t.next(...o)),r=es(e),n={autoPagingEach:e,autoPagingToArray:r,next:()=>t.next(),return:()=>({}),[Qi()]:()=>n};return n};function Qi(){return typeof Symbol<"u"&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"}function Ji(t){if(t.length<2)return null;const e=t[1];if(typeof e!="function")throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof e}`);return e}function Yi(t){if(t.length===0)return;const e=t[0];if(typeof e!="function")throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof e}`);if(e.length===2)return e;if(e.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${e}`);return function(n,o){const a=e(n);o(a)}}function Xi(t,e){const r=e?0:t.data.length-1,n=t.data[r],o=n&&n.id;if(!o)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return o}function Zi(t){return function(){const r=[].slice.call(arguments),n=Yi(r),o=Ji(r);if(r.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${r}`);const a=ts(t,n);return Sr(a,o)}}function es(t){return function(r,n){const o=r&&r.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");const a=new Promise((s,u)=>{const c=[];t(l=>{if(c.push(l),c.length>=o)return!1}).then(()=>{s(c)}).catch(u)});return Sr(a,n)}}function ts(t,e){return new Promise((r,n)=>{function o(a){if(a.done){r();return}const s=a.value;return new Promise(u=>{e(s,u)}).then(u=>u===!1?o({done:!0,value:void 0}):t().then(o))}t().then(o).catch(n)})}function lo(t){const e=[].slice.call(t);return!!io(e).ending_before}function rs(t){if(t.path!==void 0&&t.fullPath!==void 0)throw new Error(`Method spec specified both a 'path' (${t.path}) and a 'fullPath' (${t.fullPath}).`);return function(...e){const r=typeof e[e.length-1]=="function"&&e.pop();t.urlParams=Ni(t.fullPath||this.createResourcePathWithSymbols(t.path||""));const n=Sr(this._makeRequest(e,t,{}),r);return Object.assign(n,Ki(this,e,t,n)),n}}i.extend=Ui;i.method=rs;i.MAX_BUFFERED_REQUEST_METRICS=100;function i(t,e){if(this._stripe=t,e)throw new Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=cr(this.basePath||t.getApiField("basePath")),this.resourcePath=this.path,this.path=cr(this.path),this.initialize(...arguments)}i.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(t,e){const r=[this.basePath(e),this.path(e)];if(typeof t=="function"){const n=t(e);n&&r.push(n)}else r.push(t);return this._joinUrlParts(r)},createResourcePathWithSymbols(t){return t?`/${this._joinUrlParts([this.resourcePath,t])}`:`/${this.resourcePath}`},_joinUrlParts(t){return t.join("/").replace(/\/{2,}/g,"/")},_getRequestOpts(t,e,r){const n=(e.method||"GET").toUpperCase(),o=e.usage||[],a=e.urlParams||[],s=e.encode||(M=>M),u=!!e.fullPath,c=cr(u?e.fullPath:e.path||""),l=u?e.fullPath:this.createResourcePathWithSymbols(e.path),h=[].slice.call(t),p=a.reduce((M,b)=>{const G=h.shift();if(typeof G!="string")throw new Error(`Stripe: Argument "${b}" must be a string, but got: ${G} (on API request to \`${n} ${l}\`)`);return M[b]=G,M},{}),d=io(h),f=s(Object.assign({},d,r)),m=Fi(h),v=m.host||e.host,g=!!e.streaming;if(h.filter(M=>M!=null).length)throw new Error(`Stripe: Unknown arguments (${h}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${n} \`${l}\`)`);const y=u?c(p):this.createFullPath(c,p),_=Object.assign(m.headers,e.headers);e.validator&&e.validator(f,{headers:_});const E=e.method==="GET"||e.method==="DELETE";return{requestMethod:n,requestPath:y,bodyData:E?{}:f,queryData:E?f:{},auth:m.auth,headers:_,host:v??null,streaming:g,settings:m.settings,usage:o}},_makeRequest(t,e,r){return new Promise((n,o)=>{var a;let s;try{s=this._getRequestOpts(t,e,r)}catch(d){o(d);return}function u(d,f){d?o(d):n(e.transformResponseData?e.transformResponseData(f):f)}const c=Object.keys(s.queryData).length===0,l=[s.requestPath,c?"":"?",$t(s.queryData)].join(""),{headers:h,settings:p}=s;this._stripe._requestSender._request(s.requestMethod,s.host,l,s.bodyData,s.auth,{headers:h,settings:p,streaming:s.streaming},s.usage,u,(a=this.requestDataProcessor)===null||a===void 0?void 0:a.bind(this))})}};const X=i.method,ns=i.extend({retrieve:X({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:X({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:X({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),xe=i.method,os=i.extend({create:xe({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:xe({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:xe({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),increment:xe({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),reverse:xe({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),Ae=i.method,as=i.extend({retrieve:Ae({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:Ae({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:Ae({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:Ae({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:Ae({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),Jr=i.method,is=i.extend({create:Jr({method:"POST",fullPath:"/v1/tax/calculations"}),listLineItems:Jr({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),rt=i.method,ss=i.extend({create:rt({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:rt({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:rt({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:rt({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),nt=i.method,ls=i.extend({deliverCard:nt({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:nt({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:nt({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:nt({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"})}),ot=i.method,us=i.extend({create:ot({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:ot({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:ot({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:ot({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),at=i.method,cs=i.extend({create:at({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:at({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:at({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:at({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),$e=i.method,hs=i.extend({create:$e({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:$e({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:$e({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:$e({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:$e({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),ds=i.method,ps=i.extend({create:ds({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),Bt=i.method,fs=i.extend({create:Bt({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:Bt({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:Bt({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),ms=i.method,ys=i.extend({fundCashBalance:ms({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),Wt=i.method,vs=i.extend({create:Wt({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:Wt({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:Wt({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),Ce=i.method,Ps=i.extend({create:Ce({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:Ce({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:Ce({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:Ce({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:Ce({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),Yr=i.method,gs=i.extend({retrieve:Yr({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:Yr({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),ce=i.method,Ts=i.extend({create:ce({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:ce({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:ce({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:ce({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),retrieveFeatures:ce({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:ce({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),zt=i.method,_s=i.extend({fail:zt({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:zt({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:zt({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),it=i.method,Es=i.extend({create:it({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:it({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:it({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:it({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),Re=i.method,Ss=i.extend({create:Re({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:Re({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:Re({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:Re({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:Re({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),Ie=i.method,bs=i.extend({create:Ie({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:Ie({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:Ie({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:Ie({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:Ie({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),jt=i.method,Os=i.extend({fail:jt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:jt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:jt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),st=i.method,ws=i.extend({create:st({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:st({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:st({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:st({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),Vt=i.method,xs=i.extend({fail:Vt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:Vt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:Vt({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),lt=i.method,As=i.extend({create:lt({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:lt({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:lt({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:lt({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),Xr=i.method,$s=i.extend({retrieve:Xr({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:Xr({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),Cs=i.method,Rs=i.extend({presentPaymentMethod:Cs({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),L=i.method,Is=i.extend({create:L({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:L({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:L({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:L({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:L({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),Ms=i.method,Gs=i.extend({create:Ms({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),Zr=i.method,Ds=i.extend({retrieve:Zr({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:Zr({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),ks=i.method,Ns=i.extend({create:ks({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),en=i.method,Fs=i.extend({retrieve:en({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:en({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),Us=i.method,Ls=i.extend({expire:Us({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),ut=i.method,qs=i.extend({create:ut({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:ut({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:ut({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:ut({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),Kt=i.method,Hs=i.extend({create:Kt({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:Kt({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:Kt({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),tn=i.method,Bs=i.extend({retrieve:tn({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:tn({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),rn=i.method,Ws=i.extend({retrieve:rn({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:rn({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),ct=i.method,zs=i.extend({create:ct({method:"POST",fullPath:"/v1/apps/secrets"}),list:ct({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:ct({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:ct({method:"GET",fullPath:"/v1/apps/secrets/find"})}),js=i.method,Vs=i.extend({create:js({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),Me=i.method,Ks=i.extend({create:Me({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:Me({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),list:Me({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:Me({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:Me({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),nn=i.method,Qs=i.extend({create:nn({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:nn({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),on=i.method,Js=i.extend({retrieve:on({method:"GET",fullPath:"/v1/tax/settings"}),update:on({method:"POST",fullPath:"/v1/tax/settings"})}),an=i.method,Ys=i.extend({retrieve:an({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:an({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),Ge=i.method,Xs=i.extend({create:Ge({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:Ge({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:Ge({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:Ge({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:Ge({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),Qt=i.method,Zs=i.extend({retrieve:Qt({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:Qt({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:Qt({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),sn=i.method,el=i.extend({retrieve:sn({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:sn({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),Jt=i.method,tl=i.extend({createForceCapture:Jt({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:Jt({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:Jt({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),ln=i.method,rl=i.extend({retrieve:ln({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:ln({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),Yt=i.method,nl=i.extend({retrieve:Yt({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:Yt({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:Yt({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),ht=i.method,ol=i.extend({retrieve:ht({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:ht({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:ht({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:ht({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),un=i.method,al=i.extend({retrieve:un({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:un({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),dt=i.method,il=i.extend({create:dt({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:dt({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:dt({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:dt({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),De=i.method,sl=i.extend({create:De({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:De({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:De({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:De({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:De({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),cn=i.method,ll=i.extend({retrieve:cn({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:cn({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),he=i.method,ul=i.extend({create:he({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:he({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:he({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:he({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:he({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:he({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),S=i.method,hn=i.extend({create:S({method:"POST",fullPath:"/v1/accounts"}),retrieve(t,...e){return typeof t=="string"?S({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[t,...e]):(t==null&&[].shift.apply([t,...e]),S({method:"GET",fullPath:"/v1/account"}).apply(this,[t,...e]))},update:S({method:"POST",fullPath:"/v1/accounts/{account}"}),list:S({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:S({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:S({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:S({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:S({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:S({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:S({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:S({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:S({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:S({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:S({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:S({method:"GET",fullPath:"/v1/account"}),retrieveCapability:S({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:S({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:S({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:S({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:S({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:S({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),cl=i.method,hl=i.extend({create:cl({method:"POST",fullPath:"/v1/account_links"})}),dl=i.method,pl=i.extend({create:dl({method:"POST",fullPath:"/v1/account_sessions"})}),pt=i.method,fl=i.extend({create:pt({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:pt({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:pt({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:pt({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),de=i.method,ml=i.extend({retrieve:de({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:de({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:de({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:de({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:de({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:de({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),yl=i.method,vl=i.extend({retrieve:yl({method:"GET",fullPath:"/v1/balance"})}),dn=i.method,Pl=i.extend({retrieve:dn({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:dn({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),pe=i.method,gl=i.extend({create:pe({method:"POST",fullPath:"/v1/charges"}),retrieve:pe({method:"GET",fullPath:"/v1/charges/{charge}"}),update:pe({method:"POST",fullPath:"/v1/charges/{charge}"}),list:pe({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:pe({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:pe({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),pn=i.method,Tl=i.extend({retrieve:pn({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:pn({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),ke=i.method,_l=i.extend({create:ke({method:"POST",fullPath:"/v1/coupons"}),retrieve:ke({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:ke({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:ke({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:ke({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),z=i.method,El=i.extend({create:z({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:z({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:z({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:z({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:z({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:z({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:z({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:z({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),Sl=i.method,bl=i.extend({create:Sl({method:"POST",fullPath:"/v1/customer_sessions"})}),T=i.method,Ol=i.extend({create:T({method:"POST",fullPath:"/v1/customers"}),retrieve:T({method:"GET",fullPath:"/v1/customers/{customer}"}),update:T({method:"POST",fullPath:"/v1/customers/{customer}"}),list:T({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:T({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:T({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:T({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:T({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:T({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:T({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:T({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:T({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:T({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:T({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:T({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:T({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:T({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:T({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:T({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:T({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:T({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:T({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:T({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:T({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:T({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:T({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:T({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:T({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),ft=i.method,wl=i.extend({retrieve:ft({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:ft({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:ft({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:ft({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),fn=i.method,xl=i.extend({create:fn({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(t,e)=>{if(!e.headers||!e.headers["Stripe-Version"])throw new Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:fn({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),mn=i.method,Al=i.extend({retrieve:mn({method:"GET",fullPath:"/v1/events/{id}"}),list:mn({method:"GET",fullPath:"/v1/events",methodType:"list"})}),yn=i.method,$l=i.extend({retrieve:yn({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:yn({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),mt=i.method,Cl=i.extend({create:mt({method:"POST",fullPath:"/v1/file_links"}),retrieve:mt({method:"GET",fullPath:"/v1/file_links/{link}"}),update:mt({method:"POST",fullPath:"/v1/file_links/{link}"}),list:mt({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),Rl=(t,e,r)=>{const n=(Math.round(Math.random()*1e16)+Math.round(Math.random()*1e16)).toString();r["Content-Type"]=`multipart/form-data; boundary=${n}`;const o=new TextEncoder;let a=new Uint8Array(0);const s=o.encode(`\r
`);function u(h){const p=a,d=h instanceof Uint8Array?h:new Uint8Array(o.encode(h));a=new Uint8Array(p.length+d.length+2),a.set(p),a.set(d,p.length),a.set(s,a.length-2)}function c(h){return`"${h.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}const l=Wi(e);for(const h in l){const p=l[h];if(u(`--${n}`),Object.prototype.hasOwnProperty.call(p,"data")){const d=p;u(`Content-Disposition: form-data; name=${c(h)}; filename=${c(d.name||"blob")}`),u(`Content-Type: ${d.type||"application/octet-stream"}`),u(""),u(d.data)}else u(`Content-Disposition: form-data; name=${c(h)}`),u(""),u(p)}return u(`--${n}--`),a};function Il(t,e,r,n){if(e=e||{},t!=="POST")return n(null,$t(e));this._stripe._platformFunctions.tryBufferData(e).then(o=>{const a=Rl(t,o,r);return n(null,a)}).catch(o=>n(o,null))}const Xt=i.method,Ml=i.extend({create:Xt({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:Xt({method:"GET",fullPath:"/v1/files/{file}"}),list:Xt({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:Il}),Ne=i.method,Gl=i.extend({create:Ne({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:Ne({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:Ne({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:Ne({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:Ne({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),R=i.method,Dl=i.extend({create:R({method:"POST",fullPath:"/v1/invoices"}),retrieve:R({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:R({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:R({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:R({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),finalizeInvoice:R({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:R({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),listUpcomingLines:R({method:"GET",fullPath:"/v1/invoices/upcoming/lines",methodType:"list"}),markUncollectible:R({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:R({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),retrieveUpcoming:R({method:"GET",fullPath:"/v1/invoices/upcoming"}),search:R({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:R({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLineItem:R({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:R({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),kl=i.method,Nl=i.extend({retrieve:kl({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),vn=i.method,Zt="connect.stripe.com",Fl=i.extend({basePath:"/",authorizeUrl(t,e){t=t||{},e=e||{};let r="oauth/authorize";return e.express&&(r=`express/${r}`),t.response_type||(t.response_type="code"),t.client_id||(t.client_id=this._stripe.getClientId()),t.scope||(t.scope="read_write"),`https://${Zt}/${r}?${$t(t)}`},token:vn({method:"POST",path:"oauth/token",host:Zt}),deauthorize(t,...e){return t.client_id||(t.client_id=this._stripe.getClientId()),vn({method:"POST",path:"oauth/deauthorize",host:Zt}).apply(this,[t,...e])}}),F=i.method,Ul=i.extend({create:F({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:F({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:F({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:F({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:F({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:F({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),Fe=i.method,Ll=i.extend({create:Fe({method:"POST",fullPath:"/v1/payment_links"}),retrieve:Fe({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:Fe({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:Fe({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:Fe({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),yt=i.method,ql=i.extend({create:yt({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:yt({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:yt({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:yt({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),Ue=i.method,Hl=i.extend({create:Ue({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:Ue({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:Ue({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:Ue({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:Ue({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),fe=i.method,Bl=i.extend({create:fe({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:fe({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:fe({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:fe({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:fe({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:fe({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),me=i.method,Wl=i.extend({create:me({method:"POST",fullPath:"/v1/payouts"}),retrieve:me({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:me({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:me({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:me({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:me({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),Le=i.method,zl=i.extend({create:Le({method:"POST",fullPath:"/v1/plans"}),retrieve:Le({method:"GET",fullPath:"/v1/plans/{plan}"}),update:Le({method:"POST",fullPath:"/v1/plans/{plan}"}),list:Le({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:Le({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),qe=i.method,jl=i.extend({create:qe({method:"POST",fullPath:"/v1/prices"}),retrieve:qe({method:"GET",fullPath:"/v1/prices/{price}"}),update:qe({method:"POST",fullPath:"/v1/prices/{price}"}),list:qe({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:qe({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),ye=i.method,Vl=i.extend({create:ye({method:"POST",fullPath:"/v1/products"}),retrieve:ye({method:"GET",fullPath:"/v1/products/{id}"}),update:ye({method:"POST",fullPath:"/v1/products/{id}"}),list:ye({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:ye({method:"DELETE",fullPath:"/v1/products/{id}"}),search:ye({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),vt=i.method,Kl=i.extend({create:vt({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:vt({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:vt({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:vt({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),q=i.method,Ql=i.extend({create:q({method:"POST",fullPath:"/v1/quotes"}),retrieve:q({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:q({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:q({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:q({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:q({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:q({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:q({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:q({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:q({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),He=i.method,Jl=i.extend({create:He({method:"POST",fullPath:"/v1/refunds"}),retrieve:He({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:He({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:He({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:He({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),er=i.method,Yl=i.extend({retrieve:er({method:"GET",fullPath:"/v1/reviews/{review}"}),list:er({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:er({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),Xl=i.method,Zl=i.extend({list:Xl({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),Z=i.method,eu=i.extend({create:Z({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:Z({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:Z({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:Z({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:Z({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:Z({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:Z({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),Pt=i.method,tu=i.extend({create:Pt({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:Pt({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:Pt({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:Pt({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),Be=i.method,ru=i.extend({create:Be({method:"POST",fullPath:"/v1/sources"}),retrieve:Be({method:"GET",fullPath:"/v1/sources/{source}"}),update:Be({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:Be({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:Be({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),ee=i.method,nu=i.extend({create:ee({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:ee({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:ee({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:ee({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:ee({method:"DELETE",fullPath:"/v1/subscription_items/{item}"}),createUsageRecord:ee({method:"POST",fullPath:"/v1/subscription_items/{subscription_item}/usage_records"}),listUsageRecordSummaries:ee({method:"GET",fullPath:"/v1/subscription_items/{subscription_item}/usage_record_summaries",methodType:"list"})}),ve=i.method,ou=i.extend({create:ve({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:ve({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:ve({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:ve({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:ve({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:ve({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),j=i.method,au=i.extend({create:j({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:j({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:j({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:j({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:j({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:j({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:j({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:j({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),Pn=i.method,iu=i.extend({retrieve:Pn({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:Pn({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),gt=i.method,su=i.extend({create:gt({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:gt({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:gt({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:gt({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),Tt=i.method,lu=i.extend({create:Tt({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:Tt({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:Tt({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:Tt({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),gn=i.method,uu=i.extend({create:gn({method:"POST",fullPath:"/v1/tokens"}),retrieve:gn({method:"GET",fullPath:"/v1/tokens/{token}"})}),We=i.method,cu=i.extend({create:We({method:"POST",fullPath:"/v1/topups"}),retrieve:We({method:"GET",fullPath:"/v1/topups/{topup}"}),update:We({method:"POST",fullPath:"/v1/topups/{topup}"}),list:We({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:We({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),V=i.method,hu=i.extend({create:V({method:"POST",fullPath:"/v1/transfers"}),retrieve:V({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:V({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:V({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:V({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:V({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:V({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:V({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),ze=i.method,du=i.extend({create:ze({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:ze({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:ze({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:ze({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:ze({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),pu=A("apps",{Secrets:zs}),fu=A("billingPortal",{Configurations:cs,Sessions:Vs}),mu=A("checkout",{Sessions:Ks}),yu=A("climate",{Orders:bs,Products:$s,Suppliers:Ys}),vu=A("financialConnections",{Accounts:ns,Sessions:Qs,Transactions:rl}),Pu=A("identity",{VerificationReports:ll,VerificationSessions:ul}),gu=A("issuing",{Authorizations:as,Cardholders:ss,Cards:us,Disputes:Ps,Tokens:Zs,Transactions:nl}),Tu=A("radar",{EarlyFraudWarnings:gs,ValueListItems:il,ValueLists:sl}),_u=A("reporting",{ReportRuns:Hs,ReportTypes:Bs}),Eu=A("sigma",{ScheduledQueryRuns:Ws}),Su=A("tax",{Calculations:is,Registrations:qs,Settings:Js,Transactions:ol}),bu=A("terminal",{Configurations:hs,ConnectionTokens:ps,Locations:Ss,Readers:Is}),Ou=A("testHelpers",{Customers:ys,Refunds:Ls,TestClocks:Xs,Issuing:A("issuing",{Authorizations:os,Cards:ls,Transactions:tl}),Terminal:A("terminal",{Readers:Rs}),Treasury:A("treasury",{InboundTransfers:_s,OutboundPayments:Os,OutboundTransfers:xs,ReceivedCredits:Gs,ReceivedDebits:Ns})}),wu=A("treasury",{CreditReversals:fs,DebitReversals:vs,FinancialAccounts:Ts,InboundTransfers:Es,OutboundPayments:ws,OutboundTransfers:As,ReceivedCredits:Ds,ReceivedDebits:Fs,TransactionEntries:el,Transactions:al}),tr=Object.freeze(Object.defineProperty({__proto__:null,Account:hn,AccountLinks:hl,AccountSessions:pl,Accounts:hn,ApplePayDomains:fl,ApplicationFees:ml,Apps:pu,Balance:vl,BalanceTransactions:Pl,BillingPortal:fu,Charges:gl,Checkout:mu,Climate:yu,CountrySpecs:Tl,Coupons:_l,CreditNotes:El,CustomerSessions:bl,Customers:Ol,Disputes:wl,EphemeralKeys:xl,Events:Al,ExchangeRates:$l,FileLinks:Cl,Files:Ml,FinancialConnections:vu,Identity:Pu,InvoiceItems:Gl,Invoices:Dl,Issuing:gu,Mandates:Nl,OAuth:Fl,PaymentIntents:Ul,PaymentLinks:Ll,PaymentMethodConfigurations:ql,PaymentMethodDomains:Hl,PaymentMethods:Bl,Payouts:Wl,Plans:zl,Prices:jl,Products:Vl,PromotionCodes:Kl,Quotes:Ql,Radar:Tu,Refunds:Jl,Reporting:_u,Reviews:Yl,SetupAttempts:Zl,SetupIntents:eu,ShippingRates:tu,Sigma:Eu,Sources:ru,SubscriptionItems:nu,SubscriptionSchedules:ou,Subscriptions:au,Tax:Su,TaxCodes:iu,TaxIds:su,TaxRates:lu,Terminal:bu,TestHelpers:Ou,Tokens:uu,Topups:cu,Transfers:hu,Treasury:wu,WebhookEndpoints:du},Symbol.toStringTag,{value:"Module"})),xu=60;class Qe{constructor(e,r){this._stripe=e,this._maxBufferedRequestMetric=r}_addHeadersDirectlyToObject(e,r){e.requestId=r["request-id"],e.stripeAccount=e.stripeAccount||r["stripe-account"],e.apiVersion=e.apiVersion||r["stripe-version"],e.idempotencyKey=e.idempotencyKey||r["idempotency-key"]}_makeResponseEvent(e,r,n){const o=Date.now(),a=o-e.request_start_time;return qt({api_version:n["stripe-version"],account:n["stripe-account"],idempotency_key:n["idempotency-key"],method:e.method,path:e.path,status:r,request_id:this._getRequestId(n),elapsed:a,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,r,n){return o=>{const a=o.getHeaders(),s=()=>{const c=this._makeResponseEvent(e,o.getStatusCode(),a);this._stripe._emitter.emit("response",c),this._recordRequestMetrics(this._getRequestId(a),c.elapsed,r)},u=o.toStream(s);return this._addHeadersDirectlyToObject(u,a),n(null,u)}}_jsonResponseHandler(e,r,n){return o=>{const a=o.getHeaders(),s=this._getRequestId(a),u=o.getStatusCode(),c=this._makeResponseEvent(e,u,a);this._stripe._emitter.emit("response",c),o.toJSON().then(l=>{if(l.error){let h;throw typeof l.error=="string"&&(l.error={type:l.error,message:l.error_description}),l.error.headers=a,l.error.statusCode=u,l.error.requestId=s,u===401?h=new pr(l.error):u===403?h=new Dn(l.error):u===429?h=new fr(l.error):h=I.generate(l.error),h}return l},l=>{throw new dr({message:"Invalid JSON received from the Stripe API",exception:l,requestId:a["request-id"]})}).then(l=>{this._recordRequestMetrics(s,c.elapsed,r);const h=o.getRawResponse();this._addHeadersDirectlyToObject(h,a),Object.defineProperty(l,"lastResponse",{enumerable:!1,writable:!1,value:h}),n(null,l)},l=>n(l,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,r,n,o){return o&&r===0&&k.CONNECTION_CLOSED_ERROR_CODES.includes(o.code)?!0:r>=n?!1:e?e.getHeaders()["stripe-should-retry"]==="false"?!1:e.getHeaders()["stripe-should-retry"]==="true"||e.getStatusCode()===409||e.getStatusCode()>=500:!0}_getSleepTimeInMS(e,r=null){const n=this._stripe.getInitialNetworkRetryDelay(),o=this._stripe.getMaxNetworkRetryDelay();let a=Math.min(n*Math.pow(e-1,2),o);return a*=.5*(1+Math.random()),a=Math.max(n,a),Number.isInteger(r)&&r<=xu&&(a=Math.max(a,r)),a*1e3}_getMaxNetworkRetries(e={}){return e.maxNetworkRetries!==void 0&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,r){const n=this._getMaxNetworkRetries(r);return e==="POST"&&n>0?`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`:null}_makeHeaders(e,r,n,o,a,s,u){const c={Authorization:e?`Bearer ${e}`:this._stripe.getApiField("auth"),Accept:"application/json","Content-Type":"application/x-www-form-urlencoded","User-Agent":this._getUserAgentString(),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":n,"Stripe-Account":this._stripe.getApiField("stripeAccount"),"Idempotency-Key":this._defaultIdempotencyKey(a,u)},l=a=="POST"||a=="PUT"||a=="PATCH";return(l||r)&&(l||At(`${a} method had non-zero contentLength but no payload is expected for this verb`),c["Content-Length"]=r),Object.assign(qt(c),Li(s))}_getUserAgentString(){const e=this._stripe.getConstant("PACKAGE_VERSION"),r=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/v1 NodeBindings/${e} ${r}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0){const e=this._stripe._prevRequestMetrics.shift();return JSON.stringify({last_request_metrics:e})}}_recordRequestMetrics(e,r,n){if(this._stripe.getTelemetryEnabled()&&e)if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)At("Request metrics buffer is full, dropping telemetry message.");else{const o={request_id:e,request_duration_ms:r};n&&n.length>0&&(o.usage=n),this._stripe._prevRequestMetrics.push(o)}}_request(e,r,n,o,a,s={},u=[],c,l=null){let h;const p=(m,v,g,y,_)=>setTimeout(m,this._getSleepTimeInMS(y,_),v,g,y+1),d=(m,v,g)=>{const y=s.settings&&s.settings.timeout&&Number.isInteger(s.settings.timeout)&&s.settings.timeout>=0?s.settings.timeout:this._stripe.getApiField("timeout"),_=this._stripe.getApiField("httpClient").makeRequest(r||this._stripe.getApiField("host"),this._stripe.getApiField("port"),n,e,v,h,this._stripe.getApiField("protocol"),y),E=Date.now(),O=qt({api_version:m,account:v["Stripe-Account"],idempotency_key:v["Idempotency-Key"],method:e,path:n,request_start_time:E}),D=g||0,M=this._getMaxNetworkRetries(s.settings||{});this._stripe._emitter.emit("request",O),_.then(b=>Qe._shouldRetry(b,D,M)?p(d,m,v,D,b.getHeaders()["retry-after"]):s.streaming&&b.getStatusCode()<400?this._streamingResponseHandler(O,u,c)(b):this._jsonResponseHandler(O,u,c)(b)).catch(b=>{if(Qe._shouldRetry(null,D,M,b))return p(d,m,v,D,null);{const G=b.code&&b.code===k.TIMEOUT_ERROR_CODE;return c(new kn({message:G?`Request aborted due to timeout being reached (${y}ms)`:Qe._generateConnectionErrorMessage(D),detail:b}))}})},f=(m,v)=>{if(m)return c(m);h=v,this._stripe.getClientUserAgent(g=>{var y,_;const E=this._stripe.getApiField("version"),O=this._makeHeaders(a,h.length,E,g,e,(y=s.headers)!==null&&y!==void 0?y:null,(_=s.settings)!==null&&_!==void 0?_:{});d(E,O,0)})};l?l(e,o,s.headers,f):f(null,$t(o||{}))}}function Tn(t){const e={DEFAULT_TOLERANCE:300,signature:null,constructEvent(l,h,p,d,f,m){try{this.signature.verifyHeader(l,h,p,d||e.DEFAULT_TOLERANCE,f,m)}catch(g){throw g instanceof Rn&&(g.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),g}return l instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(l)):JSON.parse(l)},async constructEventAsync(l,h,p,d,f,m){return await this.signature.verifyHeaderAsync(l,h,p,d||e.DEFAULT_TOLERANCE,f,m),l instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(l)):JSON.parse(l)},generateTestHeaderString:function(l){if(!l)throw new I({message:"Options are required"});return l.timestamp=Math.floor(l.timestamp)||Math.floor(Date.now()/1e3),l.scheme=l.scheme||r.EXPECTED_SCHEME,l.cryptoProvider=l.cryptoProvider||c(),l.signature=l.signature||l.cryptoProvider.computeHMACSignature(l.timestamp+"."+l.payload,l.secret),["t="+l.timestamp,l.scheme+"="+l.signature].join(",")}},r={EXPECTED_SCHEME:"v1",verifyHeader(l,h,p,d,f,m){const{decodedHeader:v,decodedPayload:g,details:y,suspectPayloadType:_}=o(l,h,this.EXPECTED_SCHEME),E=/\s/.test(p);f=f||c();const O=f.computeHMACSignature(n(g,y),p);return a(g,v,y,O,d,_,E,m),!0},async verifyHeaderAsync(l,h,p,d,f,m){const{decodedHeader:v,decodedPayload:g,details:y,suspectPayloadType:_}=o(l,h,this.EXPECTED_SCHEME),E=/\s/.test(p);f=f||c();const O=await f.computeHMACSignatureAsync(n(g,y),p);return a(g,v,y,O,d,_,E,m)}};function n(l,h){return`${h.timestamp}.${l}`}function o(l,h,p){if(!l)throw new K(h,l,{message:"No webhook payload was provided."});const d=typeof l!="string"&&!(l instanceof Uint8Array),f=new TextDecoder("utf8"),m=l instanceof Uint8Array?f.decode(l):l;if(Array.isArray(h))throw new Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(h==null||h=="")throw new K(h,l,{message:"No stripe-signature header value was provided."});const v=h instanceof Uint8Array?f.decode(h):h,g=s(v,p);if(!g||g.timestamp===-1)throw new K(v,m,{message:"Unable to extract timestamp and signatures from header"});if(!g.signatures.length)throw new K(v,m,{message:"No signatures found with expected scheme"});return{decodedPayload:m,decodedHeader:v,details:g,suspectPayloadType:d}}function a(l,h,p,d,f,m,v,g){const y=!!p.signatures.filter(t.secureCompare.bind(t,d)).length,_=`
Learn more about webhook signing and explore webhook integration examples for various frameworks at https://github.com/stripe/stripe-node#webhook-signing`,E=v?`

Note: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value`:"";if(!y)throw m?new K(h,l,{message:`Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. 
Signature verification is impossible without access to the original signed material. 
`+_+`
`+E}):new K(h,l,{message:`No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? 
 If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.
`+_+`
`+E});const O=Math.floor((typeof g=="number"?g:Date.now())/1e3)-p.timestamp;if(f>0&&O>f)throw new K(h,l,{message:"Timestamp outside the tolerance zone"});return!0}function s(l,h){return typeof l!="string"?null:l.split(",").reduce((p,d)=>{const f=d.split("=");return f[0]==="t"&&(p.timestamp=parseInt(f[1],10)),f[0]===h&&p.signatures.push(f[1]),p},{timestamp:-1,signatures:[]})}let u=null;function c(){return u||(u=t.createDefaultCryptoProvider()),u}return e.signature=r,e}const _n="api.stripe.com",En="443",Sn="/v1/",bn=vo,On=8e4,wn=2,xn=.5,Au=["name","version","url","partner_id"],An=["apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount"],$u=t=>new Qe(t,i.MAX_BUFFERED_REQUEST_METRICS);function Cu(t,e=$u){n.PACKAGE_VERSION="14.19.0",n.USER_AGENT=Object.assign({bindings_version:n.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},zi()),n.StripeResource=i,n.resources=tr,n.HttpClient=k,n.HttpClientResponse=$n,n.CryptoProvider=Cn;function r(o=t){return Tn(o)}n.webhooks=Object.assign(r,Tn(t));function n(o,a={}){if(!(this instanceof n))return new n(o,a);const s=this._getPropsFromConfig(a);this._platformFunctions=t,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=n.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);const u=s.httpAgent||null;this._api={auth:null,host:s.host||_n,port:s.port||En,protocol:s.protocol||"https",basePath:Sn,version:s.apiVersion||bn,timeout:Ht("timeout",s.timeout,On),maxNetworkRetries:Ht("maxNetworkRetries",s.maxNetworkRetries,1),agent:u,httpClient:s.httpClient||(u?this._platformFunctions.createNodeHttpClient(u):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:s.stripeAccount||null};const c=s.typescript||!1;c!==n.USER_AGENT.typescript&&(n.USER_AGENT.typescript=c),s.appInfo&&this._setAppInfo(s.appInfo),this._prepResources(),this._setApiKey(o),this.errors=br,this.webhooks=r(),this._prevRequestMetrics=[],this._enableTelemetry=s.telemetry!==!1,this._requestSender=e(this),this.StripeResource=n.StripeResource}return n.errors=br,n.createNodeHttpClient=t.createNodeHttpClient,n.createFetchHttpClient=t.createFetchHttpClient,n.createNodeCryptoProvider=t.createNodeCryptoProvider,n.createSubtleCryptoProvider=t.createSubtleCryptoProvider,n.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,_setApiKey(o){o&&this._setApiField("auth",`Bearer ${o}`)},_setAppInfo(o){if(o&&typeof o!="object")throw new Error("AppInfo must be an object.");if(o&&!o.name)throw new Error("AppInfo.name is required");o=o||{},this._appInfo=Au.reduce((a,s)=>(typeof o[s]=="string"&&(a=a||{},a[s]=o[s]),a),void 0)},_setApiField(o,a){this._api[o]=a},getApiField(o){return this._api[o]},setClientId(o){this._clientId=o},getClientId(){return this._clientId},getConstant:o=>{switch(o){case"DEFAULT_HOST":return _n;case"DEFAULT_PORT":return En;case"DEFAULT_BASE_PATH":return Sn;case"DEFAULT_API_VERSION":return bn;case"DEFAULT_TIMEOUT":return On;case"MAX_NETWORK_RETRY_DELAY_SEC":return wn;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return xn}return n[o]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(o,a,s){const u=Ht(o,a,s);this._setApiField(o,u)},getMaxNetworkRetryDelay(){return wn},getInitialNetworkRetryDelay(){return xn},getClientUserAgent(o){return this.getClientUserAgentSeeded(n.USER_AGENT,o)},getClientUserAgentSeeded(o,a){this._platformFunctions.getUname().then(s=>{var u;const c={};for(const h in o)c[h]=encodeURIComponent((u=o[h])!==null&&u!==void 0?u:"null");c.uname=encodeURIComponent(s||"UNKNOWN");const l=this.getApiField("httpClient");l&&(c.httplib=encodeURIComponent(l.getClientName())),this._appInfo&&(c.application=this._appInfo),a(JSON.stringify(c))})},getAppInfoAsString(){if(!this._appInfo)return"";let o=this._appInfo.name;return this._appInfo.version&&(o+=`/${this._appInfo.version}`),this._appInfo.url&&(o+=` (${this._appInfo.url})`),o},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(const o in tr)this[Hi(o)]=new tr[o](this)},_getPropsFromConfig(o){if(!o)return{};const a=typeof o=="string";if(!(o===Object(o)&&!Array.isArray(o))&&!a)throw new Error("Config must either be an object or a string");if(a)return{apiVersion:o};if(Object.keys(o).filter(c=>!An.includes(c)).length>0)throw new Error(`Config object may only contain the following: ${An.join(", ")}`);return o}},n}Cu(new yo);
