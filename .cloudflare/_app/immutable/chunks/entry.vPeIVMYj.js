import{w as me,T as it,o as st}from"./index.UaHqEmIZ.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},n=new Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="30f752f8-155e-43ae-8d97-e15735f13728",e._sentryDebugIdIdentifier="sentry-dbid-30f752f8-155e-43ae-8d97-e15735f13728")}catch{}})();new URL("sveltekit-internal://");function ct(e,n){return e==="/"||n==="ignore"?e:n==="never"?e.endsWith("/")?e.slice(0,-1):e:n==="always"&&!e.endsWith("/")?e+"/":e}function lt(e){return e.split("%25").map(decodeURI).join("%25")}function ft(e){for(const n in e)e[n]=decodeURIComponent(e[n]);return e}function le({href:e}){return e.split("#")[0]}const ut=["href","pathname","search","toString","toJSON"];function dt(e,n,t){const r=new URL(e);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(a,o){if(o==="get"||o==="getAll"||o==="has")return i=>(t(i),a[o](i));n();const s=Reflect.get(a,o);return typeof s=="function"?s.bind(a):s}}),enumerable:!0,configurable:!0});for(const a of ut)Object.defineProperty(r,a,{get(){return n(),e[a]},enumerable:!0,configurable:!0});return r}const ht="/__data.json",pt=".html__data.json";function gt(e){return e.endsWith(".html")?e.replace(/\.html$/,pt):e.replace(/\/$/,"")+ht}function mt(...e){let n=5381;for(const t of e)if(typeof t=="string"){let r=t.length;for(;r;)n=n*33^t.charCodeAt(--r)}else if(ArrayBuffer.isView(t)){const r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);let a=r.length;for(;a;)n=n*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(n>>>0).toString(36)}function _t(e){const n=atob(e),t=new Uint8Array(n.length);for(let r=0;r<n.length;r++)t[r]=n.charCodeAt(r);return t.buffer}const Ve=window.fetch;window.fetch=(e,n)=>((e instanceof Request?e.method:(n==null?void 0:n.method)||"GET")!=="GET"&&F.delete(_e(e)),Ve(e,n));const F=new Map;function yt(e,n){const t=_e(e,n),r=document.querySelector(t);if(r!=null&&r.textContent){let{body:a,...o}=JSON.parse(r.textContent);const s=r.getAttribute("data-ttl");return s&&F.set(t,{body:a,init:o,ttl:1e3*Number(s)}),r.getAttribute("data-b64")!==null&&(a=_t(a)),Promise.resolve(new Response(a,o))}return window.fetch(e,n)}function wt(e,n,t){if(F.size>0){const r=_e(e,t),a=F.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(t==null?void 0:t.cache))return new Response(a.body,a.init);F.delete(r)}}return window.fetch(n,t)}function _e(e,n){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(n!=null&&n.headers||n!=null&&n.body){const a=[];n.headers&&a.push([...new Headers(n.headers)].join(",")),n.body&&(typeof n.body=="string"||ArrayBuffer.isView(n.body))&&a.push(n.body),r+=`[data-hash="${mt(...a)}"]`}return r}const vt=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function bt(e){const n=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${kt(e).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return n.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const o=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(o)return n.push({name:o[1],matcher:o[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const s=r.split(/\[(.+?)\](?!\])/);return"/"+s.map((c,f)=>{if(f%2){if(c.startsWith("x+"))return fe(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return fe(String.fromCharCode(...c.slice(2).split("-").map(l=>parseInt(l,16))));const u=vt.exec(c),[,h,g,d,m]=u;return n.push({name:d,matcher:m,optional:!!h,rest:!!g,chained:g?f===1&&s[0]==="":!1}),g?"(.*?)":h?"([^/]*)?":"([^/]+?)"}return fe(c)}).join("")}).join("")}/?$`),params:n}}function Et(e){return!/^\([^)]+\)$/.test(e)}function kt(e){return e.slice(1).split("/").filter(Et)}function St(e,n,t){const r={},a=e.slice(1),o=a.filter(i=>i!==void 0);let s=0;for(let i=0;i<n.length;i+=1){const c=n[i];let f=a[i-s];if(c.chained&&c.rest&&s&&(f=a.slice(i-s,i+1).filter(u=>u).join("/"),s=0),f===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||t[c.matcher](f)){r[c.name]=f;const u=n[i+1],h=a[i+1];u&&!u.rest&&u.optional&&h&&c.chained&&(s=0),!u&&!h&&Object.keys(r).length===o.length&&(s=0);continue}if(c.optional&&c.chained){s++;continue}return}if(!s)return r}function fe(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function At({nodes:e,server_loads:n,dictionary:t,matchers:r}){const a=new Set(n);return Object.entries(t).map(([i,[c,f,u]])=>{const{pattern:h,params:g}=bt(i),d={id:i,exec:m=>{const l=h.exec(m);if(l)return St(l,g,r)},errors:[1,...u||[]].map(m=>e[m]),layouts:[0,...f||[]].map(s),leaf:o(c)};return d.errors.length=d.layouts.length=Math.max(d.errors.length,d.layouts.length),d});function o(i){const c=i<0;return c&&(i=~i),[c,e[i]]}function s(i){return i===void 0?i:[a.has(i),e[i]]}}function Fe(e,n=JSON.parse){try{return n(sessionStorage[e])}catch{}}function xe(e,n,t=JSON.stringify){const r=t(n);try{sessionStorage[e]=r}catch{}}var $e;const I=(($e=globalThis.__sveltekit_2uzl2x)==null?void 0:$e.base)??"";var Ce;const Rt=((Ce=globalThis.__sveltekit_2uzl2x)==null?void 0:Ce.assets)??I,It="1755813141400",Me="sveltekit:snapshot",Ge="sveltekit:scroll",qe="sveltekit:states",Lt="sveltekit:pageurl",j="sveltekit:history",M="sveltekit:navigation",z={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},B=location.origin;function He(e){if(e instanceof URL)return e;let n=document.baseURI;if(!n){const t=document.getElementsByTagName("base");n=t.length?t[0].href:document.URL}return new URL(e,n)}function ye(){return{x:pageXOffset,y:pageYOffset}}function O(e,n){return e.getAttribute(`data-sveltekit-${n}`)}const Te={...z,"":z.hover};function Be(e){let n=e.assignedSlot??e.parentNode;return(n==null?void 0:n.nodeType)===11&&(n=n.host),n}function Ke(e,n){for(;e&&e!==n;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=Be(e)}}function he(e,n){let t;try{t=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI)}catch{}const r=e instanceof SVGAElement?e.target.baseVal:e.target,a=!t||!!r||ee(t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),o=(t==null?void 0:t.origin)===B&&e.hasAttribute("download");return{url:t,external:a,target:r,download:o}}function Y(e){let n=null,t=null,r=null,a=null,o=null,s=null,i=e;for(;i&&i!==document.documentElement;)r===null&&(r=O(i,"preload-code")),a===null&&(a=O(i,"preload-data")),n===null&&(n=O(i,"keepfocus")),t===null&&(t=O(i,"noscroll")),o===null&&(o=O(i,"reload")),s===null&&(s=O(i,"replacestate")),i=Be(i);function c(f){switch(f){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:Te[r??"off"],preload_data:Te[a??"off"],keepfocus:c(n),noscroll:c(t),reload:c(o),replace_state:c(s)}}function Ue(e){const n=me(e);let t=!0;function r(){t=!0,n.update(s=>s)}function a(s){t=!1,n.set(s)}function o(s){let i;return n.subscribe(c=>{(i===void 0||t&&c!==i)&&s(i=c)})}return{notify:r,set:a,subscribe:o}}function Pt(){const{set:e,subscribe:n}=me(!1);let t;async function r(){clearTimeout(t);try{const a=await fetch(`${Rt}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const s=(await a.json()).version!==It;return s&&(e(!0),clearTimeout(t)),s}catch{return!1}}return{subscribe:n,check:r}}function ee(e,n){return e.origin!==B||!e.pathname.startsWith(n)}const xt=-1,Tt=-2,Ut=-3,Nt=-4,Ot=-5,jt=-6;function Dt(e,n){if(typeof e=="number")return a(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const t=e,r=Array(t.length);function a(o,s=!1){if(o===xt)return;if(o===Ut)return NaN;if(o===Nt)return 1/0;if(o===Ot)return-1/0;if(o===jt)return-0;if(s)throw new Error("Invalid input");if(o in r)return r[o];const i=t[o];if(!i||typeof i!="object")r[o]=i;else if(Array.isArray(i))if(typeof i[0]=="string"){const c=i[0],f=n==null?void 0:n[c];if(f)return r[o]=f(a(i[1]));switch(c){case"Date":r[o]=new Date(i[1]);break;case"Set":const u=new Set;r[o]=u;for(let d=1;d<i.length;d+=1)u.add(a(i[d]));break;case"Map":const h=new Map;r[o]=h;for(let d=1;d<i.length;d+=2)h.set(a(i[d]),a(i[d+1]));break;case"RegExp":r[o]=new RegExp(i[1],i[2]);break;case"Object":r[o]=Object(i[1]);break;case"BigInt":r[o]=BigInt(i[1]);break;case"null":const g=Object.create(null);r[o]=g;for(let d=1;d<i.length;d+=2)g[i[d]]=a(i[d+1]);break;default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(i.length);r[o]=c;for(let f=0;f<i.length;f+=1){const u=i[f];u!==Tt&&(c[f]=a(u))}}else{const c={};r[o]=c;for(const f in i){const u=i[f];c[f]=a(u)}}return r[o]}return a(0)}const ze=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...ze];const $t=new Set([...ze]);[...$t];function Ct(e){return e.filter(n=>n!=null)}class te{constructor(n,t){this.status=n,typeof t=="string"?this.body={message:t}:t?this.body=t:this.body={message:`Error: ${n}`}}toString(){return JSON.stringify(this.body)}}class Ye{constructor(n,t){this.status=n,this.location=t}}class we extends Error{constructor(n,t,r){super(r),this.status=n,this.text=t}}const Vt="x-sveltekit-invalidated",Ft="x-sveltekit-trailing-slash";function J(e){return e instanceof te||e instanceof we?e.status:500}function Mt(e){return e instanceof we?e.text:"Internal Error"}const N=Fe(Ge)??{},G=Fe(Me)??{},x={url:Ue({}),page:Ue({}),navigating:me(null),updated:Pt()};function ve(e){N[e]=ye()}function Gt(e,n){let t=e+1;for(;N[t];)delete N[t],t+=1;for(t=n+1;G[t];)delete G[t],t+=1}function D(e){return location.href=e.href,new Promise(()=>{})}function Ne(){}let ne,pe,W,L,ge,C;const be=[],X=[];let P=null;const Ee=[],qt=[];let U=[],y={branch:[],error:null,url:null},ke=!1,Z=!1,Oe=!0,q=!1,V=!1,Je=!1,ae=!1,re,k,R,A,$,ue;async function en(e,n,t){var a,o;document.URL!==location.href&&(location.href=location.href),C=e,ne=At(e),L=document.documentElement,ge=n,pe=e.nodes[0],W=e.nodes[1],pe(),W(),k=(a=history.state)==null?void 0:a[j],R=(o=history.state)==null?void 0:o[M],k||(k=R=Date.now(),history.replaceState({...history.state,[j]:k,[M]:R},""));const r=N[k];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),t?await Xt(ge,t):Jt(location.href,{replaceState:!0}),Wt()}async function Ht(){if(await(ue||(ue=Promise.resolve())),!ue)return;ue=null;const e=ie(y.url,!0);P=null;const n=$={},t=e&&await Ie(e);n===$&&(t&&(t.type==="redirect"?await Se(new URL(t.location,y.url).href,{},1,n):(t.props.page!==void 0&&(A=t.props.page),re.$set(t.props))),be.length=0)}function We(e){X.some(n=>n==null?void 0:n.snapshot)&&(G[e]=X.map(n=>{var t;return(t=n==null?void 0:n.snapshot)==null?void 0:t.capture()}))}function Xe(e){var n;(n=G[e])==null||n.forEach((t,r)=>{var a,o;(o=(a=X[r])==null?void 0:a.snapshot)==null||o.restore(t)})}function je(){ve(k),xe(Ge,N),We(R),xe(Me,G)}async function Se(e,n,t,r){return K({type:"goto",url:He(e),keepfocus:n.keepFocus,noscroll:n.noScroll,replace_state:n.replaceState,state:n.state,redirect_count:t,nav_token:r,accept:()=>{n.invalidateAll&&(ae=!0)}})}async function Bt(e){return P={id:e.id,promise:Ie(e).then(n=>(n.type==="loaded"&&n.state.error&&(P=null),n))},P.promise}async function de(e){const n=ne.find(t=>t.exec(Qe(e)));n&&await Promise.all([...n.layouts,n.leaf].map(t=>t==null?void 0:t[1]()))}function Ze(e,n){var a;y=e.state;const t=document.querySelector("style[data-sveltekit]");t&&t.remove(),A=e.props.page,re=new C.root({target:n,props:{...e.props,stores:x,components:X},hydrate:!0}),Xe(R);const r={from:null,to:{params:y.params,route:{id:((a=y.route)==null?void 0:a.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};U.forEach(o=>o(r)),Z=!0}async function Q({url:e,params:n,branch:t,status:r,error:a,route:o,form:s}){let i="never";if(I&&(e.pathname===I||e.pathname===I+"/"))i="always";else for(const d of t)(d==null?void 0:d.slash)!==void 0&&(i=d.slash);e.pathname=ct(e.pathname,i),e.search=e.search;const c={type:"loaded",state:{url:e,params:n,branch:t,error:a,route:o},props:{constructors:Ct(t).map(d=>d.node.component),page:A}};s!==void 0&&(c.props.form=s);let f={},u=!A,h=0;for(let d=0;d<Math.max(t.length,y.branch.length);d+=1){const m=t[d],l=y.branch[d];(m==null?void 0:m.data)!==(l==null?void 0:l.data)&&(u=!0),m&&(f={...f,...m.data},u&&(c.props[`data_${h}`]=f),h+=1)}return(!y.url||e.href!==y.url.href||y.error!==a||s!==void 0&&s!==A.form||u)&&(c.props.page={error:a,params:n,route:{id:(o==null?void 0:o.id)??null},state:{},status:r,url:new URL(e),form:s??null,data:u?f:A.data}),c}async function Ae({loader:e,parent:n,url:t,params:r,route:a,server_data_node:o}){var u,h,g;let s=null,i=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},f=await e();if((u=f.universal)!=null&&u.load){let d=function(...l){for(const _ of l){const{href:v}=new URL(_,t);c.dependencies.add(v)}};const m={route:new Proxy(a,{get:(l,_)=>(i&&(c.route=!0),l[_])}),params:new Proxy(r,{get:(l,_)=>(i&&c.params.add(_),l[_])}),data:(o==null?void 0:o.data)??null,url:dt(t,()=>{i&&(c.url=!0)},l=>{i&&c.search_params.add(l)}),async fetch(l,_){let v;l instanceof Request?(v=l.url,_={body:l.method==="GET"||l.method==="HEAD"?void 0:await l.blob(),cache:l.cache,credentials:l.credentials,headers:l.headers,integrity:l.integrity,keepalive:l.keepalive,method:l.method,mode:l.mode,redirect:l.redirect,referrer:l.referrer,referrerPolicy:l.referrerPolicy,signal:l.signal,..._}):v=l;const S=new URL(v,t);return i&&d(S.href),S.origin===t.origin&&(v=S.href.slice(t.origin.length)),Z?wt(v,S.href,_):yt(v,_)},setHeaders:()=>{},depends:d,parent(){return i&&(c.parent=!0),n()},untrack(l){i=!1;try{return l()}finally{i=!0}}};s=await f.universal.load.call(null,m)??null}return{node:f,loader:e,server:o,universal:(h=f.universal)!=null&&h.load?{type:"data",data:s,uses:c}:null,data:s??(o==null?void 0:o.data)??null,slash:((g=f.universal)==null?void 0:g.trailingSlash)??(o==null?void 0:o.slash)}}function De(e,n,t,r,a,o){if(ae)return!0;if(!a)return!1;if(a.parent&&e||a.route&&n||a.url&&t)return!0;for(const s of a.search_params)if(r.has(s))return!0;for(const s of a.params)if(o[s]!==y.params[s])return!0;for(const s of a.dependencies)if(be.some(i=>i(new URL(s))))return!0;return!1}function Re(e,n){return(e==null?void 0:e.type)==="data"?e:(e==null?void 0:e.type)==="skip"?n??null:null}function Kt(e,n){if(!e)return new Set(n.searchParams.keys());const t=new Set([...e.searchParams.keys(),...n.searchParams.keys()]);for(const r of t){const a=e.searchParams.getAll(r),o=n.searchParams.getAll(r);a.every(s=>o.includes(s))&&o.every(s=>a.includes(s))&&t.delete(r)}return t}async function Ie({id:e,invalidating:n,url:t,params:r,route:a}){if((P==null?void 0:P.id)===e)return P.promise;const{errors:o,layouts:s,leaf:i}=a,c=[...s,i];o.forEach(p=>p==null?void 0:p().catch(()=>{})),c.forEach(p=>p==null?void 0:p[1]().catch(()=>{}));let f=null;const u=y.url?e!==y.url.pathname+y.url.search:!1,h=y.route?a.id!==y.route.id:!1,g=Kt(y.url,t);let d=!1;const m=c.map((p,w)=>{var T;const b=y.branch[w],E=!!(p!=null&&p[0])&&((b==null?void 0:b.loader)!==p[1]||De(d,h,u,g,(T=b.server)==null?void 0:T.uses,r));return E&&(d=!0),E});if(m.some(Boolean)){try{f=await at(t,m)}catch(p){return oe({status:J(p),error:await H(p,{url:t,params:r,route:{id:a.id}}),url:t,route:a})}if(f.type==="redirect")return f}const l=f==null?void 0:f.nodes;let _=!1;const v=c.map(async(p,w)=>{var se;if(!p)return;const b=y.branch[w],E=l==null?void 0:l[w];if((!E||E.type==="skip")&&p[1]===(b==null?void 0:b.loader)&&!De(_,h,u,g,(se=b.universal)==null?void 0:se.uses,r))return b;if(_=!0,(E==null?void 0:E.type)==="error")throw E;return Ae({loader:p[1],url:t,params:r,route:a,parent:async()=>{var Pe;const Le={};for(let ce=0;ce<w;ce+=1)Object.assign(Le,(Pe=await v[ce])==null?void 0:Pe.data);return Le},server_data_node:Re(E===void 0&&p[0]?{type:"skip"}:E??null,p[0]?b==null?void 0:b.server:void 0)})});for(const p of v)p.catch(()=>{});const S=[];for(let p=0;p<c.length;p+=1)if(c[p])try{S.push(await v[p])}catch(w){if(w instanceof Ye)return{type:"redirect",location:w.location};let b=J(w),E;if(l!=null&&l.includes(w))b=w.status??b,E=w.error;else if(w instanceof te)E=w.body;else{if(await x.updated.check())return await D(t);E=await H(w,{params:r,url:t,route:{id:a.id}})}const T=await zt(p,S,o);return T?await Q({url:t,params:r,branch:S.slice(0,T.idx).concat(T.node),status:b,error:E,route:a}):await tt(t,{id:a.id},E,b)}else S.push(void 0);return await Q({url:t,params:r,branch:S,status:200,error:null,route:a,form:n?void 0:null})}async function zt(e,n,t){for(;e--;)if(t[e]){let r=e;for(;!n[r];)r-=1;try{return{idx:r+1,node:{node:await t[e](),loader:t[e],data:{},server:null,universal:null}}}catch{continue}}}async function oe({status:e,error:n,url:t,route:r}){const a={};let o=null;if(C.server_loads[0]===0)try{const f=await at(t,[!0]);if(f.type!=="data"||f.nodes[0]&&f.nodes[0].type!=="data")throw 0;o=f.nodes[0]??null}catch{(t.origin!==B||t.pathname!==location.pathname||ke)&&await D(t)}const i=await Ae({loader:pe,url:t,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:Re(o)}),c={node:await W(),loader:W,universal:null,server:null,data:null};return await Q({url:t,params:a,branch:[i,c],status:e,error:n,route:null})}function ie(e,n){if(!e||ee(e,I))return;let t;try{t=C.hooks.reroute({url:new URL(e)})??e.pathname}catch{return}const r=Qe(t);for(const a of ne){const o=a.exec(r);if(o)return{id:e.pathname+e.search,invalidating:n,route:a,params:ft(o),url:e}}}function Qe(e){return lt(e.slice(I.length)||"/")}function et({url:e,type:n,intent:t,delta:r}){let a=!1;const o=ot(y,t,e,n);r!==void 0&&(o.navigation.delta=r);const s={...o.navigation,cancel:()=>{a=!0,o.reject(new Error("navigation cancelled"))}};return q||Ee.forEach(i=>i(s)),a?null:o}async function K({type:e,url:n,popped:t,keepfocus:r,noscroll:a,replace_state:o,state:s={},redirect_count:i=0,nav_token:c={},accept:f=Ne,block:u=Ne}){const h=ie(n,!1),g=et({url:n,type:e,delta:t==null?void 0:t.delta,intent:h});if(!g){u();return}const d=k,m=R;f(),q=!0,Z&&x.navigating.set(g.navigation),$=c;let l=h&&await Ie(h);if(!l){if(ee(n,I))return await D(n);l=await tt(n,{id:null},await H(new we(404,"Not Found",`Not found: ${n.pathname}`),{url:n,params:{},route:{id:null}}),404)}if(n=(h==null?void 0:h.url)||n,$!==c)return g.reject(new Error("navigation aborted")),!1;if(l.type==="redirect")if(i>=20)l=await oe({status:500,error:await H(new Error("Redirect loop"),{url:n,params:{},route:{id:null}}),url:n,route:{id:null}});else return Se(new URL(l.location,n).href,{},i+1,c),!1;else l.props.page.status>=400&&await x.updated.check()&&await D(n);if(be.length=0,ae=!1,ve(d),We(m),l.props.page.url.pathname!==n.pathname&&(n.pathname=l.props.page.url.pathname),s=t?t.state:s,!t){const p=o?0:1,w={[j]:k+=p,[M]:R+=p,[qe]:s};(o?history.replaceState:history.pushState).call(history,w,"",n),o||Gt(k,R)}if(P=null,l.props.page.state=s,Z){y=l.state,l.props.page&&(l.props.page.url=n);const p=(await Promise.all(qt.map(w=>w(g.navigation)))).filter(w=>typeof w=="function");if(p.length>0){let w=function(){U=U.filter(b=>!p.includes(b))};p.push(w),U.push(...p)}re.$set(l.props),Je=!0}else Ze(l,ge);const{activeElement:_}=document;await it();const v=t?t.scroll:a?ye():null;if(Oe){const p=n.hash&&document.getElementById(decodeURIComponent(n.hash.slice(1)));v?scrollTo(v.x,v.y):p?p.scrollIntoView():scrollTo(0,0)}const S=document.activeElement!==_&&document.activeElement!==document.body;!r&&!S&&Zt(),Oe=!0,l.props.page&&(A=l.props.page),q=!1,e==="popstate"&&Xe(R),g.fulfil(void 0),U.forEach(p=>p(g.navigation)),x.navigating.set(null)}async function tt(e,n,t,r){return e.origin===B&&e.pathname===location.pathname&&!ke?await oe({status:r,error:t,url:e,route:n}):await D(e)}function Yt(){let e;L.addEventListener("mousemove",o=>{const s=o.target;clearTimeout(e),e=setTimeout(()=>{r(s,2)},20)});function n(o){r(o.composedPath()[0],1)}L.addEventListener("mousedown",n),L.addEventListener("touchstart",n,{passive:!0});const t=new IntersectionObserver(o=>{for(const s of o)s.isIntersecting&&(de(s.target.href),t.unobserve(s.target))},{threshold:0});function r(o,s){const i=Ke(o,L);if(!i)return;const{url:c,external:f,download:u}=he(i,I);if(f||u)return;const h=Y(i);if(!h.reload)if(s<=h.preload_data){const g=ie(c,!1);g&&Bt(g)}else s<=h.preload_code&&de(c.pathname)}function a(){t.disconnect();for(const o of L.querySelectorAll("a")){const{url:s,external:i,download:c}=he(o,I);if(i||c)continue;const f=Y(o);f.reload||(f.preload_code===z.viewport&&t.observe(o),f.preload_code===z.eager&&de(s.pathname))}}U.push(a),a()}function H(e,n){if(e instanceof te)return e.body;const t=J(e),r=Mt(e);return C.hooks.handleError({error:e,event:n,status:t,message:r})??{message:r}}function nt(e,n){st(()=>(e.push(n),()=>{const t=e.indexOf(n);e.splice(t,1)}))}function tn(e){nt(U,e)}function nn(e){nt(Ee,e)}function Jt(e,n={}){return e=He(e),e.origin!==B?Promise.reject(new Error("goto: invalid URL")):Se(e,n,0)}function an(){return ae=!0,Ht()}function Wt(){var n;history.scrollRestoration="manual",addEventListener("beforeunload",t=>{let r=!1;if(je(),!q){const a=ot(y,void 0,null,"leave"),o={...a.navigation,cancel:()=>{r=!0,a.reject(new Error("navigation cancelled"))}};Ee.forEach(s=>s(o))}r?(t.preventDefault(),t.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&je()}),(n=navigator.connection)!=null&&n.saveData||Yt(),L.addEventListener("click",t=>{var g;if(t.button||t.which!==1||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.defaultPrevented)return;const r=Ke(t.composedPath()[0],L);if(!r)return;const{url:a,external:o,target:s,download:i}=he(r,I);if(!a)return;if(s==="_parent"||s==="_top"){if(window.parent!==window)return}else if(s&&s!=="_self")return;const c=Y(r);if(!(r instanceof SVGAElement)&&a.protocol!==location.protocol&&!(a.protocol==="https:"||a.protocol==="http:")||i)return;if(o||c.reload){et({url:a,type:"link"})?q=!0:t.preventDefault();return}const[u,h]=a.href.split("#");if(h!==void 0&&u===le(location)){const[,d]=y.url.href.split("#");if(d===h){t.preventDefault(),h===""||h==="top"&&r.ownerDocument.getElementById("top")===null?window.scrollTo({top:0}):(g=r.ownerDocument.getElementById(h))==null||g.scrollIntoView();return}if(V=!0,ve(k),e(a),!c.replace_state)return;V=!1}t.preventDefault(),K({type:"link",url:a,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??a.href===location.href})}),L.addEventListener("submit",t=>{if(t.defaultPrevented)return;const r=HTMLFormElement.prototype.cloneNode.call(t.target),a=t.submitter;if(((a==null?void 0:a.formMethod)||r.method)!=="get")return;const s=new URL((a==null?void 0:a.hasAttribute("formaction"))&&(a==null?void 0:a.formAction)||r.action);if(ee(s,I))return;const i=t.target,c=Y(i);if(c.reload)return;t.preventDefault(),t.stopPropagation();const f=new FormData(i),u=a==null?void 0:a.getAttribute("name");u&&f.append(u,(a==null?void 0:a.getAttribute("value"))??""),s.search=new URLSearchParams(f).toString(),K({type:"form",url:s,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??s.href===location.href})}),addEventListener("popstate",async t=>{var r;if((r=t.state)!=null&&r[j]){const a=t.state[j];if($={},a===k)return;const o=N[a],s=t.state[qe]??{},i=new URL(t.state[Lt]??location.href),c=t.state[M],f=le(location)===le(y.url);if(c===R&&(Je||f)){e(i),N[k]=ye(),o&&scrollTo(o.x,o.y),s!==A.state&&(A={...A,state:s},re.$set({page:A})),k=a;return}const h=a-k;await K({type:"popstate",url:i,popped:{state:s,scroll:o,delta:h},accept:()=>{k=a,R=c},block:()=>{history.go(-h)},nav_token:$})}else if(!V){const a=new URL(location.href);e(a)}}),addEventListener("hashchange",()=>{V&&(V=!1,history.replaceState({...history.state,[j]:++k,[M]:R},"",location.href))});for(const t of document.querySelectorAll("link"))t.rel==="icon"&&(t.href=t.href);addEventListener("pageshow",t=>{t.persisted&&x.navigating.set(null)});function e(t){y.url=t,x.page.set({...A,url:t}),x.page.notify()}}async function Xt(e,{status:n=200,error:t,node_ids:r,params:a,route:o,data:s,form:i}){ke=!0;const c=new URL(location.href);({params:a={},route:o={id:null}}=ie(c,!1)||{});let f;try{const u=r.map(async(d,m)=>{const l=s[m];return l!=null&&l.uses&&(l.uses=rt(l.uses)),Ae({loader:C.nodes[d],url:c,params:a,route:o,parent:async()=>{const _={};for(let v=0;v<m;v+=1)Object.assign(_,(await u[v]).data);return _},server_data_node:Re(l)})}),h=await Promise.all(u),g=ne.find(({id:d})=>d===o.id);if(g){const d=g.layouts;for(let m=0;m<d.length;m++)d[m]||h.splice(m,0,void 0)}f=await Q({url:c,params:a,branch:h,status:n,error:t,form:i,route:g??null})}catch(u){if(u instanceof Ye){await D(new URL(u.location,location.href));return}f=await oe({status:J(u),error:await H(u,{url:c,params:a,route:o}),url:c,route:o})}f.props.page&&(f.props.page.state={}),Ze(f,e)}async function at(e,n){var a;const t=new URL(e);t.pathname=gt(e.pathname),e.pathname.endsWith("/")&&t.searchParams.append(Ft,"1"),t.searchParams.append(Vt,n.map(o=>o?"1":"0").join(""));const r=await Ve(t.href);if(!r.ok){let o;throw(a=r.headers.get("content-type"))!=null&&a.includes("application/json")?o=await r.json():r.status===404?o="Not Found":r.status===500&&(o="Internal Error"),new te(r.status,o)}return new Promise(async o=>{var h;const s=new Map,i=r.body.getReader(),c=new TextDecoder;function f(g){return Dt(g,{Promise:d=>new Promise((m,l)=>{s.set(d,{fulfil:m,reject:l})})})}let u="";for(;;){const{done:g,value:d}=await i.read();if(g&&!u)break;for(u+=!d&&u?`
`:c.decode(d,{stream:!0});;){const m=u.indexOf(`
`);if(m===-1)break;const l=JSON.parse(u.slice(0,m));if(u=u.slice(m+1),l.type==="redirect")return o(l);if(l.type==="data")(h=l.nodes)==null||h.forEach(_=>{(_==null?void 0:_.type)==="data"&&(_.uses=rt(_.uses),_.data=f(_.data))}),o(l);else if(l.type==="chunk"){const{id:_,data:v,error:S}=l,p=s.get(_);s.delete(_),S?p.reject(f(S)):p.fulfil(f(v))}}}})}function rt(e){return{dependencies:new Set((e==null?void 0:e.dependencies)??[]),params:new Set((e==null?void 0:e.params)??[]),parent:!!(e!=null&&e.parent),route:!!(e!=null&&e.route),url:!!(e!=null&&e.url),search_params:new Set((e==null?void 0:e.search_params)??[])}}function Zt(){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const n=document.body,t=n.getAttribute("tabindex");n.tabIndex=-1,n.focus({preventScroll:!0,focusVisible:!1}),t!==null?n.setAttribute("tabindex",t):n.removeAttribute("tabindex");const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let o=0;o<r.rangeCount;o+=1)a.push(r.getRangeAt(o));setTimeout(()=>{if(r.rangeCount===a.length){for(let o=0;o<r.rangeCount;o+=1){const s=a[o],i=r.getRangeAt(o);if(s.commonAncestorContainer!==i.commonAncestorContainer||s.startContainer!==i.startContainer||s.endContainer!==i.endContainer||s.startOffset!==i.startOffset||s.endOffset!==i.endOffset)return}r.removeAllRanges()}})}}}function ot(e,n,t,r){var c,f;let a,o;const s=new Promise((u,h)=>{a=u,o=h});return s.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:((c=e.route)==null?void 0:c.id)??null},url:e.url},to:t&&{params:(n==null?void 0:n.params)??null,route:{id:((f=n==null?void 0:n.route)==null?void 0:f.id)??null},url:t},willUnload:!n,type:r,complete:s},fulfil:a,reject:o}}export{tn as a,nn as b,en as c,Jt as g,an as i,x as s};
