import{_ as m}from"./index.RK-K-o1D.js";import{w as z,u as _,H as D}from"./index.UaHqEmIZ.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},t=new Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="15ebaa3b-4ed2-41e9-9ecb-43d34e31a99e",e._sentryDebugIdIdentifier="sentry-dbid-15ebaa3b-4ed2-41e9-9ecb-43d34e31a99e")}catch{}})();function ft(e,t){const r={},o={},a={$$scope:1};let n=e.length;for(;n--;){const i=e[n],s=t[n];if(s){for(const l in i)l in s||(o[l]=1);for(const l in s)a[l]||(r[l]=s[l],a[l]=1);e[n]=s}else for(const l in i)a[l]=1}for(const i in o)i in r||(r[i]=void 0);return r}function mt(e){return typeof e=="object"&&e!==null?e:{}}const Qe=(e,t)=>{const r=e[t];return r?typeof r=="function"?r():Promise.resolve(r):new Promise((o,a)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(a.bind(null,new Error("Unknown variable dynamic import: "+t)))})};var Je=Object.defineProperty,Xe=Object.defineProperties,Ze=Object.getOwnPropertyDescriptors,X=Object.getOwnPropertySymbols,Te=Object.prototype.hasOwnProperty,$e=Object.prototype.propertyIsEnumerable,we=(e,t,r)=>t in e?Je(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,v=(e,t)=>{for(var r in t||(t={}))Te.call(t,r)&&we(e,r,t[r]);if(X)for(var r of X(t))$e.call(t,r)&&we(e,r,t[r]);return e},P=(e,t)=>Xe(e,Ze(t)),G=(e,t)=>{var r={};for(var o in e)Te.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&X)for(var o of X(e))t.indexOf(o)<0&&$e.call(e,o)&&(r[o]=e[o]);return r},ae=["error","warn","debug"],Ie=({logger:e=console,level:t=ae[1],prefix:r="[i18n]: "})=>ae.reduce((o,a,n)=>P(v({},o),{[a]:i=>ae.indexOf(t)>=n&&e[a](`${r}${i}`)}),{}),w=Ie({}),Ye=e=>{w=e},xe=e=>{var t=e,{parser:r,key:o,params:a,translations:n,locale:i,fallbackLocale:s}=t,l=G(t,["parser","key","params","translations","locale","fallbackLocale"]);if(!o)return w.warn(`No translation key provided ('${i}' locale). Skipping translation...`),"";if(!i)return w.warn(`No locale provided for '${o}' key. Skipping translation...`),"";let c=(n[i]||{})[o];if(s&&c===void 0&&(w.debug(`No translation provided for '${o}' key in locale '${i}'. Trying fallback '${s}'`),c=(n[s]||{})[o]),c===void 0){if(w.debug(`No translation provided for '${o}' key in fallback '${s}'.`),l.hasOwnProperty("fallbackValue"))return l.fallbackValue;w.warn(`No translation nor fallback found for '${o}' .`)}return r.parse(c,a,i,o)},T=(...e)=>e.length?e.filter(t=>!!t).map(t=>{let r=`${t}`.toLowerCase();try{let[o]=Intl.Collator.supportedLocalesOf(t);if(!o)throw new Error;r=o}catch{w.warn(`'${t}' locale is non-standard.`)}return r}):[],J=(e,t,r)=>{if(t&&Array.isArray(e))return e.map(o=>J(o,t));if(e&&typeof e=="object"){let o=Object.keys(e).reduce((a,n)=>{let i=e[n],s=r?`${r}.${n}`:`${n}`;return i&&typeof i=="object"&&!(t&&Array.isArray(i))?v(v({},a),J(i,t,s)):P(v({},a),{[s]:J(i,t)})},{});return Object.keys(o).length?o:null}return e},er=e=>e.reduce((t,{key:r,data:o,locale:a})=>{if(!o)return t;let[n]=T(a),i=P(v({},t[n]||{}),{[r]:o});return P(v({},t),{[n]:i})},{}),rr=async e=>{try{let t=await Promise.all(e.map(r=>{var o=r,{loader:a}=o,n=G(o,["loader"]);return new Promise(async i=>{let s;try{s=await a()}catch(l){w.error(`Failed to load translation. Verify your '${n.locale}' > '${n.key}' Loader.`),w.error(l)}i(P(v({loader:a},n),{data:s}))})}));return er(t)}catch(t){w.error(t)}return{}},tr=e=>t=>{try{if(typeof t=="string")return t===e;if(typeof t=="object")return t.test(e)}catch{w.error("Invalid route config!")}return!1},_e=(e,t)=>{let r=!0;try{r=Object.keys(e).filter(o=>e[o]!==void 0).every(o=>e[o]===t[o])}catch{}return r},ke=1e3*60*60*24,or=class{constructor(t){this.cachedAt=0,this.loadedKeys={},this.currentRoute=z(),this.config=z(),this.isLoading=z(!1),this.promises=new Set,this.loading={subscribe:this.isLoading.subscribe,toPromise:(r,o)=>{let{fallbackLocale:a}=_(this.config),n=Array.from(this.promises).filter(i=>{let s=_e({locale:T(r)[0],route:o},i);return a&&(s=s||_e({locale:T(a)[0],route:o},i)),s}).map(({promise:i})=>i);return Promise.all(n)},get:()=>_(this.isLoading)},this.privateRawTranslations=z({}),this.rawTranslations={subscribe:this.privateRawTranslations.subscribe,get:()=>_(this.rawTranslations)},this.privateTranslations=z({}),this.translations={subscribe:this.privateTranslations.subscribe,get:()=>_(this.translations)},this.locales=P(v({},D([this.config,this.privateTranslations],([r,o])=>{if(!r)return[];let{loaders:a=[]}=r,n=a.map(({locale:s})=>s),i=Object.keys(o).map(s=>s);return Array.from(new Set([...T(...n),...T(...i)]))},[])),{get:()=>_(this.locales)}),this.internalLocale=z(),this.loaderTrigger=D([this.internalLocale,this.currentRoute],([r,o],a)=>{var n,i;r!==void 0&&o!==void 0&&!(r===((n=_(this.loaderTrigger))==null?void 0:n[0])&&o===((i=_(this.loaderTrigger))==null?void 0:i[1]))&&(w.debug("Triggering translation load..."),a([r,o]))},[]),this.localeHelper=z(),this.locale={subscribe:this.localeHelper.subscribe,forceSet:this.localeHelper.set,set:this.internalLocale.set,update:this.internalLocale.update,get:()=>_(this.locale)},this.initialized=D([this.locale,this.currentRoute,this.privateTranslations],([r,o,a],n)=>{_(this.initialized)||n(r!==void 0&&o!==void 0&&!!Object.keys(a).length)}),this.translation=D([this.privateTranslations,this.locale,this.isLoading],([r,o,a],n)=>{let i=r[o];i&&Object.keys(i).length&&!a&&n(i)},{}),this.t=P(v({},D([this.config,this.translation],r=>{var[o]=r,a=o,{parser:n,fallbackLocale:i}=a,s=G(a,["parser","fallbackLocale"]);return(l,...c)=>xe(v({parser:n,key:l,params:c,translations:this.translations.get(),locale:this.locale.get(),fallbackLocale:i},s.hasOwnProperty("fallbackValue")?{fallbackValue:s.fallbackValue}:{}))})),{get:(r,...o)=>_(this.t)(r,...o)}),this.l=P(v({},D([this.config,this.translations],r=>{var[o,...a]=r,n=o,{parser:i,fallbackLocale:s}=n,l=G(n,["parser","fallbackLocale"]),[c]=a;return(u,f,...x)=>xe(v({parser:i,key:f,params:x,translations:c,locale:u,fallbackLocale:s},l.hasOwnProperty("fallbackValue")?{fallbackValue:l.fallbackValue}:{}))})),{get:(r,o,...a)=>_(this.l)(r,o,...a)}),this.getLocale=r=>{let{fallbackLocale:o}=_(this.config)||{},a=r||o;if(!a)return;let n=this.locales.get();return n.find(i=>T(a).includes(i))||n.find(i=>T(o).includes(i))},this.setLocale=r=>{if(r&&r!==_(this.internalLocale))return w.debug(`Setting '${r}' locale.`),this.internalLocale.set(r),this.loading.toPromise(r,_(this.currentRoute))},this.setRoute=r=>{if(r!==_(this.currentRoute)){w.debug(`Setting '${r}' route.`),this.currentRoute.set(r);let o=_(this.internalLocale);return this.loading.toPromise(o,r)}},this.loadConfig=async r=>{await this.configLoader(r)},this.getTranslationProps=async(r=this.locale.get(),o=_(this.currentRoute))=>{let a=_(this.config);if(!a||!r)return[];let n=this.translations.get(),{loaders:i,fallbackLocale:s="",cache:l=ke}=a||{},c=Number.isNaN(+l)?ke:+l;this.cachedAt?Date.now()>c+this.cachedAt&&(w.debug("Refreshing cache."),this.loadedKeys={},this.cachedAt=0):(w.debug("Setting cache timestamp."),this.cachedAt=Date.now());let[u,f]=T(r,s),x=n[u],L=n[f],O=(i||[]).map(p=>{var b=p,{locale:E}=b,y=G(b,["locale"]);return P(v({},y),{locale:T(E)[0]})}).filter(({routes:p})=>!p||(p||[]).some(tr(o))).filter(({key:p,locale:b})=>b===u&&(!x||!(this.loadedKeys[u]||[]).includes(p))||s&&b===f&&(!L||!(this.loadedKeys[f]||[]).includes(p)));if(O.length){this.isLoading.set(!0),w.debug("Fetching translations...");let p=await rr(O);this.isLoading.set(!1);let b=Object.keys(p).reduce((y,j)=>P(v({},y),{[j]:Object.keys(p[j])}),{}),E=O.filter(({key:y,locale:j})=>(b[j]||[]).some(C=>`${C}`.startsWith(y))).reduce((y,{key:j,locale:C})=>P(v({},y),{[C]:[...y[C]||[],j]}),{});return[p,E]}return[]},this.addTranslations=(r,o)=>{if(!r)return;let a=_(this.config),{preprocess:n}=a||{};w.debug("Adding translations...");let i=Object.keys(r||{});this.privateRawTranslations.update(s=>i.reduce((l,c)=>P(v({},l),{[c]:v(v({},l[c]||{}),r[c])}),s)),this.privateTranslations.update(s=>i.reduce((l,c)=>{let u=!0,f=r[c];return typeof n=="function"&&(f=n(f)),(typeof n=="function"||n==="none")&&(u=!1),P(v({},l),{[c]:v(v({},l[c]||{}),u?J(f,n==="preserveArrays"):f)})},s)),i.forEach(s=>{let l=Object.keys(r[s]).map(c=>`${c}`.split(".")[0]);o&&(l=o[s]),this.loadedKeys[s]=Array.from(new Set([...this.loadedKeys[s]||[],...l||[]]))})},this.loader=async([r,o])=>{let a=this.getLocale(r)||void 0;w.debug(`Adding loader promise for '${a}' locale and '${o}' route.`);let n=(async()=>{let i=await this.getTranslationProps(a,o);i.length&&this.addTranslations(...i)})();this.promises.add({locale:a,route:o,promise:n}),n.then(()=>{a&&this.locale.get()!==a&&this.locale.forceSet(a)})},this.loadTranslations=(r,o=_(this.currentRoute)||"")=>{let a=this.getLocale(r);if(a)return this.setRoute(o),this.setLocale(a),this.loading.toPromise(a,o)},this.loaderTrigger.subscribe(this.loader),this.isLoading.subscribe(async r=>{r&&this.promises.size&&(await this.loading.toPromise(),this.promises.clear(),w.debug("Loader promises have been purged."))}),t&&this.loadConfig(t)}async configLoader(t){if(!t)return w.error("No config provided!");let r=t,{initLocale:o,fallbackLocale:a,translations:n,log:i}=r,s=G(r,["initLocale","fallbackLocale","translations","log"]);i&&Ye(Ie(i)),[o]=T(o),[a]=T(a),w.debug("Setting config."),this.config.set(v({initLocale:o,fallbackLocale:a,translations:n},s)),n&&this.addTranslations(n),o&&await this.loadTranslations(o)}},Ce=Object.defineProperty,nr=Object.defineProperties,ar=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,Re=Object.prototype.hasOwnProperty,Ve=Object.prototype.propertyIsEnumerable,Oe=(e,t,r)=>t in e?Ce(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,$=(e,t)=>{for(var r in t||(t={}))Re.call(t,r)&&Oe(e,r,t[r]);if(Z)for(var r of Z(t))Ve.call(t,r)&&Oe(e,r,t[r]);return e},ce=(e,t)=>nr(e,ar(t)),S=(e,t)=>{var r={};for(var o in e)Re.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&Z)for(var o of Z(e))t.indexOf(o)<0&&Ve.call(e,o)&&(r[o]=e[o]);return r},ir=(e,t)=>{for(var r in t)Ce(e,r,{get:t[r],enumerable:!0})},Se={};ir(Se,{ago:()=>mr,currency:()=>br,date:()=>ur,eq:()=>de,gt:()=>Me,gte:()=>cr,lt:()=>ze,lte:()=>lr,ne:()=>sr,number:()=>dr});var ee=(e,t)=>{let{modifierDefaults:r}=t||{},{[e]:o}=r||{};return o||{}},de=({value:e,options:t=[],defaultValue:r=""})=>(t.find(({key:o})=>`${o}`.toLowerCase()===`${e}`.toLowerCase())||{}).value||r,sr=({value:e,options:t=[],defaultValue:r=""})=>(t.find(({key:o})=>`${o}`.toLowerCase()!==`${e}`.toLowerCase())||{}).value||r,ze=({value:e,options:t=[],defaultValue:r=""})=>(t.sort((o,a)=>+o.key-+a.key).find(({key:o})=>+e<+o)||{}).value||r,Me=({value:e,options:t=[],defaultValue:r=""})=>(t.sort((o,a)=>+a.key-+o.key).find(({key:o})=>+e>+o)||{}).value||r,lr=({value:e,options:t=[],defaultValue:r=""})=>de({value:e,options:t,defaultValue:ze({value:e,options:t,defaultValue:r})}),cr=({value:e,options:t=[],defaultValue:r=""})=>de({value:e,options:t,defaultValue:Me({value:e,options:t,defaultValue:r})}),dr=({value:e,props:t,defaultValue:r="",locale:o="",parserOptions:a})=>{if(!o)return"";let n=ee("number",a),{maximumFractionDigits:i}=n,s=S(n,["maximumFractionDigits"]),l=(t==null?void 0:t.number)||{},{maximumFractionDigits:c=i||2}=l,u=S(l,["maximumFractionDigits"]);return new Intl.NumberFormat(o,$(ce($({},s),{maximumFractionDigits:c}),u)).format(+e||+r)},ur=({value:e,props:t,defaultValue:r="",locale:o="",parserOptions:a})=>{if(!o)return"";let n=S(ee("date",a),[]),i=S((t==null?void 0:t.date)||{},[]);return new Intl.DateTimeFormat(o,$($({},n),i)).format(+e||+r)},ie=[{key:"second",multiplier:1e3},{key:"minute",multiplier:60},{key:"hour",multiplier:60},{key:"day",multiplier:24},{key:"week",multiplier:7},{key:"month",multiplier:13/3},{key:"year",multiplier:12}],De=(e="",t="")=>new RegExp(`^${e}s?$`).test(t),pr=e=>ie.indexOf(ie.find(({key:t})=>De(t,e))),fr=(e,t)=>ie.reduce(([r,o],{key:a,multiplier:n},i)=>{if(De(o,t))return[r,o];if(!o||i===pr(o)+1){let s=Math.round(r/n);if(!o||Math.abs(s)>=1||t!=="auto")return[s,a]}return[r,o]},[e,""]),mr=({value:e,defaultValue:t="",locale:r="",props:o,parserOptions:a})=>{if(!r)return"";let n=ee("ago",a),{format:i,numeric:s}=n,l=S(n,["format","numeric"]),c=(o==null?void 0:o.ago)||{},{format:u=i||"auto",numeric:f=s||"auto"}=c,x=S(c,["format","numeric"]),L=+e||+t,O=fr(L,u);return new Intl.RelativeTimeFormat(r,$(ce($({},l),{numeric:f}),x)).format(...O)},br=({value:e,defaultValue:t="",locale:r="",props:o,parserOptions:a})=>{if(!r)return"";let n=ee("currency",a),{ratio:i,currency:s}=n,l=S(n,["ratio","currency"]),c=(o==null?void 0:o.currency)||{},{ratio:u=i||1,currency:f=s}=c,x=S(c,["ratio","currency"]);return new Intl.NumberFormat(r,$(ce($({},l),{style:"currency",currency:f}),x)).format(u*(e||t))},gr=e=>typeof e=="string"&&/{{(?:(?!{{|}}).)+}}/.test(e),se=e=>typeof e=="string"?e.replace(/\\(?=:|;|{|})/g,""):e,hr=({value:e,props:t,payload:r,parserOptions:o,locale:a})=>`${e}`.replace(/{{\s*(?:(?!{{|}}).)+\s*}}/g,n=>{let i=se(`${n.match(/(?!{|\s).+?(?!\\[:;]).(?=\s*(?:[:;]|}}$))/)}`),s=r==null?void 0:r[i],[,l=""]=n.match(/.+?(?!\\;).;\s*default\s*:\s*([^\s:;].+?(?:\\[:;]|[^;}])*)(?=\s*(?:;|}}$))/i)||[];l=l||(r==null?void 0:r.default)||"";let[,c=""]=n.match(/{{\s*(?:[^;]|(?:\\;))+\s*(?:(?!\\:).[:])\s*(?!\s)((?:\\;|[^;])+?)(?=\s*(?:[;]|}}$))/i)||[];if(s===void 0&&c!=="ne")return l;let u=!!c,{customModifiers:f}=o||{},x=$($({},Se),f||{});c=Object.keys(x).includes(c)?c:"eq";let L=x[c],O=(n.match(/[^\s:;{](?:[^;]|\\[;])+[^:;}]/gi)||[]).reduce((p,b,E)=>{if(E>0){let y=se(`${b.match(/(?:(?:\\:)|[^:])+/)}`.trim()),j=`${b.match(/(?:(?:\\:)|[^:])+$/)}`.trimStart();if(y&&y!=="default"&&j)return[...p,{key:y,value:j}]}return p},[]);return!u&&!O.length?s:L({value:s,options:O,props:t,defaultValue:l,locale:a,parserOptions:o})}),Ge=({value:e,props:t,payload:r,parserOptions:o,locale:a})=>{if(gr(e)){let n=hr({value:e,payload:r,props:t,parserOptions:o,locale:a});return Ge({value:n,payload:r,props:t,parserOptions:o,locale:a})}else return se(e)},yr=e=>({parse:(t,[r,o],a,n)=>(r!=null&&r.default&&t===void 0&&(t=r.default),t===void 0&&(t=n),Ge({value:t,payload:r,props:o,parserOptions:e,locale:a}))}),vr=yr,wr=Object.defineProperty,xr=Object.defineProperties,_r=Object.getOwnPropertyDescriptors,Y=Object.getOwnPropertySymbols,Fe=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable,Le=(e,t,r)=>t in e?wr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,kr=(e,t)=>{for(var r in t||(t={}))Fe.call(t,r)&&Le(e,r,t[r]);if(Y)for(var r of Y(t))Ne.call(t,r)&&Le(e,r,t[r]);return e},Or=(e,t)=>xr(e,_r(t)),Lr=(e,t)=>{var r={};for(var o in e)Fe.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&Y)for(var o of Y(e))t.indexOf(o)<0&&Ne.call(e,o)&&(r[o]=e[o]);return r},Ee=e=>{var t=e,{parserOptions:r={}}=t,o=Lr(t,["parserOptions"]);return Or(kr({},o),{parser:vr(r)})},Er=class extends or{constructor(t){super(t&&Ee(t)),this.loadConfig=r=>super.configLoader(Ee(r))}},Ar=Er,k=(e=>(e.Login="/login",e.LoginOTP="/login/otp",e.Home="/",e.Search="/search",e.Favorites="/favorites",e.StationScan="/stations/scan",e.Settings="/settings",e.FAQ="/faq",e.Coupons="/coupons",e.OnBoarding="/onboarding",e.Report="/report",e.ReportEmail="/report/email",e.PaymentMethods="/payment-methods",e.PaymentMethodsCreate="/payment-methods/create",e.History="/history",e.HistoryCompleted="/history/completed",e))(k||{});const Pr="English",jr="Français",Ae={en:Pr,fr:jr},ht="fr",yt="lang",A=(e,t)=>["fr","en"].map(r=>({locale:r,key:e,routes:t,loader:async()=>(await Qe(Object.assign({"./en/common.json":()=>m(()=>import("./common.12YNXCze.js"),__vite__mapDeps([]),import.meta.url),"./en/coupons.json":()=>m(()=>import("./coupons.OW7xIeq9.js"),__vite__mapDeps([]),import.meta.url),"./en/faq.json":()=>m(()=>import("./faq.gE9_RobL.js"),__vite__mapDeps([]),import.meta.url),"./en/favorites.json":()=>m(()=>import("./favorites.aQ8_b9ac.js"),__vite__mapDeps([]),import.meta.url),"./en/history.json":()=>m(()=>import("./history.sxX-d4s9.js"),__vite__mapDeps([]),import.meta.url),"./en/home.json":()=>m(()=>import("./home.gFzY33RN.js"),__vite__mapDeps([]),import.meta.url),"./en/login.json":()=>m(()=>import("./login.7jjiqXjO.js"),__vite__mapDeps([]),import.meta.url),"./en/onboarding.json":()=>m(()=>import("./onboarding.9hHYHiHd.js"),__vite__mapDeps([]),import.meta.url),"./en/payment_methods.json":()=>m(()=>import("./payment_methods.xan0-LJl.js"),__vite__mapDeps([]),import.meta.url),"./en/report.json":()=>m(()=>import("./report.QgxpVxJr.js"),__vite__mapDeps([]),import.meta.url),"./en/search.json":()=>m(()=>import("./search.Zowngf_T.js"),__vite__mapDeps([]),import.meta.url),"./en/settings.json":()=>m(()=>import("./settings.3QeASeLH.js"),__vite__mapDeps([]),import.meta.url),"./en/station_scan.json":()=>m(()=>import("./station_scan.fzgo6sTE.js"),__vite__mapDeps([]),import.meta.url),"./fr/common.json":()=>m(()=>import("./common.oL5cp0f2.js"),__vite__mapDeps([]),import.meta.url),"./fr/coupons.json":()=>m(()=>import("./coupons.zaN3AVog.js"),__vite__mapDeps([]),import.meta.url),"./fr/faq.json":()=>m(()=>import("./faq.r9M9XlfK.js"),__vite__mapDeps([]),import.meta.url),"./fr/favorites.json":()=>m(()=>import("./favorites.8BlE1uN-.js"),__vite__mapDeps([]),import.meta.url),"./fr/history.json":()=>m(()=>import("./history.M68EvTCl.js"),__vite__mapDeps([]),import.meta.url),"./fr/home.json":()=>m(()=>import("./home.SmJyol5b.js"),__vite__mapDeps([]),import.meta.url),"./fr/login.json":()=>m(()=>import("./login.hgzyc7cx.js"),__vite__mapDeps([]),import.meta.url),"./fr/onboarding.json":()=>m(()=>import("./onboarding.DuuxZ6bn.js"),__vite__mapDeps([]),import.meta.url),"./fr/payment_methods.json":()=>m(()=>import("./payment_methods.tHq342--.js"),__vite__mapDeps([]),import.meta.url),"./fr/report.json":()=>m(()=>import("./report.9GrmHv78.js"),__vite__mapDeps([]),import.meta.url),"./fr/search.json":()=>m(()=>import("./search.8wBG90pG.js"),__vite__mapDeps([]),import.meta.url),"./fr/settings.json":()=>m(()=>import("./settings.ez_J0EIn.js"),__vite__mapDeps([]),import.meta.url),"./fr/station_scan.json":()=>m(()=>import("./station_scan.jUD-hZKQ.js"),__vite__mapDeps([]),import.meta.url)}),`./${r}/${e}.json`)).default})),Tr=e=>new RegExp(`^${e}(?:/[^/]+)?/?$`),$r={translations:{en:{lang:Ae},fr:{lang:Ae}},loaders:[...A("common"),...A("login",[k.Login,k.LoginOTP]),...A("home",[k.Home]),...A("search",[k.Search]),...A("settings",[k.Settings]),...A("onboarding",[k.OnBoarding]),...A("history",[k.History,k.HistoryCompleted,k.Home]),...A("payment_methods",[Tr(k.PaymentMethods),k.PaymentMethodsCreate,k.Home]),...A("report",[k.Report,k.ReportEmail]),...A("faq",[k.FAQ]),...A("station_scan",[k.StationScan]),...A("favorites",[k.Favorites]),...A("coupons",[k.Coupons])]},{t:vt,locale:wt,locales:xt,loading:_t,translations:kt,loadTranslations:Ot,addTranslations:Lt,setLocale:Et,setRoute:At}=new Ar($r);function He(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=He(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function Ir(){for(var e,t,r=0,o="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=He(e))&&(o&&(o+=" "),o+=t);return o}const ue="-";function Cr(e){const t=Vr(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;function a(i){const s=i.split(ue);return s[0]===""&&s.length!==1&&s.shift(),We(s,t)||Rr(i)}function n(i,s){const l=r[i]||[];return s&&o[i]?[...l,...o[i]]:l}return{getClassGroupId:a,getConflictingClassGroupIds:n}}function We(e,t){var i;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?We(e.slice(1),o):void 0;if(a)return a;if(t.validators.length===0)return;const n=e.join(ue);return(i=t.validators.find(({validator:s})=>s(n)))==null?void 0:i.classGroupId}const Pe=/^\[(.+)\]$/;function Rr(e){if(Pe.test(e)){const t=Pe.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}}function Vr(e){const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return zr(Object.entries(e.classGroups),r).forEach(([n,i])=>{le(i,o,n,t)}),o}function le(e,t,r,o){e.forEach(a=>{if(typeof a=="string"){const n=a===""?t:je(t,a);n.classGroupId=r;return}if(typeof a=="function"){if(Sr(a)){le(a(o),t,r,o);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([n,i])=>{le(i,je(t,n),r,o)})})}function je(e,t){let r=e;return t.split(ue).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r}function Sr(e){return e.isThemeGetter}function zr(e,t){return t?e.map(([r,o])=>{const a=o.map(n=>typeof n=="string"?t+n:typeof n=="object"?Object.fromEntries(Object.entries(n).map(([i,s])=>[t+i,s])):n);return[r,a]}):e}function Mr(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;function a(n,i){r.set(n,i),t++,t>e&&(t=0,o=r,r=new Map)}return{get(n){let i=r.get(n);if(i!==void 0)return i;if((i=o.get(n))!==void 0)return a(n,i),i},set(n,i){r.has(n)?r.set(n,i):a(n,i)}}}const qe="!";function Dr(e){const t=e.separator,r=t.length===1,o=t[0],a=t.length;return function(i){const s=[];let l=0,c=0,u;for(let p=0;p<i.length;p++){let b=i[p];if(l===0){if(b===o&&(r||i.slice(p,p+a)===t)){s.push(i.slice(c,p)),c=p+a;continue}if(b==="/"){u=p;continue}}b==="["?l++:b==="]"&&l--}const f=s.length===0?i:i.substring(c),x=f.startsWith(qe),L=x?f.substring(1):f,O=u&&u>c?u-c:void 0;return{modifiers:s,hasImportantModifier:x,baseClassName:L,maybePostfixModifierPosition:O}}}function Gr(e){if(e.length<=1)return e;const t=[];let r=[];return e.forEach(o=>{o[0]==="["?(t.push(...r.sort(),o),r=[]):r.push(o)}),t.push(...r.sort()),t}function Fr(e){return{cache:Mr(e.cacheSize),splitModifiers:Dr(e),...Cr(e)}}const Nr=/\s+/;function Hr(e,t){const{splitModifiers:r,getClassGroupId:o,getConflictingClassGroupIds:a}=t,n=new Set;return e.trim().split(Nr).map(i=>{const{modifiers:s,hasImportantModifier:l,baseClassName:c,maybePostfixModifierPosition:u}=r(i);let f=o(u?c.substring(0,u):c),x=!!u;if(!f){if(!u)return{isTailwindClass:!1,originalClassName:i};if(f=o(c),!f)return{isTailwindClass:!1,originalClassName:i};x=!1}const L=Gr(s).join(":");return{isTailwindClass:!0,modifierId:l?L+qe:L,classGroupId:f,originalClassName:i,hasPostfixModifier:x}}).reverse().filter(i=>{if(!i.isTailwindClass)return!0;const{modifierId:s,classGroupId:l,hasPostfixModifier:c}=i,u=s+l;return n.has(u)?!1:(n.add(u),a(l,c).forEach(f=>n.add(s+f)),!0)}).reverse().map(i=>i.originalClassName).join(" ")}function Wr(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Ue(t))&&(o&&(o+=" "),o+=r);return o}function Ue(e){if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=Ue(e[o]))&&(r&&(r+=" "),r+=t);return r}function qr(e,...t){let r,o,a,n=i;function i(l){const c=t.reduce((u,f)=>f(u),e());return r=Fr(c),o=r.cache.get,a=r.cache.set,n=s,s(l)}function s(l){const c=o(l);if(c)return c;const u=Hr(l,r);return a(l,u),u}return function(){return n(Wr.apply(null,arguments))}}function g(e){const t=r=>r[e]||[];return t.isThemeGetter=!0,t}const Be=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ur=/^\d+\/\d+$/,Br=new Set(["px","full","screen"]),Kr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Qr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Jr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Xr=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Zr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function I(e){return M(e)||Br.has(e)||Ur.test(e)}function R(e){return F(e,"length",it)}function M(e){return!!e&&!Number.isNaN(Number(e))}function Q(e){return F(e,"number",M)}function W(e){return!!e&&Number.isInteger(Number(e))}function Yr(e){return e.endsWith("%")&&M(e.slice(0,-1))}function d(e){return Be.test(e)}function V(e){return Kr.test(e)}const et=new Set(["length","size","percentage"]);function rt(e){return F(e,et,Ke)}function tt(e){return F(e,"position",Ke)}const ot=new Set(["image","url"]);function nt(e){return F(e,ot,lt)}function at(e){return F(e,"",st)}function q(){return!0}function F(e,t,r){const o=Be.exec(e);return o?o[1]?typeof t=="string"?o[1]===t:t.has(o[1]):r(o[2]):!1}function it(e){return Qr.test(e)&&!Jr.test(e)}function Ke(){return!1}function st(e){return Xr.test(e)}function lt(e){return Zr.test(e)}function ct(){const e=g("colors"),t=g("spacing"),r=g("blur"),o=g("brightness"),a=g("borderColor"),n=g("borderRadius"),i=g("borderSpacing"),s=g("borderWidth"),l=g("contrast"),c=g("grayscale"),u=g("hueRotate"),f=g("invert"),x=g("gap"),L=g("gradientColorStops"),O=g("gradientColorStopPositions"),p=g("inset"),b=g("margin"),E=g("opacity"),y=g("padding"),j=g("saturate"),C=g("scale"),pe=g("sepia"),fe=g("skew"),me=g("space"),be=g("translate"),re=()=>["auto","contain","none"],te=()=>["auto","hidden","clip","visible","scroll"],oe=()=>["auto",d,t],h=()=>[d,t],ge=()=>["",I,R],U=()=>["auto",M,d],he=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],ye=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],ne=()=>["start","end","center","between","around","evenly","stretch"],N=()=>["","0",d],ve=()=>["auto","avoid","all","avoid-page","page","left","right","column"],H=()=>[M,Q],K=()=>[M,d];return{cacheSize:500,separator:":",theme:{colors:[q],spacing:[I,R],blur:["none","",V,d],brightness:H(),borderColor:[e],borderRadius:["none","","full",V,d],borderSpacing:h(),borderWidth:ge(),contrast:H(),grayscale:N(),hueRotate:K(),invert:N(),gap:h(),gradientColorStops:[e],gradientColorStopPositions:[Yr,R],inset:oe(),margin:oe(),opacity:H(),padding:h(),saturate:H(),scale:H(),sepia:N(),skew:K(),space:h(),translate:h()},classGroups:{aspect:[{aspect:["auto","square","video",d]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":ve()}],"break-before":[{"break-before":ve()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...he(),d]}],overflow:[{overflow:te()}],"overflow-x":[{"overflow-x":te()}],"overflow-y":[{"overflow-y":te()}],overscroll:[{overscroll:re()}],"overscroll-x":[{"overscroll-x":re()}],"overscroll-y":[{"overscroll-y":re()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",W,d]}],basis:[{basis:oe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",d]}],grow:[{grow:N()}],shrink:[{shrink:N()}],order:[{order:["first","last","none",W,d]}],"grid-cols":[{"grid-cols":[q]}],"col-start-end":[{col:["auto",{span:["full",W,d]},d]}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":[q]}],"row-start-end":[{row:["auto",{span:[W,d]},d]}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",d]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",d]}],gap:[{gap:[x]}],"gap-x":[{"gap-x":[x]}],"gap-y":[{"gap-y":[x]}],"justify-content":[{justify:["normal",...ne()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...ne(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...ne(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[me]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[me]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",d,t]}],"min-w":[{"min-w":[d,t,"min","max","fit"]}],"max-w":[{"max-w":[d,t,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[d,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[d,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[d,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[d,t,"auto","min","max","fit"]}],"font-size":[{text:["base",V,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Q]}],"font-family":[{font:[q]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",d]}],"line-clamp":[{"line-clamp":["none",M,Q]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,d]}],"list-image":[{"list-image":["none",d]}],"list-style-type":[{list:["none","disc","decimal",d]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[E]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[E]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,R]}],"underline-offset":[{"underline-offset":["auto",I,d]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:h()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",d]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",d]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[E]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...he(),tt]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",rt]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},nt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[O]}],"gradient-via-pos":[{via:[O]}],"gradient-to-pos":[{to:[O]}],"gradient-from":[{from:[L]}],"gradient-via":[{via:[L]}],"gradient-to":[{to:[L]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[E]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[E]}],"divide-style":[{divide:B()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[I,d]}],"outline-w":[{outline:[I,R]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:ge()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[E]}],"ring-offset-w":[{"ring-offset":[I,R]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,at]}],"shadow-color":[{shadow:[q]}],opacity:[{opacity:[E]}],"mix-blend":[{"mix-blend":ye()}],"bg-blend":[{"bg-blend":ye()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",V,d]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[f]}],saturate:[{saturate:[j]}],sepia:[{sepia:[pe]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[E]}],"backdrop-saturate":[{"backdrop-saturate":[j]}],"backdrop-sepia":[{"backdrop-sepia":[pe]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",d]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",d]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",d]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[W,d]}],"translate-x":[{"translate-x":[be]}],"translate-y":[{"translate-y":[be]}],"skew-x":[{"skew-x":[fe]}],"skew-y":[{"skew-y":[fe]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",d]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",d]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":h()}],"scroll-mx":[{"scroll-mx":h()}],"scroll-my":[{"scroll-my":h()}],"scroll-ms":[{"scroll-ms":h()}],"scroll-me":[{"scroll-me":h()}],"scroll-mt":[{"scroll-mt":h()}],"scroll-mr":[{"scroll-mr":h()}],"scroll-mb":[{"scroll-mb":h()}],"scroll-ml":[{"scroll-ml":h()}],"scroll-p":[{"scroll-p":h()}],"scroll-px":[{"scroll-px":h()}],"scroll-py":[{"scroll-py":h()}],"scroll-ps":[{"scroll-ps":h()}],"scroll-pe":[{"scroll-pe":h()}],"scroll-pt":[{"scroll-pt":h()}],"scroll-pr":[{"scroll-pr":h()}],"scroll-pb":[{"scroll-pb":h()}],"scroll-pl":[{"scroll-pl":h()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",d]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,R,Q]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const dt=qr(ct),Pt=(...e)=>dt(Ir(e));export{ht as D,yt as L,k as R,mt as a,xt as b,Pt as c,Lt as d,At as e,ft as g,wt as l,Et as s,vt as t};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
