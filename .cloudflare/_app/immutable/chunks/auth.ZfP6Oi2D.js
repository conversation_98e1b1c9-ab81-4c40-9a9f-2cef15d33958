import{s as h,P as l}from"./exports.GLhM_dct.js";import{s as I}from"./supabase.xRgAeO37.js";import{u as C}from"./user.oAuVK7RJ.js";import{b as v}from"./promo-codes.client.2UzDNhxK.js";import"./index.RK-K-o1D.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="1d535c2c-2f92-48f7-97c0-4935d5ae1fb1",e._sentryDebugIdIdentifier="sentry-dbid-1d535c2c-2f92-48f7-97c0-4935d5ae1fb1")}catch{}})();/*! js-cookie v3.0.5 | MIT */function d(e){for(var r=1;r<arguments.length;r++){var c=arguments[r];for(var a in c)e[a]=c[a]}return e}var _={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function s(e,r){function c(o,f,n){if(!(typeof document>"u")){n=d({},r,n),typeof n.expires=="number"&&(n.expires=new Date(Date.now()+n.expires*864e5)),n.expires&&(n.expires=n.expires.toUTCString()),o=encodeURIComponent(o).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var t="";for(var i in n)n[i]&&(t+="; "+i,n[i]!==!0&&(t+="="+n[i].split(";")[0]));return document.cookie=o+"="+e.write(f,o)+t}}function a(o){if(!(typeof document>"u"||arguments.length&&!o)){for(var f=document.cookie?document.cookie.split("; "):[],n={},t=0;t<f.length;t++){var i=f[t].split("="),E=i.slice(1).join("=");try{var u=decodeURIComponent(i[0]);if(n[u]=e.read(E,u),o===u)break}catch{}}return o?n[o]:n}}return Object.create({set:c,get:a,remove:function(o,f){c(o,"",d({},f,{expires:-1}))},withAttributes:function(o){return s(this.converter,d({},this.attributes,o))},withConverter:function(o){return s(d({},this.converter,o),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(e)}})}var p=s(_,{path:"/"});const m="access-token",g="refresh-token",O="redirect",k=async()=>{I.auth.onAuthStateChange((e,r)=>{!r||e==="SIGNED_OUT"?(p.remove(m,{path:"/"}),p.remove(g,{path:"/"}),h(null),l.reset()):(e==="SIGNED_IN"||e==="TOKEN_REFRESHED")&&(p.set(m,r.access_token,{path:"/",expires:365,sameSite:"Lax",secure:!0}),p.set(g,r.refresh_token,{path:"/",expires:365,sameSite:"Lax",secure:!0}),C.set(r.user),h({id:r.user.id,email:r.user.email,phone:r.user.phone}),l.identify(r.user.id,{email:r.user.email,phone:r.user.phone})),r&&e==="INITIAL_SESSION"&&v()})};export{O as R,p as a,k as o};
