import{s as G,e as k,l as H,p as B,c as q,a as P,d as c,m as J,b as i,i as w,h as S,E as C,I as K,A as N,B as j,C as L,t as M,g as O,j as Q,F as R}from"./index.UaHqEmIZ.js";import{S as W,i as X,t as E,g as Y,b as A,f as Z,h as F}from"./index.RK-K-o1D.js";import{s as T}from"./index.GsAsnf3x.js";import{c as U}from"./style.HZSn-yMG.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},l=new Error().stack;l&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[l]="6e2e10b3-9c82-48af-8225-fd04f85bf404",t._sentryDebugIdIdentifier="sentry-dbid-6e2e10b3-9c82-48af-8225-fd04f85bf404")}catch{}})();function z(t){let l,e,p,n,d;return{c(){l=k("div"),e=k("p"),p=M(t[1]),this.h()},l(r){l=q(r,"DIV",{class:!0});var f=P(l);e=q(f,"P",{class:!0});var m=P(e);p=O(m,t[1]),m.forEach(c),f.forEach(c),this.h()},h(){i(e,"class","text-sm text-red-400/80"),i(l,"class","absolute px-1 mt-1")},m(r,f){w(r,l,f),S(l,e),S(e,p),d=!0},p(r,f){(!d||f&2)&&Q(p,r[1])},i(r){d||(r&&R(()=>{d&&(n||(n=F(l,T,{axis:"y"},!0)),n.run(1))}),d=!0)},o(r){r&&(n||(n=F(l,T,{axis:"y"},!1)),n.run(0)),d=!1},d(r){r&&c(l),r&&n&&n.end()}}}function x(t){let l,e,p,n,d,r,f,m,_,h,b,o,g,y,I,v,D,V,s=!t[2]&&z(t);return{c(){l=k("div"),e=k("input"),I=H(),s&&s.c(),v=B(),this.h()},l(a){l=q(a,"DIV",{class:!0});var u=P(l);e=q(u,"INPUT",{class:!0,id:!0,type:!0,name:!0,placeholder:!0,minlength:!0,maxlength:!0,autocomplete:!0,inputmode:!0,pattern:!0}),u.forEach(c),I=J(a),s&&s.l(a),v=B(),this.h()},h(){e.value=t[0],i(e,"class",p=U("block w-full rounded-2xl outline-none shadow-app focus:shadow-md border-2 border-white bg-slate-50 transition-shadow focus:border-primary-500 p-3",{"border-red-400":!t[2],"placeholder:text-red-400/80":!t[2]})),i(e,"id",n=t[5].id),i(e,"type",d=t[5].type),i(e,"name",r=t[5].name),e.required=f=t[5].required,i(e,"placeholder",m=t[5].placeholder),e.autofocus=_=t[5].autofocus,i(e,"minlength",h=t[5].minlength),i(e,"maxlength",b=t[5].maxlength),i(e,"autocomplete",o=t[5].autocomplete),i(e,"inputmode",g=t[5].inputmode),i(e,"pattern",y=t[5].pattern),i(l,"class","relative text-black")},m(a,u){w(a,l,u),S(l,e),w(a,I,u),s&&s.m(a,u),w(a,v,u),t[5].autofocus&&e.focus(),D||(V=[C(e,"input",t[3]),C(e,"blur",t[4])],D=!0)},p(a,[u]){u&1&&e.value!==a[0]&&(e.value=a[0]),u&4&&p!==(p=U("block w-full rounded-2xl outline-none shadow-app focus:shadow-md border-2 border-white bg-slate-50 transition-shadow focus:border-primary-500 p-3",{"border-red-400":!a[2],"placeholder:text-red-400/80":!a[2]}))&&i(e,"class",p),u&32&&n!==(n=a[5].id)&&i(e,"id",n),u&32&&d!==(d=a[5].type)&&i(e,"type",d),u&32&&r!==(r=a[5].name)&&i(e,"name",r),u&32&&f!==(f=a[5].required)&&(e.required=f),u&32&&m!==(m=a[5].placeholder)&&i(e,"placeholder",m),u&32&&_!==(_=a[5].autofocus)&&(e.autofocus=_),u&32&&h!==(h=a[5].minlength)&&i(e,"minlength",h),u&32&&b!==(b=a[5].maxlength)&&i(e,"maxlength",b),u&32&&o!==(o=a[5].autocomplete)&&i(e,"autocomplete",o),u&32&&g!==(g=a[5].inputmode)&&i(e,"inputmode",g),u&32&&y!==(y=a[5].pattern)&&i(e,"pattern",y),a[2]?s&&(Y(),A(s,1,1,()=>{s=null}),Z()):s?(s.p(a,u),u&4&&E(s,1)):(s=z(a),s.c(),E(s,1),s.m(v.parentNode,v))},i(a){E(s)},o(a){A(s)},d(a){a&&(c(l),c(I),c(v)),s&&s.d(a),D=!1,K(V)}}}function $(t,l,e){let p,n;const d=["value","rules"];let r=N(l,d),{value:f}=l,{rules:m=[]}=l,_=!1;const h=o=>{e(0,f=o.target.value)},b=()=>{e(7,_=!0)};return t.$$set=o=>{l=j(j({},l),L(o)),e(5,r=N(l,d)),"value"in o&&e(0,f=o.value),"rules"in o&&e(6,m=o.rules)},t.$$.update=()=>{t.$$.dirty&192&&e(2,p=!_||m.every(o=>typeof o!="string")),t.$$.dirty&192&&e(1,n=_&&m.find(o=>typeof o=="string")||"")},[f,n,p,h,b,r,m,_]}class ue extends W{constructor(l){super(),X(this,l,$,x,G,{value:0,rules:6})}}export{ue as I};
