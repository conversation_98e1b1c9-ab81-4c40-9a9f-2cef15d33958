import{s as U,k as W,e as g,l as M,t as A,c as y,a as D,G as X,m as P,d as v,g as B,b as m,i as O,h as l,j as C,n as Y,f as J,H as Z}from"./index.UaHqEmIZ.js";import{S as x,i as $,e as ee,c as te,a as ae,m as se,t as ne,b as ie,d as oe}from"./index.RK-K-o1D.js";import{F as re,f as le,g as fe,S as de,a as ce,r as ue}from"./useGeolocation.3AiEsf14.js";import{R as K,l as _e}from"./style.HZSn-yMG.js";import{l as me,a as ve}from"./user.oAuVK7RJ.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},a=new Error().stack;a&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[a]="f3cfef67-3b02-4bb4-8704-2dc3e41dc496",t._sentryDebugIdIdentifier="sentry-dbid-f3cfef67-3b02-4bb4-8704-2dc3e41dc496")}catch{}})();function N(t){let a,e;return{c(){a=g("p"),e=A(t[4]),this.h()},l(s){a=y(s,"P",{class:!0});var o=D(a);e=B(o,t[4]),o.forEach(v),this.h()},h(){m(a,"class","text-xs text-center opacity-80 truncate w-14 mt-1")},m(s,o){O(s,a,o),l(a,e)},p(s,o){o&16&&C(e,s[4])},d(s){s&&v(a)}}}function he(t){let a,e,s,o,E=`<img src="${de}" alt="station" width="20" height="20"/>`,f,h,d,p,b=t[1].address+"",k,S,w,n,I,L,c,q,H,F,r=t[4]&&N(t);function Q(i){t[7](i)}let R={disabled:t[2],class:"text-2xl pr-2"};return t[0]!==void 0&&(R.value=t[0]),c=new re({props:R}),W.push(()=>ee(c,"value",Q)),c.$on("like",t[8]),c.$on("unlike",t[9]),{c(){a=g("li"),e=g("a"),s=g("div"),o=g("div"),o.innerHTML=E,f=M(),r&&r.c(),h=M(),d=g("div"),p=g("p"),k=A(b),S=M(),w=g("p"),n=A(t[3]),I=M(),L=g("div"),te(c.$$.fragment),this.h()},l(i){a=y(i,"LI",{class:!0});var u=D(a);e=y(u,"A",{href:!0,class:!0});var _=D(e);s=y(_,"DIV",{class:!0});var V=D(s);o=y(V,"DIV",{class:!0,"data-svelte-h":!0}),X(o)!=="svelte-1uoolxt"&&(o.innerHTML=E),f=P(V),r&&r.l(V),V.forEach(v),h=P(_),d=y(_,"DIV",{class:!0});var j=D(d);p=y(j,"P",{class:!0});var T=D(p);k=B(T,b),T.forEach(v),S=P(j),w=y(j,"P",{class:!0});var G=D(w);n=B(G,t[3]),G.forEach(v),j.forEach(v),I=P(_),L=y(_,"DIV",{});var z=D(L);ae(c.$$.fragment,z),z.forEach(v),_.forEach(v),u.forEach(v),this.h()},h(){m(o,"class","bg-primary-500 w-7 aspect-square rounded-full flex items-center justify-center"),m(s,"class","flex flex-col items-center overflow-hidden w-14"),m(p,"class","font-medium truncate"),m(w,"class","text-sm font-light line-clamp-2"),m(d,"class","flex-1 overflow-hidden"),m(e,"href",H=`${K.Home}?station_id=${t[1].station_id}`),m(e,"class","flex items-center gap-3 active:bg-slate-50 md:hover:bg-slate-50 px-3 py-2"),m(a,"class","border-t last-of-type:border-b")},m(i,u){O(i,a,u),l(a,e),l(e,s),l(s,o),l(s,f),r&&r.m(s,null),l(e,h),l(e,d),l(d,p),l(p,k),l(d,S),l(d,w),l(w,n),l(e,I),l(e,L),se(c,L,null),F=!0},p(i,[u]){i[4]?r?r.p(i,u):(r=N(i),r.c(),r.m(s,null)):r&&(r.d(1),r=null),(!F||u&2)&&b!==(b=i[1].address+"")&&C(k,b),(!F||u&8)&&C(n,i[3]);const _={};u&4&&(_.disabled=i[2]),!q&&u&1&&(q=!0,_.value=i[0],Y(()=>q=!1)),c.$set(_),(!F||u&2&&H!==(H=`${K.Home}?station_id=${i[1].station_id}`))&&m(e,"href",H)},i(i){F||(ne(c.$$.fragment,i),F=!0)},o(i){ie(c.$$.fragment,i),F=!1},d(i){i&&v(a),r&&r.d(),oe(c)}}}function pe(t,a,e){let s,o,E;J(t,_e,n=>e(10,o=n));let{station:f}=a,{isFavorite:h}=a,d=!1;const p=Z([me,ve],([n,I])=>le(fe(f.latitude??0,f.longitude??0,n,I),{language:o,unitDisplay:"short"}));J(t,p,n=>e(4,E=n));const b=async n=>{try{e(2,d=!0),n?await ce(f.station_id):await ue(f.station_id),e(0,h=n)}catch(I){console.error(I)}finally{e(2,d=!1)}};function k(n){h=n,e(0,h)}const S=()=>b(!0),w=()=>b(!1);return t.$$set=n=>{"station"in n&&e(1,f=n.station),"isFavorite"in n&&e(0,h=n.isFavorite)},t.$$.update=()=>{t.$$.dirty&2&&e(3,s=[f.city,f.state,f.country].filter(Boolean).join(", "))},[h,f,d,s,E,p,b,k,S,w]}class Ie extends x{constructor(a){super(),$(this,a,pe,he,U,{station:1,isFavorite:0})}}export{Ie as S};
