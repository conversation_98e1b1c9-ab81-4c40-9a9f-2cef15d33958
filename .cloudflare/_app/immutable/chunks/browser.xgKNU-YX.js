import{g as b}from"./_commonjsHelpers.jVd2wRzr.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},r=new Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="145dc2bd-32d4-4c8a-a07b-0d89e727203b",e._sentryDebugIdIdentifier="sentry-dbid-145dc2bd-32d4-4c8a-a07b-0d89e727203b")}catch{}})();function f(e,r){for(var n=0;n<r.length;n++){const t=r[n];if(typeof t!="string"&&!Array.isArray(t)){for(const o in t)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(t,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>t[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var d=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")};const a=b(d),i=f({__proto__:null,default:a},[d]);export{i as b};
