import"./stripe.esm.worker.tkZLw4d1.js";import{a as s}from"./api.FojSz0ks.js";import{s as t}from"./supabase.xRgAeO37.js";import{f as n}from"./pricing.IkKnCD68.js";import"./index.RK-K-o1D.js";(function(){try{var o=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},a=new Error().stack;a&&(o._sentryDebugIds=o._sentryDebugIds||{},o._sentryDebugIds[a]="c60d3dcd-d54d-4883-aa31-4e5970218fb5",o._sentryDebugIdIdentifier="sentry-dbid-c60d3dcd-d54d-4883-aa31-4e5970218fb5")}catch{}})();const _="code",r="promo_code",d=async o=>{const{data:a}=await s.get("/promo-codes",{params:{code:o}}),{data:{session:e}}=await t.auth.getSession();return!a&&e&&await t.auth.updateUser({data:{current_promo_code:null}}),a},p=async()=>{var e;const{data:o}=await t.auth.getSession(),a=(e=o.session)==null?void 0:e.user.user_metadata.current_promo_code;return a?await d(a):null},c=async o=>{await t.auth.updateUser({data:{current_promo_code:o}})},g=o=>o.coupon.amount_off?`-${n(o.coupon.amount_off/100)}`:`-${o.coupon.percent_off}%`,y=o=>{localStorage.setItem(r,o)},b=async()=>{const o=localStorage.getItem(r);o&&(await c(o),localStorage.removeItem(r))};export{_ as P,c as a,b,p as c,g,d as l,y as s};
