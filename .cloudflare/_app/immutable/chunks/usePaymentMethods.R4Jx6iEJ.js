import{s as oe,A as X,f as ie,B as z,C as le,P as H,l as j,e as y,t as D,p as O,m as x,c as w,a as S,g as C,d as h,b as $,i as I,h as v,j as A,G as ce,H as J,o as de,w as F}from"./index.UaHqEmIZ.js";import{S as fe,i as ue,c as N,a as U,m as V,t as P,b as L,d as B,g as K,f as Q}from"./index.RK-K-o1D.js";import{C as W,a as Y,D as Z,B as me}from"./payment-methods.h9FNc7Jc.js";import{C as pe}from"./Card.R0dmzyBc.js";import{c as ee,t as _e}from"./style.HZSn-yMG.js";import"./stripe.esm.worker.tkZLw4d1.js";import{a as te}from"./api.FojSz0ks.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},e=new Error().stack;e&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[e]="a5958f66-4ad1-44b1-98ae-8b0edd9fd368",s._sentryDebugIdIdentifier="sentry-dbid-a5958f66-4ad1-44b1-98ae-8b0edd9fd368")}catch{}})();var se="https://js.stripe.com/v3",ve=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,re="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",he=function(){for(var e=document.querySelectorAll('script[src^="'.concat(se,'"]')),r=0;r<e.length;r++){var t=e[r];if(ve.test(t.src))return t}return null},ne=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",t=document.createElement("script");t.src="".concat(se).concat(r);var n=document.head||document.body;if(!n)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},M=null,T=null,q=null,be=function(e){return function(){e(new Error("Failed to load Stripe.js"))}},ge=function(e,r){return function(){window.Stripe?e(window.Stripe):r(new Error("Stripe.js not available"))}},ye=function(e){return M!==null?M:(M=new Promise(function(r,t){if(typeof window>"u"||typeof document>"u"){r(null);return}if(window.Stripe&&e&&console.warn(re),window.Stripe){r(window.Stripe);return}try{var n=he();if(n&&e)console.warn(re);else if(!n)n=ne(e);else if(n&&q!==null&&T!==null){var o;n.removeEventListener("load",q),n.removeEventListener("error",T),(o=n.parentNode)===null||o===void 0||o.removeChild(n),n=ne(e)}q=ge(r,t),T=be(t),n.addEventListener("load",q),n.addEventListener("error",T)}catch(f){t(f);return}}),M.catch(function(r){return M=null,Promise.reject(r)}))},R,we=function(){return R||(R=ye(null).catch(function(e){return R=null,Promise.reject(e)}),R)};Promise.resolve().then(function(){return we()}).catch(function(s){console.warn(s)});function Ee(s){let e,r,t,n="•••• •••• ••••",o,f,u=s[0].last4+"",_,l,d,m=s[0].exp_month+"",g,k,b=String(s[0].exp_year).slice(-2)+"",p;return{c(){e=y("div"),r=y("div"),t=y("p"),t.textContent=n,o=j(),f=y("p"),_=D(u),l=j(),d=y("p"),g=D(m),k=D("/"),p=D(b),this.h()},l(i){e=w(i,"DIV",{class:!0});var a=S(e);r=w(a,"DIV",{class:!0});var c=S(r);t=w(c,"P",{"data-svelte-h":!0}),ce(t)!=="svelte-1qgpt4c"&&(t.textContent=n),o=x(c),f=w(c,"P",{class:!0});var E=S(f);_=C(E,u),E.forEach(h),c.forEach(h),l=x(a),d=w(a,"P",{class:!0});var G=S(d);g=C(G,m),k=C(G,"/"),p=C(G,b),G.forEach(h),a.forEach(h),this.h()},h(){$(f,"class","text-sm font-card svelte-9lelvo"),$(r,"class","flex items-center gap-1.5 shrink-0"),$(d,"class","text-sm font-card truncate svelte-9lelvo"),$(e,"class","flex items-center gap-3 opacity-80 font-bold")},m(i,a){I(i,e,a),v(e,r),v(r,t),v(r,o),v(r,f),v(f,_),v(e,l),v(e,d),v(d,g),v(d,k),v(d,p)},p(i,a){a&1&&u!==(u=i[0].last4+"")&&A(_,u),a&1&&m!==(m=i[0].exp_month+"")&&A(g,m),a&1&&b!==(b=String(i[0].exp_year).slice(-2)+"")&&A(p,b)},d(i){i&&h(e)}}}function Se(s){let e,r=s[3]("payment_methods.card_expired")+"",t;return{c(){e=y("span"),t=D(r),this.h()},l(n){e=w(n,"SPAN",{class:!0});var o=S(e);t=C(o,r),o.forEach(h),this.h()},h(){$(e,"class","text-red-500 text-xs font-medium")},m(n,o){I(n,e,o),v(e,t)},p(n,o){o&8&&r!==(r=n[3]("payment_methods.card_expired")+"")&&A(t,r)},d(n){n&&h(e)}}}function ae(s){let e,r;return e=new me({props:{class:"shrink-0 ml-auto",$$slots:{default:[Pe]},$$scope:{ctx:s}}}),{c(){N(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,n){V(e,t,n),r=!0},p(t,n){const o={};n&40&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){r||(P(e.$$.fragment,t),r=!0)},o(t){L(e.$$.fragment,t),r=!1},d(t){B(e,t)}}}function Pe(s){let e,r=s[3]("payment_methods.default")+"",t;return{c(){e=y("span"),t=D(r)},l(n){e=w(n,"SPAN",{});var o=S(e);t=C(o,r),o.forEach(h)},m(n,o){I(n,e,o),v(e,t)},p(n,o){o&8&&r!==(r=n[3]("payment_methods.default")+"")&&A(t,r)},d(n){n&&h(e)}}}function De(s){let e,r,t,n,o=(W[s[0].brand]||s[0].brand)+"",f,u,_,l,d;var m=Y[s[0].brand]||Z;function g(a,c){return{props:{class:"w-8 h-8 shrink-0"}}}m&&(e=H(m,g()));function k(a,c){return a[2]?Se:Ee}let b=k(s),p=b(s),i=s[1]&&ae(s);return{c(){e&&N(e.$$.fragment),r=j(),t=y("div"),n=y("p"),f=D(o),u=j(),p.c(),_=j(),i&&i.c(),l=O(),this.h()},l(a){e&&U(e.$$.fragment,a),r=x(a),t=w(a,"DIV",{class:!0});var c=S(t);n=w(c,"P",{class:!0});var E=S(n);f=C(E,o),E.forEach(h),u=x(c),p.l(c),c.forEach(h),_=x(a),i&&i.l(a),l=O(),this.h()},h(){$(n,"class","font-medium capitalize"),$(t,"class","truncate")},m(a,c){e&&V(e,a,c),I(a,r,c),I(a,t,c),v(t,n),v(n,f),v(t,u),p.m(t,null),I(a,_,c),i&&i.m(a,c),I(a,l,c),d=!0},p(a,c){if(c&1&&m!==(m=Y[a[0].brand]||Z)){if(e){K();const E=e;L(E.$$.fragment,1,0,()=>{B(E,1)}),Q()}m?(e=H(m,g()),N(e.$$.fragment),P(e.$$.fragment,1),V(e,r.parentNode,r)):e=null}(!d||c&1)&&o!==(o=(W[a[0].brand]||a[0].brand)+"")&&A(f,o),b===(b=k(a))&&p?p.p(a,c):(p.d(1),p=b(a),p&&(p.c(),p.m(t,null))),a[1]?i?(i.p(a,c),c&2&&P(i,1)):(i=ae(a),i.c(),P(i,1),i.m(l.parentNode,l)):i&&(K(),L(i,1,1,()=>{i=null}),Q())},i(a){d||(e&&P(e.$$.fragment,a),P(i),d=!0)},o(a){e&&L(e.$$.fragment,a),L(i),d=!1},d(a){a&&(h(r),h(t),h(_),h(l)),e&&B(e,a),p.d(),i&&i.d(a)}}}function Ce(s){let e,r;return e=new pe({props:{class:ee("relative flex items-center gap-3 border-2 border-white transition-all p-3",{"border-primary-500":s[1]},s[4].class),$$slots:{default:[De]},$$scope:{ctx:s}}}),{c(){N(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,n){V(e,t,n),r=!0},p(t,[n]){const o={};n&18&&(o.class=ee("relative flex items-center gap-3 border-2 border-white transition-all p-3",{"border-primary-500":t[1]},t[4].class)),n&47&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){r||(P(e.$$.fragment,t),r=!0)},o(t){L(e.$$.fragment,t),r=!1},d(t){B(e,t)}}}function $e(s,e,r){let t;const n=["card","isDefault"];let o=X(e,n),f;ie(s,_e,l=>r(3,f=l));let{card:u}=e,{isDefault:_=!1}=e;return s.$$set=l=>{e=z(z({},e),le(l)),r(4,o=X(e,n)),"card"in l&&r(0,u=l.card),"isDefault"in l&&r(1,_=l.isDefault)},s.$$.update=()=>{s.$$.dirty&1&&r(2,t=new Date(u.exp_year,u.exp_month)<new Date)},[u,_,t,f,o]}class xe extends fe{constructor(e){super(),ue(this,e,$e,Ce,oe,{card:0,isDefault:1})}}const Ge=(s=!0)=>{const e=F(null),r=F([]),t=F(s),n=J([r,e],([l,d])=>[...l].sort((m,g)=>m.id===d?-1:g.id===d?1:0)),o=J([e,r],([l,d])=>d.find(m=>m.id===l)),f=async()=>{const{data:l}=await te.get("/payments/customers");e.set(l.invoice_settings.default_payment_method)},u=async()=>{try{t.set(!0);const{data:l}=await te.get("/payments/methods");r.set(l)}finally{t.set(!1)}},_=async()=>{await Promise.all([f(),u()])};return de(()=>{s&&_()}),{load:_,defaultPaymentMethodId:e,paymentMethods:r,isLoading:t,sortedPaymentMethods:n,defaultPaymentMethod:o}};export{xe as C,Ge as u};
