import { beforeEach,describe, expect, it, vi } from "vitest";

import { onCollectPaymentMethodSuccess } from "$lib/events/stripe/collect_paymentMethod_success";
import { supabaseAdmin } from "$lib/services/supabase-admin";
import { computeRentalPriceCents,getHourlyPriceCents } from "$lib/utils/pricing";
import type { StripePayload } from "$types/carku";
import type { Tables } from "$types/supabase";

// Mock dependencies
vi.mock("$lib/services/supabase-admin", () => ({
    supabaseAdmin: {
        from: vi.fn(() => ({
            insert: vi.fn(() => ({ error: null })),
            select: vi.fn(() => ({
                eq: vi.fn(() => ({
                    single: vi.fn(() => ({
                        error: null,
                        data: {
                            id: "station-123",
                            hourly_price_cents: 500, // $5/hour instead of default $2
                        },
                    })),
                })),
            })),
        })),
    },
}));

vi.mock("$lib/services/stripe", () => ({
    stripe: {
        paymentIntents: {
            retrieve: vi.fn(() => ({
                id: "pi_test123",
                status: "requires_capture",
            })),
        },
    },
}));

vi.mock("$lib/utils/payments", () => ({
    generateOrderId: vi.fn(() => "ORDER123"),
}));

describe("POS Pricing Bug Investigation", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("getHourlyPriceCents function", () => {
        it("should return station hourly_price_cents when available", () => {
            const station: Partial<Tables<"stations">> = {
                hourly_price_cents: 500, // $5/hour
            };

            const result = getHourlyPriceCents(station as Tables<"stations">);
            expect(result).toBe(500);
        });

        it("should return default $2 when hourly_price_cents is null", () => {
            const station: Partial<Tables<"stations">> = {
                hourly_price_cents: null,
            };

            const result = getHourlyPriceCents(station as Tables<"stations">);
            expect(result).toBe(200); // $2 in cents
        });

        it("should return default $2 when hourly_price_cents is undefined", () => {
            const station: Partial<Tables<"stations">> = {};

            const result = getHourlyPriceCents(station as Tables<"stations">);
            expect(result).toBe(200); // $2 in cents
        });
    });

    describe("POS Order Creation Bug", () => {
        it("should demonstrate the bug: POS orders missing hourly_price_cents", async () => {
            const mockPayload: StripePayload = {
                mac: "",
                act: "collect_paymentMethod_success",
                did: "station-123",
                paymentId: "pi_test123",
            };

            // Call the POS order creation function
            await onCollectPaymentMethodSuccess(mockPayload);

            // Verify that the order was created
            expect(supabaseAdmin.from).toHaveBeenCalledWith("orders");

            // Get the insert call arguments
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const insertCall = (supabaseAdmin.from as any).mock.results[0].value.insert;
            const insertArgs = insertCall.mock.calls[0][0];

            // BUG: The order should include hourly_price_cents but it doesn't!
            expect(insertArgs).not.toHaveProperty("hourly_price_cents");
            expect(insertArgs.station_id).toBe("station-123");
            expect(insertArgs.amount_charged_cents).toBe(0);
        });

        it("should show how web app orders correctly include hourly_price_cents", () => {
            // This is how web app orders are created (from createOrder function)
            const webAppOrderData = {
                user_id: "user-123",
                station_id: "station-123",
                stripe_customer_id: "cus_123",
                stripe_payment_intent_id: "pi_123",
                carku_order_id: "ORDER123",
                cable_type: 1,
                type: "rental",
                status: "pending",
                amount_charged_cents: 0,
                promo_code: null,
                hourly_price_cents: 500, // ✅ Web app includes this!
            };

            expect(webAppOrderData).toHaveProperty("hourly_price_cents", 500);
        });
    });

    describe("Pricing Calculation Impact", () => {
        it("should show correct pricing with station-specific rate", () => {
            const startTime = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(); // 2 hours ago
            const stationRate = 500; // $5/hour

            const price = computeRentalPriceCents(startTime, stationRate);

            // Should be $10 for 2 hours at $5/hour (minus 5 free minutes)
            expect(price).toBeGreaterThan(900); // Should be close to $10
        });

        it("should show incorrect pricing with default $2 rate (the bug)", () => {
            const startTime = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(); // 2 hours ago
            const defaultRate = 200; // $2/hour (the bug)

            const price = computeRentalPriceCents(startTime, defaultRate);

            // Will be $4 for 2 hours at $2/hour instead of correct $10
            expect(price).toBeLessThan(500); // Much less than it should be
        });

        it("should demonstrate the financial impact of the bug", () => {
            const startTime = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(); // 2 hours ago

            const correctRate = 500; // $5/hour (station setting)
            const buggyRate = 200;   // $2/hour (default fallback)

            const correctPrice = computeRentalPriceCents(startTime, correctRate);
            const buggyPrice = computeRentalPriceCents(startTime, buggyRate);

            const lostRevenue = correctPrice - buggyPrice;

            console.log(`Correct price: $${correctPrice / 100}`);
            console.log(`Buggy price: $${buggyPrice / 100}`);
            console.log(`Lost revenue per transaction: $${lostRevenue / 100}`);

            expect(lostRevenue).toBeGreaterThan(0);
            expect(lostRevenue).toBeGreaterThan(300); // Should lose at least $3 per 2-hour rental
        });
    });
});
