<script lang="ts">
    import "leaflet/dist/leaflet.css";

    import dayjs from "dayjs";
    import type { LayerGroup, LeafletEvent, Map, Marker } from "leaflet";
    import { onMount } from "svelte";
    import { derived } from "svelte/store";
    import { fly } from "svelte/transition";

    import Ph<PERSON><PERSON>hairLight from "~icons/ph/crosshair-light";
    import PhGear from "~icons/ph/gear";
    import PhHeartLight from "~icons/ph/heart-light";
    import PhMagnifyingGlassLight from "~icons/ph/magnifying-glass-light";
    import SolarQrCodeLinear from "~icons/solar/qr-code-linear";
    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import BatteryStationPreview from "$components/BatteryStationPreview.svelte";
    import BatteryStationRentPreview from "$components/BatteryStationRentPreview.svelte";
    import OrderConfirmedDialog from "$components/OrderConfirmedDialog.svelte";
    import OrderItemPreview from "$components/OrderItemPreview.svelte";
    import PlacePreview from "$components/PlacePreview.svelte";
    import Button from "$components/ui/Button.svelte";
    import Loader from "$components/ui/Loader.svelte";
    import MapView from "$components/ui/MapView.svelte";
    import PlaceDot from "$lib/assets/images/marker.svg";
    import FullStationDot from "$lib/assets/images/station/full.svg";
    import EmptyStationDot from "$lib/assets/images/station/low.svg";
    import LowStationDot from "$lib/assets/images/station/normal.svg";
    import { order, useActiveOrder } from "$lib/composables/useActiveOrder";
    import { useGeolocation } from "$lib/composables/useGeolocation";
    import { t } from "$lib/i18n";
    import { api } from "$lib/services/api";
    import { supabase } from "$lib/services/supabase";
    import { isStationPreviewVisible, isStationRentPreviewVisible, station } from "$lib/stores/stations";
    import { getDistanceFromLatLngInMeters } from "$lib/utils/distance";
    import { cn } from "$lib/utils/style";
    import type { Address, PlaceDetails } from "$types/maps";
    import { OrderType } from "$types/orders";
    import { Route } from "$types/routes";
    import type { Tables } from "$types/supabase";

    const ZOOM_BREAKPOINT = 12;

    let map: Map;
    let userMarker: Marker;
    const stationsLayerGroups: LayerGroup[] = [];
    let placeMarker: Marker;
    let placeAddress: Address;
    let isPlacePreviewVisible = false;
    let stations: Tables<"stations">[] = [];
    let leaflet: typeof import("leaflet");
    let zoomLevel = 0;

    const orderConfirmedDialog: {
        visible: boolean,
        type: OrderType,
        slotId?: number,
    } = {
        visible: false,
        type: OrderType.Rental,
        slotId: undefined,
    };

    const { order: activeOrder, location: activeOrderLocation } = useActiveOrder();

    const isCenteredOnPlace = derived(page, (value) => !!value.url.searchParams.get("place_id"));

    const { latitude, longitude } = useGeolocation({
        onLocationLoaded: () => {
            if ($page.url.searchParams.get("place_id") || $page.url.searchParams.get("station_id")) {
                return;
            }

            centerMapOnUser();
        },
    });

    const loadingGeolocation = derived([latitude, longitude], ([lat, lng]) => !lat && !lng);

    const updateUserMarkerPosition = () => {
        if (!userMarker || $isCenteredOnPlace) {
            return;
        }

        userMarker.setLatLng([$latitude, $longitude]);
    };

    const centerMapOnUser = () => {
        map?.setView([$latitude, $longitude], 18);
        if (window.location.pathname === Route.Home) {
            goto(Route.Home);
        }
    };

    const onZoomMap = (event: CustomEvent<number>) => {
        const oldZoomLevel = zoomLevel;
        zoomLevel = event.detail;

        if (zoomLevel <= ZOOM_BREAKPOINT && zoomLevel < oldZoomLevel) {
            placeStationMarkers(stations, true);
        } else if (zoomLevel > ZOOM_BREAKPOINT && zoomLevel > oldZoomLevel) {
            placeStationMarkers(stations);
        }
    };

    const onClickMap = async () => {
        isStationPreviewVisible.set(false);
        isStationRentPreviewVisible.set(false);
        isPlacePreviewVisible = false;
        await goto(Route.Home);
    };

    const onClickStationMarker = async (event: LeafletEvent, targetStation: Tables<"stations">) => {
        isPlacePreviewVisible = false;
        station.set(targetStation);
        map?.setView(event.target.getLatLng(), 18);
        isStationPreviewVisible.set(true);
        await goto(`${Route.Home}?station_id=${targetStation.id}`);
    };

    const onClickPlaceMarker = (event: LeafletEvent) => {
        isStationPreviewVisible.set(false);
        map?.setView(event.target.getLatLng(), 18);
        isPlacePreviewVisible = true;
    };

    const getStationLayerGroup = (stations: Tables<"stations">[], options: {
        minimized: boolean,
        bgClass: string,
        icon: string,
    }) => {
        const markers = stations.map((station) => leaflet.marker(
            [station.latitude ?? 0, station.longitude ?? 0],
            {
                icon: options.minimized
                    ? leaflet.divIcon({ html: "<b />", className: cn("w-2 h-2 rounded-full border border-white", options.bgClass) })
                    : leaflet.icon({
                        iconSize: [32, 42],
                        iconUrl: options.icon,
                        iconAnchor: [16, 42],
                        className: "map-marker",
                    }),
            },
        ).on("click", (event) => onClickStationMarker(event, station)));

        return leaflet.layerGroup(markers);
    };

    const placeStationMarkers = (stations: Tables<"stations">[], minimized = false) => {
        stationsLayerGroups.forEach((layerGroup) => layerGroup.clearLayers());

        const emptyBatteryStations: Tables<"stations">[] = [];
        const lowBatteryStations: Tables<"stations">[] = [];
        const highBatteryStations: Tables<"stations">[] = [];

        stations.forEach(
            (station) => {
                const totalSlots = station.empty_slots + station.total_batteries;
                const availabilityPercentage = (station.usable_batteries / totalSlots) * 100;

                if (availabilityPercentage === 0) {
                    emptyBatteryStations.push(station);
                } else if (availabilityPercentage < 30) {
                    lowBatteryStations.push(station);
                } else {
                    highBatteryStations.push(station);
                }
            },
        );

        stationsLayerGroups.push(
            getStationLayerGroup(emptyBatteryStations, {
                minimized,
                bgClass: "bg-red-500",
                icon: EmptyStationDot,
            }).addTo(map),
            getStationLayerGroup(lowBatteryStations, {
                minimized,
                bgClass: "bg-papaya",
                icon: LowStationDot,
            }).addTo(map),
            getStationLayerGroup(highBatteryStations, {
                minimized,
                bgClass: "bg-primary-500",
                icon: FullStationDot,
            }).addTo(map),
        );
    };

    const loadNearestStations = async () => {
        const { data, error } = await supabase
            .from("stations")
            .select()
            .gte("last_heartbeat_at", dayjs().subtract(5, "minutes").toISOString())
            .not("latitude", "is", null)
            .not("longitude", "is", null);

        if (error) {
            console.error(error);
            return;
        }

        stations = data;

        placeStationMarkers(stations);

        const stationId = $page.url.searchParams.get("station_id");
        if (stationId) {
            centerMapOnStationId(stationId);
        }
    };

    const loadMap = async (
        event: CustomEvent<{
            map: Map,
            userMarker: Marker,
            leaflet: typeof import("leaflet")
        }>,
    ) => {
        leaflet = event.detail.leaflet;
        map = event.detail.map;
        userMarker = event.detail.userMarker;

        const placeId = $page.url.searchParams.get("place_id");

        if (placeId) {
            centerMapOnPlaceId(placeId);
        } else {
            placeMarker?.remove();
        }

        await loadNearestStations();

        const stationId = $page.url.searchParams.get("station_id");
        if (stationId) {
            centerMapOnStationId(stationId);
        }
    };

    const centerMapOnPlaceId = async (placeId: string) => {
        if (!leaflet) {
            return;
        }

        try {
            const { data } = await api.get<PlaceDetails>("/maps/places", {
                params: {
                    place_id: placeId,
                },
            });

            if (!placeMarker) {
                placeMarker = leaflet.marker([data.latitude, data.longitude], {
                    icon: leaflet.icon({
                        iconSize: [40, 40],
                        iconUrl: PlaceDot,
                        iconAnchor: [20, 20],
                        className: "map-marker",
                    }),
                })
                    .on("click", onClickPlaceMarker)
                    .addTo(map);
            } else {
                placeMarker.setLatLng([data.latitude, data.longitude]);
            }

            map?.setView([data.latitude, data.longitude], 18);

            placeAddress = {
                place_id: placeId,
                fulltext: data.address,
                text: data.address.split(",")[0],
                distance_meters: getDistanceFromLatLngInMeters($latitude, $longitude, data.latitude, data.longitude),
            };

            isPlacePreviewVisible = true;
        } catch (err) {
            console.error(err);
        }
    };

    const centerMapOnStationId = (stationId: string) => {
        const _station = stations.find((s) => s.id === stationId);

        if (!_station) {
            return;
        }

        map?.setView([_station.latitude ?? 0, _station.longitude ?? 0], 18);
        station.set(_station);
        isStationPreviewVisible.set(true);
    };

    const showOrderConfirmedDialog = (type: OrderType, event?: CustomEvent<number>) => {
        orderConfirmedDialog.slotId = event?.detail;
        orderConfirmedDialog.type = type;
        orderConfirmedDialog.visible = true;
        goto(Route.Home);
    };

    const onPurchaseCurrentRental = () => {
        showOrderConfirmedDialog(OrderType.Purchase);
        order.set(null);
    };

    onMount(() => {
        const latitudeSubscription = latitude.subscribe(() => updateUserMarkerPosition());
        const longitudeSubscription = longitude.subscribe(() => updateUserMarkerPosition());

        return () => {
            isStationPreviewVisible.set(false);
            latitudeSubscription();
            longitudeSubscription();
        };
    });
</script>

{#if $loadingGeolocation}
    <div class="flex justify-center mt-[50%]">
        <Loader />
    </div>
{/if}
<MapView
    on:load={loadMap}
    on:click={onClickMap}
    on:zoom={onZoomMap}
    class={cn($$restProps.class)}
/>
<div class="flex flex-col absolute top-6 right-0 pointer-events-none gap-3 px-3">
    <a
        href={Route.Search}
        class="bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 pointer-events-auto p-3"
    >
        <PhMagnifyingGlassLight
            width="22"
            height="22"
        />
    </a>
    <a
        href={Route.Settings}
        class="bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 pointer-events-auto p-3"
    >
        <PhGear
            width="22"
            height="22"
        />
    </a>
    <a
        href={Route.Favorites}
        class="bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 pointer-events-auto p-3"
    >
        <PhHeartLight
            width="22"
            height="22"
        />
    </a>
    <button
        on:click={centerMapOnUser}
        class="bg-white active:bg-slate-100 text-black rounded-2xl shadow-xl disabled:bg-slate-100 pointer-events-auto p-3"
    >
        <PhCrosshairLight
            width="22"
            height="22"
        />
    </button>
</div>
{#if $isStationPreviewVisible}
    <BatteryStationPreview />
{:else if $isStationRentPreviewVisible}
    <BatteryStationRentPreview
        on:rent={(event) => showOrderConfirmedDialog(OrderType.Rental, event)}
        on:purchase={(event) => showOrderConfirmedDialog(OrderType.Purchase, event)}
    />
{:else if isPlacePreviewVisible}
    <PlacePreview address={placeAddress} />
{:else if $activeOrder}
    <OrderItemPreview
        order={$activeOrder}
        location={$activeOrderLocation}
        on:purchase={onPurchaseCurrentRental}
    />
{:else}
    <div
        transition:fly={{ duration: 300, y: 300 }}
        class="flex items-center justify-between absolute bottom-6 left-0 w-full pointer-events-none gap-3 px-3"
    >
        <Button
            href={Route.StationScan}
            class="font-theme uppercase text-3xl flex-1 w-full flex items-center justify-center gap-2 pointer-events-auto py-5"
        >
            <span>{$t("home.unlock")}</span>
            <SolarQrCodeLinear
                width="30"
                height="30"
            />
        </Button>
    </div>
{/if}

<OrderConfirmedDialog
    visible={orderConfirmedDialog.visible}
    type={orderConfirmedDialog.type}
    slotId={orderConfirmedDialog.slotId}
/>
