<script lang="ts">
    import jsCookie from "js-cookie";
    import { parsePhoneNumber } from "libphonenumber-js";
    import { onMount } from "svelte";
    import { derived } from "svelte/store";

    import { goto } from "$app/navigation";
    import { page } from "$app/stores";
    import Button from "$components/ui/Button.svelte";
    import LanguageSelector from "$components/ui/LanguageSelector.svelte";
    import OneTimePasswordInput from "$components/ui/OneTimePasswordInput.svelte";
    import HikoLogoWhite from "$lib/assets/images/hiko-white.png";
    import { useTopBar } from "$lib/composables/useTopBar";
    import { locale, t } from "$lib/i18n";
    import { supabase } from "$lib/services/supabase";
    import { REDIRECT_COOKIE_NAME } from "$lib/utils/auth";
    import { cn } from "$lib/utils/style";
    import { Route } from "$types/routes";

    let otp = "";
    let verifyingOTP = false;
    let sendingOtp = false;
    let errors = {
        invalid_otp: false,
    };

    $: isOtpValid = otp.split("").every((char) => !!char.trim() && !isNaN(+char)) && otp.trim().length === 6;

    $: {
        if (otp) {
            errors.invalid_otp = false;
        }
    }

    const phoneNumber = derived(page, (value) => value.url.searchParams.get("phone_number"));

    useTopBar({
        backUrl: Route.Login,
        isVisible: false,
    });

    const setSMSListener = () => {
        if (!("OTPCredential" in window)) {
            return () => {};
        }

        const ac = new AbortController();

        (async () => {
            //@ts-expect-error is experimental
            const response: { code: string } | null = await navigator.credentials.get({
                //@ts-expect-error is experimental
                otp: { transport: ["sms"] },
                signal: ac.signal,
            });

            if (response) {
                otp = response.code;
            }
        })();

        return () => ac.abort("SMS listener was removed as component was unmounted");
    };

    const redirectAfterLogin = async () => {
        const redirectUrl = jsCookie.get(REDIRECT_COOKIE_NAME);

        if (redirectUrl) {
            jsCookie.remove(REDIRECT_COOKIE_NAME);
            await goto(redirectUrl, { replaceState: true });
        }

        return !!redirectUrl;
    };

    const verifyOtp = async () => {
        if (!$phoneNumber) {
            return;
        }

        try {
            verifyingOTP = true;
            const parsedPhoneNumber = parsePhoneNumber($phoneNumber, "CA").number;

            const { data, error } = await supabase.auth.verifyOtp({
                phone: parsedPhoneNumber,
                token: otp,
                type: "sms",
            });

            if (error) {
                console.error(error.message);
                errors.invalid_otp = error.name === "AuthApiError";
                return;
            }

            await supabase.auth.updateUser({
                data: {
                    language: $locale,
                },
            });

            if (await redirectAfterLogin()) {
                return;
            }

            // Check if user has seen onboarding tutorial
            if (data.user?.user_metadata.has_seen_onboarding) {
                await goto(Route.Home, { replaceState: true });
            } else {
                await goto(Route.OnBoarding, { replaceState: true });
            }
        } catch (err) {
            console.error(err);
        } finally {
            verifyingOTP = false;
        }
    };

    onMount(() => {
        if (!$phoneNumber) {
            goto(Route.Login, { replaceState: true });
        }

        const unsubscribe = setSMSListener();

        return () => {
            unsubscribe();
        };
    });
</script>

<div class="bg-primary-500 text-white flex flex-col flex-1 px-3 py-6">
    <LanguageSelector
        class="text-3xl font-theme text-black"
        buttonClass="uppercase"
    />
    <div class="flex justify-center mt-12">
        <img
            src={HikoLogoWhite}
            alt="HIKO"
            width="240"
        />
    </div>
    <p class="text-center"><b>Restez branché</b> sans vous arrêter!</p>

    <div class="mt-auto">
        <p class="text-xl font-medium text-center mb-1">{$t("login.texted_code")}</p>
        <div class="flex flex-col items-center gap-6 mb-6">
            <p class="flex items-center gap-1">
                <span>{$t("login.code_not_received")}</span>
                <button
                    on:click={() => goto(Route.Login)}
                    disabled={verifyingOTP || sendingOtp}
                    class={cn("block text-lime", { "opacity-50": verifyingOTP || sendingOtp })}
                >
                    {$t("login.try_again")}
                </button>
            </p>
        </div>
        <div class="mb-6">
            {#if errors.invalid_otp}
                <p class="text-center text-red-600 mb-3">{$t("login.invalid_code")}</p>
            {/if}
            <div class="flex justify-center">
                <OneTimePasswordInput
                    bind:value={otp}
                    autofocus
                    on:complete={verifyOtp}
                    class="text-3xl"
                />
            </div>
        </div>

        <Button
            on:click={verifyOtp}
            loading={verifyingOTP}
            disabled={verifyingOTP || !isOtpValid}
            class="w-full bg-white text-primary-500 font-theme uppercase text-3xl"
        >
            {$t("login.submit")}
        </Button>
    </div>
</div>
