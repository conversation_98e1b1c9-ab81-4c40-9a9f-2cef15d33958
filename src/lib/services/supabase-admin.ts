import { createClient } from "@supabase/supabase-js";

import { env as privateEnv } from "$env/dynamic/private";
import { env as publicEnv } from "$env/dynamic/public";
import type { Database } from "$types/supabase";

function createSupabaseAdminClient() {
	const url = publicEnv.PUBLIC_SUPABASE_URL;
	const key = privateEnv.SUPABASE_SERVICE_ROLE_KEY;
	
	if (!url || !key) {
		// Return a mock client during build time when env vars aren't available
		return null as any;
	}
	
	return createClient<Database>(url, key);
}

export const supabaseAdmin = createSupabaseAdminClient();
