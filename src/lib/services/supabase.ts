import { createClient } from "@supabase/supabase-js";

import { env } from "$env/dynamic/public";
import type { Database } from "$types/supabase";

function createSupabaseClient() {
	const url = env.PUBLIC_SUPABASE_URL;
	const key = env.PUBLIC_SUPABASE_ANON_KEY;
	
	if (!url || !key) {
		// Return a mock client during build time when env vars aren't available
		return null as any;
	}
	
	return createClient<Database>(url, key);
}

export const supabase = createSupabaseClient();
