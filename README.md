# HIKO

HIKO is a SvelteKit application designed to facilitate the rental and purchase of batteries from conveniently located battery stations throughout the city. Whether you need a battery for your devices or want to contribute to sustainable energy practices, HIKO provides a seamless experience.

## Features

- **Battery Rental and Purchase**: Users can rent or buy batteries from various HIKO stations located in shops, buildings, and other accessible places.
- **Interactive Map**: A map interface allows users to easily locate nearby HIKO stations and plan their battery pickups and drop-offs.
- **Payment Management**: Users can securely manage their payment methods directly within the app for quick and easy transactions.
- **Favorites and History**: Save favorite stations for quick access and view order history to track past transactions.
- **Flexible Return Options**: After renting a battery, users can conveniently drop it off at any HIKO station across the city.

## Technologies Used

- **SvelteKit**: Frontend framework for building reactive web applications.
- **Supabase**: Backend-as-a-Service platform for managing database operations and user authentication.
- **Cloudflare**: Content Delivery Network (CDN) and DNS provider for caching and security features.
- **Twilio**: Communication API for integrating SMS and other messaging functionalities.
- **Stripe**: Payment gateway for handling secure and seamless online transactions.
- **Sentry**: Application monitoring platform for tracking errors and performance issues.
- **Google Maps**: Integration for mapping services and location-based features.

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm or yarn package manager
- Supabase account with a project created or a local Supabase instance
- External service accounts (see Environment Configuration)

### Installation

1. Clone this repository
2. Install dependencies: `npm install`
3. Configure environment variables (see below)
4. Start the development server: `npm run dev`

### Environment Configuration

Copy `.env.example` to `.env` 

```bash
cp .env.example .env
```

And configure the following services:

- **Supabase**: Database and authentication backend
- **Twilio**: SMS authentication provider
- **Stripe**: Payment processing
- **Google Maps**: Location services and mapping
- **Sentry**: Error tracking (optional)
- **PostHog**: Analytics (optional)
- **Carku**: Battery station hardware API

See [`.env.example`](.env.example) for the complete list of required environment variables and their descriptions.

### Supabase Setup

#### Local Development (Recommended)

For local development, you can run Supabase locally using Docker:

1. **Install Supabase CLI** (if not already installed):
   ```bash
   npm install -g supabase
   ```

2. **Start local Supabase instance**:
   ```bash
   npx supabase start
   ```

   This will start all Supabase services locally including:
   - PostgreSQL database
   - Auth server
   - Realtime server
   - Storage server
   - Edge Functions runtime

3. **Configure environment variables**:
   The `.env.example` is already configured for local development with `PUBLIC_SUPABASE_URL=http://localhost:54321`. Copy the anon key from the `supabase start` output to your `.env` file.

4. **Database migrations**:
   The database schema will be applied automatically when you start Supabase locally.

For more information about local development, see the [Supabase Local Development Guide](https://supabase.com/docs/guides/local-development).

#### Production Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Configure SMS authentication with Twilio in Supabase Auth settings
3. Set up authentication URLs for production
4. Update environment variables with your production Supabase URL and keys

### Development Data

For local development with realistic test data:

```bash
npm run db:seed            # Create comprehensive test data
npm run types:generate     # Generate TypeScript types
```

This creates test users, organizations, stations with Montreal locations, orders, and more. See [DATABASE_SEEDING.md](../DATABASE_SEEDING.md) for details.

### External Services

Configure the following services and add their API keys to your `.env` file:

- **Twilio**: For SMS authentication
- **Stripe**: For payment processing  
- **Google Maps**: For location services
- **Supabase**: For database and auth

Refer to [`.env.example`](.env.example) for service-specific configuration details.

## Available Scripts

### Development
- `npm run dev` - Start development server with host binding
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Run Svelte type checking and sync
- `npm run check:watch` - Run type checking in watch mode
- `npm run lint` - Run ESLint on the codebase

### Supabase
- `npx supabase start` - Start local Supabase development environment
- `npx supabase stop` - Stop local Supabase services
- `npx supabase status` - Check status of local Supabase services
- `npm run types:generate` - Generate TypeScript types from Supabase schema

### Database
- `npm run db:generate-seed` - Dump database data to seed file
- `npm run db:generate-migration` - Generate new database migration
- `npm run db:push-migration` - Push migrations to linked Supabase project
