
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for hiko-app/src/components/icon/UsbC.svelte</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">hiko-app/src/components/icon</a> UsbC.svelte</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/31</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >&lt;script lang="ts"&gt;<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >    export let width: number | string = 50;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    export let height: number | string = 50;</span>
&lt;/script&gt;
&nbsp;
<span class="cstat-no" title="statement not covered" >&lt;svg</span>
    id="Calque_2"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 50 49.34"
    {width}
    {height}
&gt;
<span class="cstat-no" title="statement not covered" >    &lt;defs&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;style&gt;.cls-1 {</span>
            fill: currentColor;
            stroke-width: 0;
        }
<span class="cstat-no" title="statement not covered" >        &lt;/style&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/defs&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;g id="Calque_1-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M40.37,15.04H9.63C4.32,15.04,0,19.36,0,24.67s4.32,9.63,9.63,9.63h30.74c5.31,0,9.63-4.32,9.63-9.63s-4.32-9.63-9.63-9.63ZM40.37,30.82H9.63c-3.39,0-6.15-2.76-6.15-6.15s2.76-6.15,6.15-6.15h30.74c3.39,0,6.15,2.76,6.15,6.15s-2.76,6.15-6.15,6.15Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M38.11,22.35H11.89c-1.28,0-2.32,1.04-2.32,2.32s1.04,2.32,2.32,2.32h26.22c1.28,0,2.32-1.04,2.32-2.32s-1.04-2.32-2.32-2.32Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M15.85,4.91c0,.98.58,1.62,1.6,1.62s1.6-.64,1.6-1.62V.13h1.6v4.94c0,1.68-1.24,2.83-3.2,2.83s-3.2-1.15-3.2-2.83V.13h1.6v4.78Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M23.45,5.6c.07.63.73,1.04,1.58,1.04s1.44-.41,1.44-.97c0-.5-.37-.77-1.29-.98l-1-.22c-1.41-.3-2.1-1.02-2.1-2.13,0-1.41,1.22-2.34,2.92-2.34s2.9.92,2.92,2.3h-1.5c-.05-.65-.62-1.04-1.42-1.04s-1.32.38-1.32.94c0,.47.37.74,1.25.94l.93.2c1.53.33,2.2.98,2.2,2.15,0,1.48-1.2,2.42-3.1,2.42s-3.01-.87-3.06-2.31h1.54Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M29.31,7.77V.13h3.37c1.44,0,2.34.74,2.34,1.91,0,.84-.62,1.54-1.44,1.66v.04c1.04.08,1.8.84,1.8,1.85,0,1.34-1.01,2.19-2.64,2.19h-3.43ZM30.91,3.31h1.2c.85,0,1.34-.38,1.34-1.02s-.43-.96-1.18-.96h-1.36v1.99ZM32.33,6.58c.92,0,1.42-.39,1.42-1.12s-.51-1.1-1.46-1.1h-1.39v2.22h1.42Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M9.15,47.21v-6.32h-2.23v-1.32h6.07v1.32h-2.24v6.32h-1.6Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M13.84,49.32v-1.2c.05.01.29.02.35.02.58,0,.91-.2,1.03-.67l.05-.22-2.04-5.87h1.69l1.26,4.58h.03l1.27-4.58h1.63l-2.02,5.95c-.5,1.49-1.21,2.01-2.67,2.01-.06,0-.53-.01-.59-.02Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M25.73,44.29c0,1.85-.91,2.99-2.37,2.99-.84,0-1.49-.39-1.81-1.05h-.03v2.81h-1.55v-7.66h1.53v1h.03c.31-.66.99-1.06,1.81-1.06,1.48,0,2.39,1.13,2.39,2.98ZM24.15,44.29c0-1.05-.52-1.73-1.32-1.73s-1.31.69-1.31,1.74.52,1.73,1.31,1.73,1.32-.68,1.32-1.74Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M32.12,45.43c-.17,1.13-1.22,1.88-2.64,1.88-1.77,0-2.86-1.14-2.86-2.99s1.09-3.04,2.8-3.04,2.75,1.12,2.75,2.9v.48h-4.02v.1c0,.84.55,1.41,1.36,1.41.58,0,1.04-.28,1.19-.73h1.42ZM28.16,43.69h2.52c-.03-.76-.52-1.26-1.24-1.26s-1.22.51-1.28,1.26Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;path</span>
            class="cls-1"
<span class="cstat-no" title="statement not covered" >            d="M35.74,43.39c0-2.46,1.4-3.95,3.62-3.95,1.84,0,3.24,1.16,3.36,2.88h-1.56c-.15-.94-.87-1.55-1.8-1.55-1.21,0-1.99,1.01-1.99,2.62s.77,2.64,1.99,2.64c.95,0,1.63-.56,1.8-1.46h1.56c-.18,1.71-1.49,2.79-3.36,2.79-2.22,0-3.62-1.49-3.62-3.96Z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/g&gt;</span>
<span class="cstat-no" title="statement not covered" >&lt;/svg&gt;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-21T17:18:43.713Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    