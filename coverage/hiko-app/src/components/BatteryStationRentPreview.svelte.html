
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for hiko-app/src/components/BatteryStationRentPreview.svelte</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">hiko-app/src/components</a> BatteryStationRentPreview.svelte</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/365</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/365</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >&lt;script lang="ts"&gt;</span></span></span>
<span class="cstat-no" title="statement not covered" >    import type { PaymentMethod } from "@stripe/stripe-js";</span>
<span class="cstat-no" title="statement not covered" >    import PostHog from "posthog-js";</span>
<span class="cstat-no" title="statement not covered" >    import Stripe from "stripe";</span>
<span class="cstat-no" title="statement not covered" >    import { createEventDispatcher, onMount } from "svelte";</span>
<span class="cstat-no" title="statement not covered" >    import { derived } from "svelte/store";</span>
<span class="cstat-no" title="statement not covered" >    import { fly } from "svelte/transition";</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    import AntDesignAndroidFilled from "~icons/ant-design/android-filled";</span>
<span class="cstat-no" title="statement not covered" >    import AntDesignAppleFilled from "~icons/ant-design/apple-filled";</span>
<span class="cstat-no" title="statement not covered" >    import SolarTicketSaleBold from "~icons/solar/ticket-sale-bold";</span>
<span class="cstat-no" title="statement not covered" >    import BiStripe from "~icons/bi/stripe";</span>
<span class="cstat-no" title="statement not covered" >    import CreditCardItem from "$components/CreditCardItem.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import UsbC from "$components/icon/UsbC.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import PaymentMethodSelectorDialog from "$components/PaymentMethodSelectorDialog.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import Button from "$components/ui/Button.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import Card from "$components/ui/Card.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import ErrorDialog from "$components/ui/ErrorDialog.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import SkeletonLoader from "$components/ui/SkeletonLoader.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import Tabs from "$components/ui/Tabs.svelte";</span>
<span class="cstat-no" title="statement not covered" >    import { useLocalStorage } from "$lib/composables/useLocalStorage";</span>
<span class="cstat-no" title="statement not covered" >    import { t } from "$lib/i18n";</span>
<span class="cstat-no" title="statement not covered" >    import { api } from "$lib/services/api";</span>
<span class="cstat-no" title="statement not covered" >    import { Sentry } from "$lib/services/sentry";</span>
<span class="cstat-no" title="statement not covered" >    import { getBatteryStationSlotId } from "$lib/stores/order";</span>
<span class="cstat-no" title="statement not covered" >    import { isStationRentPreviewVisible, station } from "$lib/stores/stations";</span>
<span class="cstat-no" title="statement not covered" >    import { BATTERY_PURCHASE_PRICE, getHourlyPriceCents, MAX_CHARGEABLE_HOURS_PER_DAY, MAX_DAYS_TO_KEEP } from "$lib/utils/pricing";</span>
<span class="cstat-no" title="statement not covered" >    import { getPromoLabel, loadSavedPromoCode } from "$lib/utils/promo-codes.client";</span>
<span class="cstat-no" title="statement not covered" >    import { cn } from "$lib/utils/style";</span>
<span class="cstat-no" title="statement not covered" >    import { BatteryCableType } from "$types/carku";</span>
<span class="cstat-no" title="statement not covered" >    import { PostHogEvent } from "$types/posthog";</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    enum OrderTypeTabIndex {</span>
<span class="cstat-no" title="statement not covered" >        Rental = 0,</span>
<span class="cstat-no" title="statement not covered" >        Purchase = 1,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    type Events = {</span>
<span class="cstat-no" title="statement not covered" >        rent: number</span>
<span class="cstat-no" title="statement not covered" >        purchase: number</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const cableType = useLocalStorage&lt;BatteryCableType&gt;("cableType", BatteryCableType.USBTypeC);</span>
<span class="cstat-no" title="statement not covered" >    let paymentMethod: PaymentMethod | null = null;</span>
<span class="cstat-no" title="statement not covered" >    let isRenting = false;</span>
<span class="cstat-no" title="statement not covered" >    let isPurchasing = false;</span>
<span class="cstat-no" title="statement not covered" >    let isLoadingPaymentMethod = true;</span>
<span class="cstat-no" title="statement not covered" >    let showPaymentMethodSelector = false;</span>
<span class="cstat-no" title="statement not covered" >    let showErrorDialog = false;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    let errors = {</span>
<span class="cstat-no" title="statement not covered" >        missing_cable: false,</span>
<span class="cstat-no" title="statement not covered" >        missing_payment_method: false,</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const cableOptions = [</span>
<span class="cstat-no" title="statement not covered" >        {</span>
<span class="cstat-no" title="statement not covered" >            type: BatteryCableType.Lightning,</span>
<span class="cstat-no" title="statement not covered" >            icon: AntDesignAppleFilled,</span>
<span class="cstat-no" title="statement not covered" >            label: "Apple",</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        {</span>
<span class="cstat-no" title="statement not covered" >            type: BatteryCableType.MicroUSB,</span>
<span class="cstat-no" title="statement not covered" >            icon: AntDesignAndroidFilled,</span>
<span class="cstat-no" title="statement not covered" >            label: "Android",</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        {</span>
<span class="cstat-no" title="statement not covered" >            type: BatteryCableType.USBTypeC,</span>
<span class="cstat-no" title="statement not covered" >            icon: UsbC,</span>
<span class="cstat-no" title="statement not covered" >            label: "Type-C",</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    let activeTab = OrderTypeTabIndex.Rental;</span>
<span class="cstat-no" title="statement not covered" >    const tabItems = [</span>
<span class="cstat-no" title="statement not covered" >        $t("home.rental"),</span>
<span class="cstat-no" title="statement not covered" >        $t("home.purchase"),</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
<span class="cstat-no" title="statement not covered" >    const hourlyPrice = derived(station, (value) =&gt; getHourlyPriceCents(value) / 100);</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const dispatch = createEventDispatcher&lt;Events&gt;();</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const selectCableType = (type: BatteryCableType) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        cableType.set(type);</span>
<span class="cstat-no" title="statement not covered" >        errors.missing_cable = false;</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const selectPaymentMethod = (event: CustomEvent&lt;PaymentMethod&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        paymentMethod = event.detail;</span>
<span class="cstat-no" title="statement not covered" >        errors.missing_payment_method = false;</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const onClickAction = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (isLoadingPaymentMethod || isRenting || isPurchasing) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        if (!$cableType) {</span>
<span class="cstat-no" title="statement not covered" >            errors.missing_cable = true;</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        if (!paymentMethod) {</span>
<span class="cstat-no" title="statement not covered" >            errors.missing_payment_method = true;</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        if (activeTab === OrderTypeTabIndex.Rental) {</span>
<span class="cstat-no" title="statement not covered" >            await rentBattery();</span>
<span class="cstat-no" title="statement not covered" >        } else if (activeTab === OrderTypeTabIndex.Purchase) {</span>
<span class="cstat-no" title="statement not covered" >            await purchaseBattery();</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const rentBattery = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!paymentMethod) {</span>
<span class="cstat-no" title="statement not covered" >            Sentry.captureMessage("Payment method is missing for rental");</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            isRenting = true;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            const slotIdPromise = getBatteryStationSlotId();</span>
<span class="cstat-no" title="statement not covered" >            await api.post("/payments/orders/rent", {</span>
<span class="cstat-no" title="statement not covered" >                station_id: $station.id,</span>
<span class="cstat-no" title="statement not covered" >                payment_method_id: paymentMethod.id,</span>
<span class="cstat-no" title="statement not covered" >                cable: $cableType,</span>
<span class="cstat-no" title="statement not covered" >                promo_code: promoCode?.code,</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            let slotId = -1;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            try {</span>
<span class="cstat-no" title="statement not covered" >                slotId = await slotIdPromise;</span>
<span class="cstat-no" title="statement not covered" >                dispatch("rent", slotId);</span>
<span class="cstat-no" title="statement not covered" >            } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >                Sentry.captureException(error);</span>
<span class="cstat-no" title="statement not covered" >                window.location.reload();</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            PostHog.capture(PostHogEvent.OrderRental, {</span>
<span class="cstat-no" title="statement not covered" >                station_id: $station.id,</span>
<span class="cstat-no" title="statement not covered" >                payment_method_id: paymentMethod.id,</span>
<span class="cstat-no" title="statement not covered" >                cable: $cableType,</span>
<span class="cstat-no" title="statement not covered" >                slot_id: slotId,</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            isStationRentPreviewVisible.set(false);</span>
<span class="cstat-no" title="statement not covered" >        } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            Sentry.captureException(error);</span>
<span class="cstat-no" title="statement not covered" >            showErrorDialog = true;</span>
<span class="cstat-no" title="statement not covered" >        } finally {</span>
<span class="cstat-no" title="statement not covered" >            isRenting = false;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const purchaseBattery = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!paymentMethod) {</span>
<span class="cstat-no" title="statement not covered" >            Sentry.captureException("Payment method is missing for purchase");</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            isPurchasing = true;</span>
<span class="cstat-no" title="statement not covered" >            await api.post("/payments/orders/purchase", {</span>
<span class="cstat-no" title="statement not covered" >                station_id: $station.id,</span>
<span class="cstat-no" title="statement not covered" >                payment_method_id: paymentMethod.id,</span>
<span class="cstat-no" title="statement not covered" >                cable: $cableType,</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            let slotId = -1;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            try {</span>
<span class="cstat-no" title="statement not covered" >                slotId = await getBatteryStationSlotId();</span>
<span class="cstat-no" title="statement not covered" >                dispatch("purchase", slotId);</span>
<span class="cstat-no" title="statement not covered" >            } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >                Sentry.captureException(error);</span>
<span class="cstat-no" title="statement not covered" >                window.location.reload();</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            PostHog.capture(PostHogEvent.OrderPurchase, {</span>
<span class="cstat-no" title="statement not covered" >                station_id: $station.id,</span>
<span class="cstat-no" title="statement not covered" >                payment_method_id: paymentMethod.id,</span>
<span class="cstat-no" title="statement not covered" >                cable: $cableType,</span>
<span class="cstat-no" title="statement not covered" >                slot_id: slotId,</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            isStationRentPreviewVisible.set(false);</span>
<span class="cstat-no" title="statement not covered" >        } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            Sentry.captureException(error);</span>
<span class="cstat-no" title="statement not covered" >            showErrorDialog = true;</span>
<span class="cstat-no" title="statement not covered" >        } finally {</span>
<span class="cstat-no" title="statement not covered" >            isPurchasing = false;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const loadDefaultPaymentMethod = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            isLoadingPaymentMethod = true;</span>
<span class="cstat-no" title="statement not covered" >            const { data } = await api.get&lt;PaymentMethod&gt;("/payments/methods/default");</span>
<span class="cstat-no" title="statement not covered" >            paymentMethod = data;</span>
<span class="cstat-no" title="statement not covered" >        } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            Sentry.captureException(error);</span>
<span class="cstat-no" title="statement not covered" >        } finally {</span>
<span class="cstat-no" title="statement not covered" >            isLoadingPaymentMethod = false;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    let promoCode: Stripe.PromotionCode | null = null;</span>
<span class="cstat-no" title="statement not covered" >    $: promoLabel = promoCode ? getPromoLabel(promoCode) : "";</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const _loadSavedPromoCode = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        promoCode = await loadSavedPromoCode();</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    onMount(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        loadDefaultPaymentMethod();</span>
<span class="cstat-no" title="statement not covered" >        _loadSavedPromoCode();</span>
<span class="cstat-no" title="statement not covered" >    });</span>
<span class="cstat-no" title="statement not covered" >&lt;/script&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >&lt;aside</span>
<span class="cstat-no" title="statement not covered" >    transition:fly={{ duration: 300, y: 300 }}</span>
<span class="cstat-no" title="statement not covered" >    class="fixed z-[1] bottom-0 left-0 w-full pointer-events-none"</span>
<span class="cstat-no" title="statement not covered" >&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div class="grid grid-cols-1 gap-3 w-full max-w-app mx-auto px-3 pb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Card class="pointer-events-auto px-0 pb-0 pt-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {#if false}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="bg-slate-100 rounded-t-2xl mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Tabs</span>
<span class="cstat-no" title="statement not covered" >                        bind:value={activeTab}</span>
<span class="cstat-no" title="statement not covered" >                        items={tabItems}</span>
<span class="cstat-no" title="statement not covered" >                        class={cn({</span>
<span class="cstat-no" title="statement not covered" >                            "pointer-events-none": isRenting || isPurchasing,</span>
<span class="cstat-no" title="statement not covered" >                        })}</span>
<span class="cstat-no" title="statement not covered" >                    /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            {/if}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div class="px-5 pb-5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p class="font-bold"&gt;{$t("home.price")}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {#if activeTab === OrderTypeTabIndex.Rental}</span>
<span class="cstat-no" title="statement not covered" >                        {#if $hourlyPrice &gt; 0}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div class="text-right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;p class="text-lg font-medium leading-snug"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;span class="-mr-1"&gt;${$hourlyPrice}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;span class="font-normal opacity-50 text-xs"&gt;/{$t("home.hour")}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                {#if promoCode}</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;p class="text-xs font-medium text-primary-500 flex items-center gap-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;SolarTicketSaleBold class="size-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;span&gt;{promoLabel} {$t("home.on_total")}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                {/if}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {:else}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div class="text-lg font-medium text-primary-500 leading-snug"&gt;{$t("common.free")}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {/if}</span>
<span class="cstat-no" title="statement not covered" >                    {:else if activeTab === OrderTypeTabIndex.Purchase}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span class="text-lg font-medium leading-snug"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            ${BATTERY_PURCHASE_PRICE}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {/if}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                {#if false}</span>
<span class="cstat-no" title="statement not covered" >                &lt;hr class="my-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div class="mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p class="font-bold"&gt;{$t("home.cable")}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {#if errors.missing_cable}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;p class="text-xs text-red-500"&gt;{$t("home.error_cable_type")}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {/if}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                        &lt;div class="flex items-center justify-between md:justify-evenly gap-3 place-items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {#each cableOptions as cableOption}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;button</span>
<span class="cstat-no" title="statement not covered" >                                        on:click={() =&gt; selectCableType(cableOption.type)}</span>
<span class="cstat-no" title="statement not covered" >                                        class={cn("border rounded-2xl aspect-square mb-1 p-3", {</span>
<span class="cstat-no" title="statement not covered" >                                            "bg-primary-500 text-white border-primary-500": $cableType === cableOption.type,</span>
<span class="cstat-no" title="statement not covered" >                                            "border-red-500": errors.missing_cable,</span>
<span class="cstat-no" title="statement not covered" >                                        })}</span>
<span class="cstat-no" title="statement not covered" >                                    &gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;svelte:component</span>
<span class="cstat-no" title="statement not covered" >                                            this={cableOption.icon}</span>
<span class="cstat-no" title="statement not covered" >                                            width="50"</span>
<span class="cstat-no" title="statement not covered" >                                            height="50"</span>
<span class="cstat-no" title="statement not covered" >                                        /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {/each}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                {/if}</span>
<span class="cstat-no" title="statement not covered" >                &lt;hr class="my-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div class="mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p class="font-bold"&gt;{$t("home.payment")}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p class="text-xs text-gray-500 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;strong&gt;{$t("home.deposit")}:&lt;/strong&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;span&gt;{$t("home.deposit_info", { deposit: BATTERY_PURCHASE_PRICE })}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p class="text-xs text-gray-500 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;strong&gt;{$t("home.hourly_rate")}&lt;/strong&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;span&gt;{$t("home.hourly_rate_info")}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p class="text-xs text-gray-500 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;strong&gt;{$t("home.battery_return")}:&lt;/strong&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;span&gt;{$t("home.battery_return_info", { hours: MAX_DAYS_TO_KEEP * 24 })}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {#if errors.missing_payment_method}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p class="text-xs text-red-500 mt-1"&gt;{$t("home.error_payment_method")}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {/if}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                    {#if isLoadingPaymentMethod}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;SkeletonLoader class="w-full h-[70px]" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {:else if paymentMethod &amp;&amp; paymentMethod.card}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;button</span>
<span class="cstat-no" title="statement not covered" >                            on:click={() =&gt; showPaymentMethodSelector = true}</span>
<span class="cstat-no" title="statement not covered" >                            class={cn("w-full text-left", {</span>
<span class="cstat-no" title="statement not covered" >                                "pointer-events-none": isRenting || isPurchasing,</span>
<span class="cstat-no" title="statement not covered" >                            })}</span>
<span class="cstat-no" title="statement not covered" >                        &gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;CreditCardItem</span>
<span class="cstat-no" title="statement not covered" >                                card={paymentMethod.card}</span>
<span class="cstat-no" title="statement not covered" >                                class="shadow-none border border-black/10"</span>
<span class="cstat-no" title="statement not covered" >                            /&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {:else}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;button</span>
<span class="cstat-no" title="statement not covered" >                            on:click={() =&gt; showPaymentMethodSelector = true}</span>
<span class="cstat-no" title="statement not covered" >                            class={cn("border border-dashed border-black/10 rounded-2xl h-[70px] flex items-center justify-center w-full gap-2 px-3", {</span>
<span class="cstat-no" title="statement not covered" >                                "border-red-500": errors.missing_payment_method,</span>
<span class="cstat-no" title="statement not covered" >                            })}</span>
<span class="cstat-no" title="statement not covered" >                        &gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;BiStripe class="shrink-0 text-indigo-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;span class="truncate"&gt;{$t("home.select_payment_method")}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {/if}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Card&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Button</span>
<span class="cstat-no" title="statement not covered" >            on:click={onClickAction}</span>
<span class="cstat-no" title="statement not covered" >            loading={isRenting || isPurchasing}</span>
<span class="cstat-no" title="statement not covered" >            class="font-theme uppercase text-3xl w-full pointer-events-auto"</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
<span class="cstat-no" title="statement not covered" >            {#if activeTab === OrderTypeTabIndex.Rental}</span>
<span class="cstat-no" title="statement not covered" >                {$t("home.rent")}</span>
<span class="cstat-no" title="statement not covered" >            {:else if activeTab === OrderTypeTabIndex.Purchase}</span>
<span class="cstat-no" title="statement not covered" >                {$t("home.purchase")}</span>
<span class="cstat-no" title="statement not covered" >            {/if}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >&lt;/aside&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >&lt;PaymentMethodSelectorDialog</span>
<span class="cstat-no" title="statement not covered" >    bind:visible={showPaymentMethodSelector}</span>
<span class="cstat-no" title="statement not covered" >    on:select={selectPaymentMethod}</span>
<span class="cstat-no" title="statement not covered" >/&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >&lt;ErrorDialog bind:visible={showErrorDialog} /&gt;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-21T17:18:43.713Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    