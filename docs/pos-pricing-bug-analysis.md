# POS $2 Pricing Bug - Root Cause Analysis

## **Problem Statement**
POS transactions are always charged $2 regardless of station-specific pricing customizations, while web app transactions correctly use the configured station rates.

## **Root Cause**
The POS order creation flow in `src/lib/events/stripe/collect_paymentMethod_success.ts` **does not set the `hourly_price_cents` field** when creating orders, causing the pricing system to fall back to the hardcoded default of $2/hour.

## **Technical Analysis**

### **Bug Location**
**File:** `src/lib/events/stripe/collect_paymentMethod_success.ts` (Lines 18-28)

**Problem:** Missing `hourly_price_cents` in order insertion:
```typescript
const { error } = await supabaseAdmin
    .from("orders")
    .insert({
        station_id: payload.did,
        stripe_payment_intent_id: paymentIntent.id,
        carku_order_id: orderId,
        cable_type: BatteryCableType.USBTypeC,
        type: OrderType.Rental,
        status: OrderStatus.Pending,
        amount_charged_cents: 0,
        // ❌ MISSING: hourly_price_cents: station.hourly_price_cents
    });
```

### **Comparison with Working Code**
**File:** `src/lib/utils/orders.ts` (Lines 72-86) - Web App (Correct)
```typescript
const { error: orderError } = await supabaseAdmin
    .from("orders")
    .insert({
        user_id: user.id,
        station_id: stationId,
        stripe_customer_id: stripeCustomerId,
        stripe_payment_intent_id: intent.id,
        carku_order_id: orderId,
        cable_type: cable,
        type: orderType,
        status: OrderStatus.Pending,
        amount_charged_cents: 0,
        promo_code: promoCode,
        hourly_price_cents: station.hourly_price_cents, // ✅ CORRECT
    });
```

### **Fallback Logic**
**File:** `src/lib/utils/pricing.ts` (Lines 80-82)
```typescript
export const getHourlyPriceCents = (value: Tables<"stations"> | Tables<"orders">) => {
    return value?.hourly_price_cents ?? DEFAULT_BATTERY_HOURLY_RENTAL_PRICE * 100;
};
```

When `hourly_price_cents` is null/undefined, it defaults to `DEFAULT_BATTERY_HOURLY_RENTAL_PRICE * 100 = 200` cents ($2).

## **Impact Assessment**

### **Financial Impact**
Based on test results for a 2-hour rental:
- **Station Rate:** $5/hour → **Expected:** $10.00
- **Default Rate:** $2/hour → **Actual:** $4.00
- **Lost Revenue:** $6.00 per transaction (60% revenue loss)

### **Affected Transactions**
- ✅ **Web App:** Correctly uses station-specific pricing
- ❌ **POS Terminal:** Always uses $2/hour default rate
- ❌ **All POS transactions since implementation**

## **Solution**

### **Required Fix**
Modify `src/lib/events/stripe/collect_paymentMethod_success.ts` to:

1. **Retrieve station data** (already available in parent function)
2. **Pass station data** to `onCollectPaymentMethodSuccess`
3. **Include `hourly_price_cents`** in order creation

### **Implementation Steps**

1. **Update function signature:**
```typescript
export const onCollectPaymentMethodSuccess = async (
    payload: StripePayload, 
    station: Tables<"stations">
) => {
    // ... existing code ...
    
    const { error } = await supabaseAdmin
        .from("orders")
        .insert({
            station_id: payload.did,
            stripe_payment_intent_id: paymentIntent.id,
            carku_order_id: orderId,
            cable_type: BatteryCableType.USBTypeC,
            type: OrderType.Rental,
            status: OrderStatus.Pending,
            amount_charged_cents: 0,
            hourly_price_cents: station.hourly_price_cents, // ✅ FIX
        });
}
```

2. **Update caller in `src/routes/api/callback/stripe/+server.ts`:**
```typescript
if (ACT === StripeEvent.CollectPaymentMethodSuccess) {
    const { intentId, orderId } = await onCollectPaymentMethodSuccess(payload, station);
    // ... rest of the code
}
```

## **Testing**

### **Unit Tests Created**
- ✅ `src/tests/pos-pricing-bug.test.ts` - Reproduces the bug
- ✅ Demonstrates financial impact
- ✅ Shows difference between POS and web app flows

### **Test Results**
```
✓ POS orders missing hourly_price_cents (demonstrates bug)
✓ Web app orders correctly include hourly_price_cents  
✓ Financial impact: $6 lost revenue per 2-hour rental
```

## **Verification Steps**

After implementing the fix:

1. **Run existing tests:** Ensure no regressions
2. **Test POS flow:** Verify station-specific pricing is used
3. **Compare with web app:** Ensure both flows produce same pricing
4. **Monitor production:** Track revenue recovery

## **Priority**
**HIGH** - Direct revenue impact on all POS transactions

## **Files to Modify**
1. `src/lib/events/stripe/collect_paymentMethod_success.ts`
2. `src/routes/api/callback/stripe/+server.ts`
3. Update tests to verify fix

## **Related Issues**
- Station customizations not working for POS
- Revenue discrepancies between POS and web app
- Hardcoded pricing constants being used inappropriately
